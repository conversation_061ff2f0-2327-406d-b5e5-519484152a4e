import pandas as pd

# Read the CSV file
csv_file = "aws-log.csv"  # Replace with your file path if different
df = pd.read_csv(csv_file)

# Extract unique keys from the 'key' column
unique_keys = df['key'].unique().tolist()

# Base SQL query templates
query1_template = """SELECT i.instruction_ref_id, t2.*, t2.transaction_flow, t2.created_date_time 
FROM transaction.transactions t2  
JOIN transaction.instructions i ON t2.instruction_id = i.id 
WHERE i.instruction_ref_id IN ({keys});
"""

query2_template = """SELECT SUM(t2.amount), t2.profile_ref_id, t2.account_ref_id  
FROM transaction.transactions t2  
JOIN transaction.instructions i ON t2.instruction_id = i.id 
WHERE i.instruction_ref_id IN ({keys})
GROUP BY t2.profile_ref_id, t2.account_ref_id;
"""

query3_template = """SELECT * 
FROM "transaction".payments p 
WHERE p.external_ref_id IN ({keys});
"""

query4_template = """SELECT SUM(p.amount), p.service_account_ref_id  
FROM "transaction".payments p
WHERE p.external_ref_id IN ({keys})
GROUP BY p.service_account_ref_id;
"""

# Format the keys as a SQL-compatible string (e.g., "'key1', 'key2', ...")
keys_str = ", ".join(f"'{key}'" for key in unique_keys)

# Generate the full queries
queries = {
    "tol-detail.sql": query1_template.format(keys=keys_str),
    "tol-symmary.sql": query2_template.format(keys=keys_str),
    "etransfer-detail.sql": query3_template.format(keys=keys_str),
    "etransfer-summary.sql": query4_template.format(keys=keys_str),
}

# Save each query to a file
for filename, query in queries.items():
    with open(filename, 'w') as f:
        f.write(query)
    print(f"Saved {filename}")

print(f"Generated queries with {len(unique_keys)} unique keys.")