@timestamp,log,key,topic,message
2025-04-09 12:41:53.986,"2025-04-09 08:41:53,986 INFO  [DEFAULT] [00000000-0000-0000-0000-000000000000]-[asyncTransactionListener-0-C-1] ListenerRetryHandler - Moving key = PSfj638gseZrSNzk to topic = MANUAL_INTERVENTION_TRANSACTIONS message = 400 : ""{""error"":{""code"":""INVALID_STATUS_TRANSITION"",""additional_information"":""Transaction status is not PENDING""}}"" record = ConsumerRecord(topic = PENDING_TRANSACTIONS_V2, partition = 1, leaderEpoch = 0, offset = 1807, CreateTime = *************, serialized key size = 16, serialized value size = -1, headers = RecordHeaders(headers = [RecordHeader(key = x-pg-profile-id, value = [48, 100, 97, 50, 98, 57, 100, 57, 45, 98, 51, 56, 52, 45, 52, 101, 100, 51, 45, 98, 55, 51, 49, 45, 48, 99, 102, 56, 57, 56, 98, 100, 98, 52, 53, 54]), RecordHeader(key = x-pg-account-id, value = [50, 56, 51, 56, 49, 57, 48, 99, 45, 101, 97, 97, 48, 45, 52, 56, 54, 98, 45, 98, 97, 48, 98, 45, 49, 52, 98, 97, 48, 55, 54, 101, 97, 102, 100, 97]), RecordHeader(key = x-pg-interaction-id, value = [52, 100, 48, 101, 101, 100, 48, 48, 45, 57, 98, 49, 55, 45, 52, 54, 101, 102, 45, 56, 51, 57, 57, 45, 51, 53, 49, 57, 54, 54, 50, 52, 57, 54, 99, 53]), RecordHeader(key = x-pg-instruction-ref-id, value = [80, 83, 102, 106, 54, 51, 56, 103, 115, 101, 90, 114, 83, 78, 122, 107]), RecordHeader(key = x-pg-operation-id, value = [67, 79, 77, 77, 73, 84]), RecordHeader(key = x-pg-kafka-key, value = [67, 79, 77, 77, 124, 80, 83, 102, 106, 54, 51, 56, 103, 115, 101, 90, 114, 83, 78, 122, 107]), RecordHeader(key = x-datadog-trace-id, value = [49, 50, 49, 48, 48, 49, 49, 48, 57, 49, 56, 49, 51, 50, 48, 54, 53, 51, 56]), RecordHeader(key = x-datadog-parent-id, value = [50, 48, 48, 57, 56, 56, 55, 53, 49, 52, 56, 51, 54, 52, 57, 52, 56, 54, 52]), RecordHeader(key = x-datadog-sampling-priority, value = [49]), RecordHeader(key = x-datadog-tags, value = [95, 100, 100, 46, 112, 46, 100, 109, 61, 45, 49, 44, 95, 100, 100, 46, 112, 46, 116, 105, 100, 61, 54, 55, 102, 54, 53, 56, 54, 53, 48, 48, 48, 48, 48, 48, 48, 48]), RecordHeader(key = traceparent, value = [48, 48, 45, 54, 55, 102, 54, 53, 56, 54, 53, 48, 48, 48, 48, 48, 48, 48, 48, 49, 48, 99, 97, 100, 50, 97, 100, 53, 53, 99, 102, 48, 97, 48, 97, 45, 49, 98, 101, 52, 56, 101, 48, 98, 99, 48, 53, 50, 54, 97, 49, 48, 45, 48, 49]), RecordHeader(key = tracestate, value = [100, 100, 61, 115, 58, 49, 59, 112, 58, 49, 98, 101, 52, 56, 101, 48, 98, 99, 48, 53, 50, 54, 97, 49, 48, 59, 116, 46, 100, 109, 58, 45, 49, 59, 116, 46, 116, 105, 100, 58, 54, 55, 102, 54, 53, 56, 54, 53, 48, 48, 48, 48, 48, 48, 48, 48])], isReadOnly = false), key = PSfj638gseZrSNzk, value = null)",PSfj638gseZrSNzk,MANUAL_INTERVENTION_TRANSACTIONS,"400 : ""{""error"":{""code"":""INVALID_STATUS_TRANSITION"",""additional_information"":""Transaction status is not PENDING""}}"" record = ConsumerRecord(topic = PENDING_TRANSACTIONS_V2, partition = 1, leaderEpoch = 0, offset = 1807, CreateTime = *************, serialized key size = 16, serialized value size = -1, headers = RecordHeaders(headers = [RecordHeader(key = x-pg-profile-id, value = [48, 100, 97, 50, 98, 57, 100, 57, 45, 98, 51, 56, 52, 45, 52, 101, 100, 51, 45, 98, 55, 51, 49, 45, 48, 99, 102, 56, 57, 56, 98, 100, 98, 52, 53, 54]), RecordHeader(key = x-pg-account-id, value = [50, 56, 51, 56, 49, 57, 48, 99, 45, 101, 97, 97, 48, 45, 52, 56, 54, 98, 45, 98, 97, 48, 98, 45, 49, 52, 98, 97, 48, 55, 54, 101, 97, 102, 100, 97]), RecordHeader(key = x-pg-interaction-id, value = [52, 100, 48, 101, 101, 100, 48, 48, 45, 57, 98, 49, 55, 45, 52, 54, 101, 102, 45, 56, 51, 57, 57, 45, 51, 53, 49, 57, 54, 54, 50, 52, 57, 54, 99, 53]), RecordHeader(key = x-pg-instruction-ref-id, value = [80, 83, 102, 106, 54, 51, 56, 103, 115, 101, 90, 114, 83, 78, 122, 107]), RecordHeader(key = x-pg-operation-id, value = [67, 79, 77, 77, 73, 84]), RecordHeader(key = x-pg-kafka-key, value = [67, 79, 77, 77, 124, 80, 83, 102, 106, 54, 51, 56, 103, 115, 101, 90, 114, 83, 78, 122, 107]), RecordHeader(key = x-datadog-trace-id, value = [49, 50, 49, 48, 48, 49, 49, 48, 57, 49, 56, 49, 51, 50, 48, 54, 53, 51, 56]), RecordHeader(key = x-datadog-parent-id, value = [50, 48, 48, 57, 56, 56, 55, 53, 49, 52, 56, 51, 54, 52, 57, 52, 56, 54, 52]), RecordHeader(key = x-datadog-sampling-priority, value = [49]), RecordHeader(key = x-datadog-tags, value = [95, 100, 100, 46, 112, 46, 100, 109, 61, 45, 49, 44, 95, 100, 100, 46, 112, 46, 116, 105, 100, 61, 54, 55, 102, 54, 53, 56, 54, 53, 48, 48, 48, 48, 48, 48, 48, 48]), RecordHeader(key = traceparent, value = [48, 48, 45, 54, 55, 102, 54, 53, 56, 54, 53, 48, 48, 48, 48, 48, 48, 48, 48, 49, 48, 99, 97, 100, 50, 97, 100, 53, 53, 99, 102, 48, 97, 48, 97, 45, 49, 98, 101, 52, 56, 101, 48, 98, 99, 48, 53, 50, 54, 97, 49, 48, 45, 48, 49]), RecordHeader(key = tracestate, value = [100, 100, 61, 115, 58, 49, 59, 112, 58, 49, 98, 101, 52, 56, 101, 48, 98, 99, 48, 53, 50, 54, 97, 49, 48, 59, 116, 46, 100, 109, 58, 45, 49, 59, 116, 46, 116, 105, 100, 58, 54, 55, 102, 54, 53, 56, 54, 53, 48, 48, 48, 48, 48, 48, 48, 48])], isReadOnly = false), key = PSfj638gseZrSNzk, value = null)"
2025-04-09 12:41:53.982,"2025-04-09 08:41:53,982 INFO  [DEFAULT] [00000000-0000-0000-0000-000000000000]-[asyncTransactionListener-0-C-1] ListenerRetryHandler - Moving key = zhfj638gqiZmtkKH to topic = MANUAL_INTERVENTION_TRANSACTIONS message = 400 : ""{""error"":{""code"":""INVALID_STATUS_TRANSITION"",""additional_information"":""Transaction status is not PENDING""}}"" record = ConsumerRecord(topic = PENDING_TRANSACTIONS_V2, partition = 1, leaderEpoch = 0, offset = 1806, CreateTime = *************, serialized key size = 16, serialized value size = -1, headers = RecordHeaders(headers = [RecordHeader(key = x-pg-profile-id, value = [56, 51, 54, 97, 51, 48, 57, 48, 45, 99, 97, 50, 97, 45, 52, 54, 55, 98, 45, 98, 50, 97, 101, 45, 55, 99, 52, 54, 97, 100, 102, 97, 51, 97, 102, 50]), RecordHeader(key = x-pg-account-id, value = [101, 53, 54, 100, 48, 100, 52, 102, 45, 101, 56, 98, 101, 45, 52, 52, 98, 100, 45, 57, 53, 98, 99, 45, 56, 56, 50, 57, 56, 53, 57, 98, 51, 48, 57, 99]), RecordHeader(key = x-pg-interaction-id, value = [57, 50, 56, 52, 102, 101, 55, 52, 45, 57, 98, 49, 50, 45, 52, 56, 54, 102, 45, 98, 100, 50, 48, 45, 53, 52, 102, 101, 51, 48, 102, 50, 48, 100, 49, 50]), RecordHeader(key = x-pg-instruction-ref-id, value = [122, 104, 102, 106, 54, 51, 56, 103, 113, 105, 90, 109, 116, 107, 75, 72]), RecordHeader(key = x-pg-operation-id, value = [67, 79, 77, 77, 73, 84]), RecordHeader(key = x-pg-kafka-key, value = [67, 79, 77, 77, 124, 122, 104, 102, 106, 54, 51, 56, 103, 113, 105, 90, 109, 116, 107, 75, 72]), RecordHeader(key = x-datadog-trace-id, value = [50, 56, 50, 52, 50, 54, 55, 51, 48, 50, 49, 49, 49, 51, 50, 53, 51, 51, 49]), RecordHeader(key = x-datadog-parent-id, value = [50, 54, 55, 51, 56, 52, 50, 52, 49, 55, 52, 51, 53, 51, 54, 53, 57, 53, 51]), RecordHeader(key = x-datadog-sampling-priority, value = [49]), RecordHeader(key = x-datadog-tags, value = [95, 100, 100, 46, 112, 46, 100, 109, 61, 45, 49, 44, 95, 100, 100, 46, 112, 46, 116, 105, 100, 61, 54, 55, 102, 54, 53, 56, 53, 55, 48, 48, 48, 48, 48, 48, 48, 48]), RecordHeader(key = traceparent, value = [48, 48, 45, 54, 55, 102, 54, 53, 56, 53, 55, 48, 48, 48, 48, 48, 48, 48, 48, 50, 55, 51, 49, 100, 48, 50, 53, 99, 100, 49, 56, 97, 99, 57, 51, 45, 50, 53, 49, 98, 54, 53, 56, 51, 49, 52, 57, 98, 53, 50, 52, 49, 45, 48, 49]), RecordHeader(key = tracestate, value = [100, 100, 61, 115, 58, 49, 59, 112, 58, 50, 53, 49, 98, 54, 53, 56, 51, 49, 52, 57, 98, 53, 50, 52, 49, 59, 116, 46, 100, 109, 58, 45, 49, 59, 116, 46, 116, 105, 100, 58, 54, 55, 102, 54, 53, 56, 53, 55, 48, 48, 48, 48, 48, 48, 48, 48])], isReadOnly = false), key = zhfj638gqiZmtkKH, value = null)",zhfj638gqiZmtkKH,MANUAL_INTERVENTION_TRANSACTIONS,"400 : ""{""error"":{""code"":""INVALID_STATUS_TRANSITION"",""additional_information"":""Transaction status is not PENDING""}}"" record = ConsumerRecord(topic = PENDING_TRANSACTIONS_V2, partition = 1, leaderEpoch = 0, offset = 1806, CreateTime = *************, serialized key size = 16, serialized value size = -1, headers = RecordHeaders(headers = [RecordHeader(key = x-pg-profile-id, value = [56, 51, 54, 97, 51, 48, 57, 48, 45, 99, 97, 50, 97, 45, 52, 54, 55, 98, 45, 98, 50, 97, 101, 45, 55, 99, 52, 54, 97, 100, 102, 97, 51, 97, 102, 50]), RecordHeader(key = x-pg-account-id, value = [101, 53, 54, 100, 48, 100, 52, 102, 45, 101, 56, 98, 101, 45, 52, 52, 98, 100, 45, 57, 53, 98, 99, 45, 56, 56, 50, 57, 56, 53, 57, 98, 51, 48, 57, 99]), RecordHeader(key = x-pg-interaction-id, value = [57, 50, 56, 52, 102, 101, 55, 52, 45, 57, 98, 49, 50, 45, 52, 56, 54, 102, 45, 98, 100, 50, 48, 45, 53, 52, 102, 101, 51, 48, 102, 50, 48, 100, 49, 50]), RecordHeader(key = x-pg-instruction-ref-id, value = [122, 104, 102, 106, 54, 51, 56, 103, 113, 105, 90, 109, 116, 107, 75, 72]), RecordHeader(key = x-pg-operation-id, value = [67, 79, 77, 77, 73, 84]), RecordHeader(key = x-pg-kafka-key, value = [67, 79, 77, 77, 124, 122, 104, 102, 106, 54, 51, 56, 103, 113, 105, 90, 109, 116, 107, 75, 72]), RecordHeader(key = x-datadog-trace-id, value = [50, 56, 50, 52, 50, 54, 55, 51, 48, 50, 49, 49, 49, 51, 50, 53, 51, 51, 49]), RecordHeader(key = x-datadog-parent-id, value = [50, 54, 55, 51, 56, 52, 50, 52, 49, 55, 52, 51, 53, 51, 54, 53, 57, 53, 51]), RecordHeader(key = x-datadog-sampling-priority, value = [49]), RecordHeader(key = x-datadog-tags, value = [95, 100, 100, 46, 112, 46, 100, 109, 61, 45, 49, 44, 95, 100, 100, 46, 112, 46, 116, 105, 100, 61, 54, 55, 102, 54, 53, 56, 53, 55, 48, 48, 48, 48, 48, 48, 48, 48]), RecordHeader(key = traceparent, value = [48, 48, 45, 54, 55, 102, 54, 53, 56, 53, 55, 48, 48, 48, 48, 48, 48, 48, 48, 50, 55, 51, 49, 100, 48, 50, 53, 99, 100, 49, 56, 97, 99, 57, 51, 45, 50, 53, 49, 98, 54, 53, 56, 51, 49, 52, 57, 98, 53, 50, 52, 49, 45, 48, 49]), RecordHeader(key = tracestate, value = [100, 100, 61, 115, 58, 49, 59, 112, 58, 50, 53, 49, 98, 54, 53, 56, 51, 49, 52, 57, 98, 53, 50, 52, 49, 59, 116, 46, 100, 109, 58, 45, 49, 59, 116, 46, 116, 105, 100, 58, 54, 55, 102, 54, 53, 56, 53, 55, 48, 48, 48, 48, 48, 48, 48, 48])], isReadOnly = false), key = zhfj638gqiZmtkKH, value = null)"