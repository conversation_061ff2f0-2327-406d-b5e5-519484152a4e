package com.peoplestrust.transaction.api.v1.controller;

import com.peoples.banking.util.logger.annotation.PerfLogger;
import com.peoplestrust.transaction.api.v1.config.TransactionProperty;
import com.peoplestrust.transaction.api.v1.mapper.TransactionMapper;
import com.peoplestrust.transaction.api.v1.model.Balance;
import com.peoplestrust.transaction.api.v1.model.Instruction;
import com.peoplestrust.transaction.api.v1.service.TransactionService;
import com.peoplestrust.transaction.domain.model.BalanceSnapshotResponse;
import com.peoplestrust.transaction.domain.model.InitiateLedgerPrefundReserveRequest;
import com.peoplestrust.transaction.domain.model.InitiateLedgerPrefundReserveResponse;
import com.peoplestrust.transaction.domain.model.InitiateLedgerPrefundReserveResponseTransactionsInner;
import com.peoplestrust.transaction.domain.model.RetrieveBalanceResponse;
import com.peoplestrust.transaction.domain.model.RetrieveLedgerTransactionsResponse;
import com.peoplestrust.transaction.domain.model.Totals;
import com.peoplestrust.transaction.domain.model.TransactionFlow;
import com.peoplestrust.util.api.common.config.APICommonUtilConstant;
import com.peoplestrust.util.api.common.config.ErrorProperty;
import com.peoplestrust.util.api.common.controller.InternalAPIController;
import com.peoplestrust.util.api.common.exception.ValidationException;
import com.peoplestrust.util.api.common.util.JwtUtil;
import com.peoplestrust.util.api.common.util.Messages;
import com.peoplestrust.util.api.common.util.Utils;
import com.peoplestrust.util.api.common.validation.Validations;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping(value = "/v1")
public class InternalController extends InternalAPIController {

  /**
   * Service layer
   */
  @Autowired
  TransactionService transactionService;

  /**
   * Properties
   */
  @Autowired
  TransactionProperty transactionProperty;

  /**
   * Domain to DTO or DTO to Domain translation/mapper layer.
   */
  @Autowired
  TransactionMapper transactionMapper;

  @Autowired
  private Environment env;

  /**
   * Controller to  retrieve account related balances
   *
   * @param interactionId   unique Id
   * @param profileRefId    unique Id that identifies profile
   * @param overdraftAmount overdraft amount from related to an account
   * @param accountRefId    unique Id that identifies account
   * @return RetrieveBalanceResponse
   */
  @PerfLogger
  @RequestMapping(value = "/internal/transaction/balance/{account_ref_id}",
      produces = MediaType.APPLICATION_JSON_VALUE, method = RequestMethod.GET)
  public ResponseEntity<RetrieveBalanceResponse> retrieveBalanceResponse(
      @RequestHeader(name = APICommonUtilConstant.HEADER_AMAZON_REQUEST_ID, required = false) String requestId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_ID, required = true) String interactionId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_PROFILE_ID, required = true) String profileRefId,
      @RequestHeader(name = APICommonUtilConstant.OVERDRAFT_AMOUNT) String overdraftAmount,
      @PathVariable(name = APICommonUtilConstant.ACCOUNT_REF_ID, required = true) String accountRefId) throws Exception {

    // service layer
    RetrieveBalanceResponse response = transactionService.retrieveAccountBalance(
        profileRefId, accountRefId, interactionId, overdraftAmount);

    // response
    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  /**
   * Controller to initiate prefund reserve and correcting transactions
   *
   * @param interactionId                       unique ID header
   * @param interactionTime                     interaction timestamp header
   * @param profileRefId                        profile ID header
   * @param accountRefId                        account ID header
   * @param initiateLedgerPrefundReserveRequest request payload
   * @param bindingResult
   * @return InitiateLedgerPrefundReserveResponse
   */
  @PerfLogger
  @RequestMapping(
      value = "/ledger/transaction/internal",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE,
      method = RequestMethod.POST)
  public ResponseEntity<InitiateLedgerPrefundReserveResponse> initiateInternalTransaction(
      @RequestHeader(name = APICommonUtilConstant.HEADER_AMAZON_REQUEST_ID, required = false) String requestId,
      @RequestHeader(name = APICommonUtilConstant.AUTHORIZATION_HEADER) String authorization,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_ID, required = true) String interactionId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, required = true) String interactionTime,
      @RequestHeader(name = APICommonUtilConstant.HEADER_PROFILE_ID, required = true) String profileRefId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_ACCOUNT_ID, required = true) String accountRefId,
      @Valid @RequestBody InitiateLedgerPrefundReserveRequest initiateLedgerPrefundReserveRequest, BindingResult bindingResult) throws Exception {

    //error checking
    if (bindingResult.hasErrors()) {
      Validations.reply(bindingResult);
    }

    // initial header validations
    initialCheck(interactionTime, interactionId, profileRefId, accountRefId);

    //Validate UUID
    validateUUID(profileRefId, accountRefId);

    //check user group
    checkJwtTokenAndVerifyGroup(authorization, new String[]{APICommonUtilConstant.GL_ADMINISTRATION});

    // transformation layer -- domain (request) to DTO (request)
    Instruction requestDto = transactionMapper.fromPrefundReserveRequestBodyToInstruction(
        initiateLedgerPrefundReserveRequest);

    // service layer
    Instruction responseDto = transactionService.initiateReserve(requestDto, profileRefId, accountRefId, interactionId);

    // transformation layer -- domain (request) to DTO (request)
    InitiateLedgerPrefundReserveResponse response = transactionMapper.fromInstructionToPrefundReserveResponse(responseDto);
    response.setTotals(getTotals(response.getTransactions())); // calculate totals and populate in the response

    // response
    return new ResponseEntity<>(response, HttpStatus.CREATED);
  }

  private Totals getTotals(List<InitiateLedgerPrefundReserveResponseTransactionsInner> transactions) {
    Totals tRes = new Totals();

    // initialize the CREDIT and DEBIT counts/sums
    tRes.setCreditCount(0);
    tRes.setCreditTotal(BigDecimal.ZERO.setScale(2));

    tRes.setDebitCount(0);
    tRes.setDebitTotal(BigDecimal.ZERO.setScale(2));

    // calculate and populate totals
    Map<TransactionFlow, List<InitiateLedgerPrefundReserveResponseTransactionsInner>> map =
        transactions.stream().collect(
            Collectors.groupingBy(InitiateLedgerPrefundReserveResponseTransactionsInner::getTransactionFlow));

    map.forEach(
        (k, v) -> {
          BigDecimal sum = v.stream().map(InitiateLedgerPrefundReserveResponseTransactionsInner::getAmount).reduce(BigDecimal.ZERO.setScale(2), BigDecimal::add);

          if (k == TransactionFlow.CREDIT) {
            // build up running totals for CREDITTs
            tRes.setCreditCount(v.size());
            tRes.setCreditTotal(sum);
          } else if (k == TransactionFlow.DEBIT) {
            // build up running totals for DEBITs
            tRes.setDebitCount(v.size());
            tRes.setDebitTotal(sum);
          }
        });
    return tRes;
  }

  /**
   * Controller to be called for rollback any expired orphan transactions.
   *
   * @param interactionId interaction ID header
   * @param profileRefId  profile ID header
   * @param accountRefId  account ID header
   * @return
   * @throws Exception
   */
  @RequestMapping(
      value = "/internal/ledger/transaction/rollback/{instruction_ref_id}",
      produces = MediaType.APPLICATION_JSON_VALUE,
      method = RequestMethod.DELETE)
  public ResponseEntity<?> rollbackTransaction(
      @RequestHeader(name = APICommonUtilConstant.HEADER_AMAZON_REQUEST_ID, required = false) String requestId,
      @RequestHeader(name = APICommonUtilConstant.AUTHORIZATION_HEADER, required = false ) String authorization,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_ID, required = true) String interactionId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_PROFILE_ID, required = true) String profileRefId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_ACCOUNT_ID, required = true) String accountRefId,
      @PathVariable(name = APICommonUtilConstant.INSTRUCTION_REF_ID) String instructionRefId) throws Exception {

    Boolean b = transactionService.rollbackScheduler(instructionRefId, profileRefId, accountRefId, interactionId);
    return ResponseEntity.ok().body(b);
  }

  /**
   * Controller to retrieve last 48 balance snapshots for a specific account
   *
   * @param interactionId unique id
   * @param profileRefId  unique profile identifier
   * @param accountRefId  unique account identifier
   * @return
   * @throws Exception
   */

  /**
   * Controller to search balance snapshots for a specific account based on condition
   *
   * @param interactionId    unique id
   * @param profileRefId     unique profile identifier
   * @param accountRefId     unique account identifier
   * @param startTime        the start date time range
   * @param endTime          the end data time range
   * @param offset           the offset of search data
   * @param maxResponseItems the max return item
   * @return List<BalanceSnapshotResponse>
   */
  @PerfLogger
  @RequestMapping(value = "/internal/transaction/balance/search/{account_ref_id}",
      produces = MediaType.APPLICATION_JSON_VALUE, method = RequestMethod.GET)
  public ResponseEntity<List<BalanceSnapshotResponse>> searchBalanceSnapShots(
      @RequestHeader(name = APICommonUtilConstant.HEADER_AMAZON_REQUEST_ID, required = false) String requestId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_ID, required = true) String interactionId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_PROFILE_ID, required = true) String profileRefId,
      @PathVariable(name = APICommonUtilConstant.ACCOUNT_REF_ID, required = true) String accountRefId,
      @RequestParam(name = APICommonUtilConstant.START_TIME, required = false) @DateTimeFormat(pattern = APICommonUtilConstant.UTC_DATE_TIME_FORMAT) LocalDateTime startTime,
      @RequestParam(name = APICommonUtilConstant.END_TIME, required = false) @DateTimeFormat(pattern = APICommonUtilConstant.UTC_DATE_TIME_FORMAT) LocalDateTime endTime,
      @RequestParam(name = APICommonUtilConstant.OFFSET, required = false, defaultValue = "0") Integer offset,
      @RequestParam(name = APICommonUtilConstant.MAX_ITEMS, required = false, defaultValue = "25") @Max(25) Integer maxResponseItems) throws Exception {

    if (!Utils.isValidUUID(profileRefId)) {
      throw new ValidationException(ErrorProperty.INVALID_HEADER.name(), APICommonUtilConstant.PROFILE_ID + " is invalid input");
    }

    if (!Utils.isValidUUID(accountRefId)) {
      throw new ValidationException(ErrorProperty.INVALID_FIELD.name(), APICommonUtilConstant.ACCOUNT_ID + " is invalid input");
    }

    if (maxResponseItems < 1 || maxResponseItems > 250) {
      throw new ValidationException(ErrorProperty.INVALID_FIELD.name(), APICommonUtilConstant.MAX_ITEMS + " is invalid input");
    }

    if (startTime != null && endTime != null) {
      if (startTime.isAfter(endTime) || startTime.isEqual(endTime)) {
        log.error("The startTime must be before endTime. startTime: {}, endTime: {}", startTime, endTime);
        throw new ValidationException(Messages.END_TIME_LESS_THAN_START_TIME);
      }
    }

    // service layer
    List<Balance> balanceList = transactionService.searchBalanceSnapShots(
        profileRefId, accountRefId, interactionId, startTime, endTime, offset, maxResponseItems);

    List<BalanceSnapshotResponse> balanceResponse = transactionMapper.fromBalanceListToBalanceResponseList(balanceList);
    // response
    return new ResponseEntity<>(balanceResponse, HttpStatus.OK);
  }

  /**
   * Controller to retrieve latest 25 instructions
   *
   * @param interactionId   unique id
   * @param interactionTime interaction timestamp header
   * @param profileRefId    profile ID header
   * @param accountRefId    account ID header
   * @return List<RetrieveLedgerTransactionsResponse>
   */
  @PerfLogger
  @RequestMapping(
      value = "/ledger/transaction",
      produces = MediaType.APPLICATION_JSON_VALUE,
      method = RequestMethod.GET)
  public ResponseEntity<List<RetrieveLedgerTransactionsResponse>> retrieveInstructionList(
      @RequestHeader(name = APICommonUtilConstant.HEADER_AMAZON_REQUEST_ID, required = false) String requestId,
      @RequestHeader(name = APICommonUtilConstant.AUTHORIZATION_HEADER, required = true) String authorization,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_ID, required = true) String interactionId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, required = true) String interactionTime,
      @RequestHeader(name = APICommonUtilConstant.HEADER_PROFILE_ID, required = true) String profileRefId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_ACCOUNT_ID, required = true) String accountRefId) throws Exception {

    // initial header validations
    initialCheck(interactionTime, interactionId, profileRefId, accountRefId);

    //Validate UUID
    validateUUID(profileRefId, accountRefId);

    //check user group
    checkJwtTokenAndVerifyGroup(authorization, new String[]{APICommonUtilConstant.GL_ADMINISTRATION,
        APICommonUtilConstant.GL_OPERATIONS, APICommonUtilConstant.GL_SERVICE_TEAM});

    // service layer
    List<Instruction> responseDto = transactionService.retrieveInstructionList(profileRefId, accountRefId, interactionId);

    // transformation layer -- domain (request) to DTO (request)
    List<RetrieveLedgerTransactionsResponse> response = transactionMapper.fromInstructionListToRetrieveInstructionResponseList(responseDto);

    // response
    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @Override
  protected int getApiTimeToLiveValue() {
    return transactionProperty.getTimeToLive();
  }

  private void checkJwtTokenAndVerifyGroup(String authorization, String[] checkUserGroups) {
    String disableJwt = env.getProperty(APICommonUtilConstant.ENVIRONMENT_VARIABLE_DISABLE_COGNITO_JWT_VERIFICATION);
    if (!Boolean.parseBoolean(disableJwt)) {
      JwtUtil.isTokenValidAndVerifyGroup(authorization, checkUserGroups);
    }
  }
}
