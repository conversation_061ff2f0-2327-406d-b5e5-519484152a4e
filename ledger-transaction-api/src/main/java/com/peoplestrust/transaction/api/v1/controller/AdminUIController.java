package com.peoplestrust.transaction.api.v1.controller;

import com.peoples.banking.util.logger.annotation.PerfLogger;
import com.peoplestrust.transaction.api.v1.config.TransactionProperty;
import com.peoplestrust.transaction.api.v1.service.TransactionService;
import com.peoplestrust.transaction.api.v1.util.RequestValidationUtil;
import com.peoplestrust.transaction.domain.model.Metadata;
import com.peoplestrust.transaction.domain.model.RetrieveInstructionsResponse;
import com.peoplestrust.transaction.domain.model.RetrieveMetadata;
import com.peoplestrust.transaction.domain.model.RetrieveTransactionsResponse;
import com.peoplestrust.util.api.common.config.APICommonUtilConstant;
import com.peoplestrust.util.api.common.config.ErrorProperty;
import com.peoplestrust.util.api.common.controller.InternalAPIController;
import com.peoplestrust.util.api.common.exception.ValidationException;
import com.peoplestrust.util.api.common.util.JwtUtil;
import com.peoplestrust.util.api.common.util.Messages;
import com.peoplestrust.util.api.common.util.Utils;
import jakarta.validation.constraints.Max;
import java.time.LocalDateTime;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@Validated
@RequestMapping(value = "/v1")
public class AdminUIController extends InternalAPIController {

  /**
   * Properties.
   */
  @Autowired
  TransactionProperty transactionProperty;
  /**
   * Service layer.
   */
  @Autowired
  private TransactionService transactionService;

  @Autowired
  private Environment env;

  /**
   * Controller to retrieve a specific transaction related to an instruction
   *
   * @param interactionId    interaction ID header
   * @param interactionTime  interaction timestamp header
   * @param profileRefId     profile ID header
   * @param accountRefId     account ID header
   * @param instructionRefId instruction reference ID as path variable to retrieve transaction
   * @param transactionRefId transaction reference ID as path variable to retrieve a specific function
   * @return RetrieveLedgerTransactionResponse
   */
  @PerfLogger
  @RequestMapping(
      value = "/ledger/transaction/{instruction_ref_id}/{transaction_ref_id}/metadata",
      produces = MediaType.APPLICATION_JSON_VALUE,
      method = RequestMethod.PUT)
  public ResponseEntity<?> updateTransactionMetaData(
      @RequestHeader(name = APICommonUtilConstant.HEADER_AMAZON_REQUEST_ID, required = false) String requestId,
      @RequestHeader(name = APICommonUtilConstant.AUTHORIZATION_HEADER) String authorization,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_ID) String interactionId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP) String interactionTime,
      @RequestHeader(name = APICommonUtilConstant.HEADER_PROFILE_ID) String profileRefId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_ACCOUNT_ID) String accountRefId,
      @PathVariable(name = APICommonUtilConstant.INSTRUCTION_REF_ID) String instructionRefId,
      @PathVariable(name = APICommonUtilConstant.TRANSACTION_REF_ID) String transactionRefId,
      @RequestBody Metadata metaData) throws Exception {

    // initial header validations
    initialCheck(interactionTime, interactionId, profileRefId, accountRefId);

    //Validate UUID
    validateUUID(profileRefId, accountRefId);

    RequestValidationUtil.validateTicketAndNotes(metaData);

    //check user group
    checkJwtTokenAndVerifyGroup(authorization, new String[]{APICommonUtilConstant.GL_ADMINISTRATION});

    // service layer
    boolean isNewEntity = transactionService.updateTransactionMetadata(instructionRefId, transactionRefId, profileRefId, accountRefId, interactionId, metaData);

    // response
    if (isNewEntity) {
      return new ResponseEntity<>(HttpStatus.CREATED);
    } else {
      return new ResponseEntity<>(HttpStatus.NO_CONTENT);

    }
  }

  /**
   * Controller to retrieve a specific transaction related to an instruction
   *
   * @param interactionId    interaction ID header
   * @param interactionTime  interaction timestamp header
   * @param profileRefId     profile ID header
   * @param accountRefId     account ID header
   * @param instructionRefId instruction reference ID as path variable to retrieve transaction
   * @param transactionRefId transaction reference ID as path variable to retrieve a specific function
   * @return RetrieveMetadata
   */
  @PerfLogger
  @RequestMapping(
      value = "/ledger/transaction/{instruction_ref_id}/{transaction_ref_id}/metadata",
      produces = MediaType.APPLICATION_JSON_VALUE,
      method = RequestMethod.GET)
  public ResponseEntity<RetrieveMetadata> getTransactionMetaData(
      @RequestHeader(name = APICommonUtilConstant.HEADER_AMAZON_REQUEST_ID, required = false) String requestId,
      @RequestHeader(name = APICommonUtilConstant.AUTHORIZATION_HEADER) String authorization,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_ID) String interactionId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP) String interactionTime,
      @RequestHeader(name = APICommonUtilConstant.HEADER_PROFILE_ID) String profileRefId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_ACCOUNT_ID) String accountRefId,
      @PathVariable(name = APICommonUtilConstant.INSTRUCTION_REF_ID) String instructionRefId,
      @PathVariable(name = APICommonUtilConstant.TRANSACTION_REF_ID) String transactionRefId) throws Exception {

    // initial header validations
    initialCheck(interactionTime, interactionId, profileRefId, accountRefId);

    //Validate UUID
    validateUUID(profileRefId, accountRefId);

    //check user group
    checkJwtTokenAndVerifyGroup(authorization, new String[]{APICommonUtilConstant.GL_ADMINISTRATION,
        APICommonUtilConstant.GL_OPERATIONS, APICommonUtilConstant.GL_SERVICE_TEAM});

    // service layer
    RetrieveMetadata retrieveMetadata = transactionService.getTransactionMetadata(instructionRefId, transactionRefId, profileRefId, accountRefId, interactionId);

    // response
    return new ResponseEntity<>(retrieveMetadata, HttpStatus.OK);
  }

  /**
   * Controller for Phase 1 :: Initiate.
   *
   * @param interactionId    interaction ID header
   * @param interactionTime  interaction timestamp header
   * @param profileRefId     profile ID
   * @param paymentRail      payment rail type
   * @param startDate        the start date time range
   * @param endDate          the end data time range
   * @param offset           offset is starting point of payments request filter; if offset if not provided it would be defaulted to zero
   * @param maxResponseItems Maximum number of response items to be returned. All items are returned if this field is absent
   * @return RetrieveTransactionsResponse
   */
  @PerfLogger
  @RequestMapping(
      value = "/ledger/transaction/search",
      produces = MediaType.APPLICATION_JSON_VALUE,
      method = RequestMethod.POST)
  public ResponseEntity<RetrieveTransactionsResponse> searchTransaction(
      @RequestHeader(name = APICommonUtilConstant.HEADER_AMAZON_REQUEST_ID, required = false) String requestId,
      @RequestHeader(name = APICommonUtilConstant.AUTHORIZATION_HEADER, required = true) String authorization,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_ID) String interactionId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP) String interactionTime,
      @RequestParam(name = APICommonUtilConstant.PROFILE_ID) String profileRefId,
      @RequestParam(name = APICommonUtilConstant.INSTRUCTION_ID, required = false) String instructionRefId,
      @RequestParam(name = APICommonUtilConstant.PAYMENT_RAIL) String paymentRail,
      @RequestParam(name = APICommonUtilConstant.START_DATE, required = false) @DateTimeFormat(pattern = APICommonUtilConstant.UTC_DATE_TIME_FORMAT) LocalDateTime startDate,
      @RequestParam(name = APICommonUtilConstant.END_DATE, required = false) @DateTimeFormat(pattern = APICommonUtilConstant.UTC_DATE_TIME_FORMAT) LocalDateTime endDate,
      @RequestParam(name = APICommonUtilConstant.OFFSET) Integer offset,
      @RequestParam(name = APICommonUtilConstant.MAX_ITEMS) @Max(25) Integer maxResponseItems)
      throws Exception {

    // initial header validations
    initialCheck(interactionTime, interactionId);

    if (!Utils.isValidUUID(profileRefId)) {
      throw new ValidationException(ErrorProperty.INVALID_PARAM.name(), APICommonUtilConstant.PROFILE_ID + " is invalid input");
    }

    if (offset < 0) {
      throw new ValidationException(ErrorProperty.INVALID_PARAM.name(), APICommonUtilConstant.OFFSET + " is invalid input");
    }

    if (maxResponseItems < 1 || maxResponseItems > 25) {
      throw new ValidationException(ErrorProperty.INVALID_PARAM.name(), APICommonUtilConstant.MAX_ITEMS + " is invalid input");
    }

    if (startDate != null && endDate != null) {
      if (startDate.isAfter(endDate) || startDate.isEqual(endDate)) {
        log.error("The startTime must be before endTime. startTime: {}, endTime: {}", startDate, endDate);
        throw new ValidationException(ErrorProperty.INVALID_PARAM .name(), Messages.END_TIME_LESS_THAN_START_TIME);
      }
    }

    //check user group
    checkJwtTokenAndVerifyGroup(authorization, new String[]{APICommonUtilConstant.GL_ADMINISTRATION,
        APICommonUtilConstant.GL_OPERATIONS, APICommonUtilConstant.GL_SERVICE_TEAM});

    // service layer
    RetrieveTransactionsResponse response = transactionService.searchTransaction(profileRefId, instructionRefId, paymentRail,
        startDate, endDate, offset, maxResponseItems);

    // response
    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  /**
   * Controller for Phase 1 :: Initiate.
   *
   * @param interactionId    interaction ID header
   * @param interactionTime  interaction timestamp header
   * @param profileRefId     profile ID
   * @param paymentRail      payment rail type
   * @param startTime        the start date time range
   * @param endTime          the end data time range
   * @param offset           offset is starting point of payments request filter; if offset if not provided it would be defaulted to zero
   * @param maxResponseItems Maximum number of response items to be returned. All items are returned if this field is absent
   * @return RetrieveInstructionsResponse
   */
  @PerfLogger
  @RequestMapping(
      value = "/ledger/transaction/search/instruction",
      produces = MediaType.APPLICATION_JSON_VALUE,
      method = RequestMethod.POST)
  public ResponseEntity<RetrieveInstructionsResponse> searchInstruction(
      @RequestHeader(name = APICommonUtilConstant.HEADER_AMAZON_REQUEST_ID, required = false) String requestId,
      @RequestHeader(name = APICommonUtilConstant.AUTHORIZATION_HEADER, required = true) String authorization,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_ID) String interactionId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP) String interactionTime,
      @RequestParam(name = APICommonUtilConstant.PROFILE_ID) String profileRefId,
      @RequestParam(name = APICommonUtilConstant.ACCOUNT_ID, required = true) String accountRefId,
      @RequestParam(name = APICommonUtilConstant.INSTRUCTION_ID, required = false) String instructionRefId,
      @RequestParam(name = APICommonUtilConstant.PAYMENT_RAIL, required = false) String paymentRail,
      @RequestParam(name = APICommonUtilConstant.START_TIME, required = false) @DateTimeFormat(pattern = APICommonUtilConstant.UTC_DATE_TIME_FORMAT) LocalDateTime startTime,
      @RequestParam(name = APICommonUtilConstant.END_TIME, required = false) @DateTimeFormat(pattern = APICommonUtilConstant.UTC_DATE_TIME_FORMAT) LocalDateTime endTime,
      @RequestParam(name = APICommonUtilConstant.OFFSET) Integer offset,
      @RequestParam(name = APICommonUtilConstant.MAX_ITEMS) @Max(25) Integer maxResponseItems)
      throws Exception {

    // initial header validations
    initialCheck(interactionTime, interactionId);

    if (!Utils.isValidUUID(profileRefId)) {
      throw new ValidationException(ErrorProperty.INVALID_FIELD.name(), APICommonUtilConstant.PROFILE_ID + " is invalid input");
    }

    if (!Utils.isValidUUID(accountRefId)) {
      throw new ValidationException(ErrorProperty.INVALID_FIELD.name(), APICommonUtilConstant.ACCOUNT_ID + " is invalid input");
    }
    if (offset < 0) {
      throw new ValidationException(ErrorProperty.INVALID_FIELD.name(), APICommonUtilConstant.OFFSET + " is invalid input");
    }

    if (maxResponseItems < 1 || maxResponseItems > 25) {
      throw new ValidationException(ErrorProperty.INVALID_FIELD.name(), APICommonUtilConstant.MAX_ITEMS + " is invalid input");
    }

    if (startTime != null && endTime != null) {
      if (startTime.isAfter(endTime) || startTime.isEqual(endTime)) {
        log.error("The startTime must be before endTime. startTime: {}, endTime: {}", startTime, endTime);
        throw new ValidationException(Messages.END_TIME_LESS_THAN_START_TIME);
      }
    }

    //check user group
    checkJwtTokenAndVerifyGroup(authorization, new String[]{APICommonUtilConstant.GL_ADMINISTRATION,
        APICommonUtilConstant.GL_OPERATIONS, APICommonUtilConstant.GL_SERVICE_TEAM});

    // service layer
    RetrieveInstructionsResponse response = transactionService.searchInstruction(profileRefId, accountRefId, instructionRefId, paymentRail,
        startTime, endTime, offset, maxResponseItems);

    // response
    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @Override
  protected int getApiTimeToLiveValue() {
    return transactionProperty.getTimeToLive();
  }

  private void checkJwtTokenAndVerifyGroup(String authorization, String[] checkUserGroups) {
    String disableJwt = env.getProperty(APICommonUtilConstant.ENVIRONMENT_VARIABLE_DISABLE_COGNITO_JWT_VERIFICATION);
    if (!Boolean.parseBoolean(disableJwt)) {
      JwtUtil.isTokenValidAndVerifyGroup(authorization, checkUserGroups);
    }
  }

}
