package com.peoplestrust.transaction.api.v1.model;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.peoplestrust.util.api.common.util.LocalDateTimeDeserializer;
import com.peoplestrust.util.api.common.util.LocalDateTimeSerializer;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.util.UUID;

import jakarta.persistence.Id;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@Builder
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
public class Balance {

  @Id
  private Integer id;
  private UUID profileRefId;
  private UUID accountRefId;
  private BigDecimal totalAmountCredit;
  private BigDecimal totalAmountDebit;
  private BigDecimal totalAmount;
  private BigDecimal totalReserveAmount;
  private BigDecimal totalPendingAmount;
  private String monetaryUnit;
  private OffsetDateTime effectiveToDateTime;
  private OffsetDateTime effectiveFromDateTime;

  @JsonDeserialize(using = LocalDateTimeDeserializer.class)
  @JsonSerialize(using = LocalDateTimeSerializer.class)
  private LocalDateTime createdDateTime;

}

