package com.peoplestrust.transaction.api.v1.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.peoplestrust.util.api.common.util.CustomOffsetDateTimeDeserializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;

@Configuration
public class JacksonConfig {

  /** ISO-8601 formatter with *exactly* 3 fractional digits and “Z”. */
  private static final DateTimeFormatter ISO_UTC_MILLIS =
      new DateTimeFormatterBuilder().appendInstant(3).toFormatter();

  /** Serialiser that always prints  .SSS  milliseconds. */
  private static final com.fasterxml.jackson.databind.JsonSerializer<OffsetDateTime>
      OFFSET_DT_SERIALIZER = new com.fasterxml.jackson.databind.JsonSerializer<>() {
    @Override
    public void serialize(OffsetDateTime value,
        com.fasterxml.jackson.core.JsonGenerator gen,
        com.fasterxml.jackson.databind.SerializerProvider serializers)
        throws IOException {
      gen.writeString(ISO_UTC_MILLIS.format(value));
    }
  };

  @Bean
  public ObjectMapper objectMapper() {
    ObjectMapper objectMapper = new ObjectMapper();
    SimpleModule customModule = new SimpleModule();
    customModule.addDeserializer(OffsetDateTime.class, new CustomOffsetDateTimeDeserializer());
    objectMapper.registerModule(new JavaTimeModule());
    objectMapper.registerModule(customModule);
    objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
    return objectMapper;
  }
}
