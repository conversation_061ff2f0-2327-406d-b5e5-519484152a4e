package com.peoplestrust.transaction.api.v1.service;

import com.peoples.banking.util.logger.annotation.PerfLogger;
import com.peoplestrust.scheduler.persistence.entity.BalanceEntity;
import com.peoplestrust.transaction.api.v1.model.Balance;
import com.peoplestrust.transaction.api.v1.model.Instruction;
import com.peoplestrust.transaction.api.v1.model.Transaction;
import com.peoplestrust.transaction.domain.model.Metadata;
import com.peoplestrust.transaction.domain.model.RetrieveBalanceResponse;
import com.peoplestrust.transaction.domain.model.RetrieveInstructionsResponse;
import com.peoplestrust.transaction.domain.model.RetrieveMetadata;
import com.peoplestrust.transaction.domain.model.RetrieveTransactionsResponse;
import com.peoplestrust.util.api.common.exception.InvalidFieldException;
import com.peoplestrust.util.api.common.exception.ResourceNotFoundException;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import org.springframework.transaction.annotation.Transactional;

/**
 * Transaction service interface.
 */
public interface TransactionService {

  /**
   * Service for Phase 1 :: Initiate.
   *
   * @param instruction   instruction DTO
   * @param profileRefId  unique profile reference id
   * @param accountRefId  unique account reference id
   * @param interactionId unique id
   * @return
   * @throws Exception
   */
  Instruction initiateInstruction(Instruction instruction, String profileRefId, String accountRefId, String interactionId) throws Exception;

  /**
   * Service to post prefund reserve or post the corrections manually
   *
   * @param instruction   instruction DTO
   * @param profileRefId  unique profile reference id
   * @param accountRefId  unique account reference id
   * @param interactionId unique id
   * @return
   * @throws Exception
   */
  Instruction initiateReserve(Instruction instruction, String profileRefId, String accountRefId, String interactionId) throws Exception;

  /**
   * Service for Phase 2 :: Commit.
   *
   * @param instructionRefId unique instruction reference id
   * @param profileId        unique profile reference id
   * @param accountId        unique account reference id
   * @param interactionId    unique id
   * @return
   * @throws Exception
   */
  Instruction commitTransaction(String instructionRefId, String profileId, String accountId, String interactionId) throws Exception;

  /**
   * Service for Phase 2 :: Rollback
   *
   * @param instructionRefId unique instruction reference id
   * @param profileId        unique profile reference id
   * @param accountId        unique account reference id
   * @param interactionId    unique id
   * @throws Exception
   */
  void rollbackTransaction(String instructionRefId, String profileId, String accountId, String interactionId) throws Exception;

  @PerfLogger
  Optional<BigDecimal> getTotalAmount(String accountId, String profileId) throws InvalidFieldException;

  @PerfLogger
  Optional<BigDecimal> getTotalAmount(String accountId, String profileId, Integer instructionId, LocalDateTime effectiveToDateTime)
      throws InvalidFieldException;

  @PerfLogger
  Optional<BigDecimal> getTotalAmount(String accountId, String profileId, Integer instructionId) throws InvalidFieldException;

  @PerfLogger
  Optional<BalanceEntity> getBalanceSnapshot(String accountId, String profileId) throws InvalidFieldException;


  @PerfLogger
  Optional<BigDecimal> getTotalAmount(String accountId, String profileId, LocalDateTime effectiveToDateTime) throws InvalidFieldException;

  @PerfLogger
  Optional<BigDecimal> getReserveTotalAmount(String accountId, String profileId) throws InvalidFieldException;

  @PerfLogger
  Optional<BigDecimal> getReserveTotalAmount(String accountId, String profileId, LocalDateTime effectiveToDateTime) throws InvalidFieldException;

  /**
   * service to retrieve all the transactions related to an instruction
   *
   * @param profileRefId     profile reference ID (unique) that identifies the profile
   * @param accountRefId     account reference ID (unique) that identifies the account
   * @param instructionRefId instruction reference ID (unique) that identifies the instruction
   * @return
   * @throws Exception
   */
  Instruction getInstruction(String profileRefId, String accountRefId, String instructionRefId, String interactionId)
      throws Exception;


  /**
   * service to retrieve a specific transaction related to an instruction
   *
   * @param instructionRefId instruction reference ID (unique) that identifies the instruction
   * @param transactionRefId transaction reference ID (unique) that identifies the transaction
   * @param profileRefId     profile reference ID (unique) that identifies the profile
   * @param accountRefId     account reference ID (unique) that identifies the account
   * @return
   * @throws Exception
   */
  Transaction getTransaction(String instructionRefId, String transactionRefId, String profileRefId, String accountRefId, String interactionId)
      throws Exception;

  /**
   * service to retrieve a specific transaction related to an transaction refId
   *
   * @param transactionRefId transaction reference ID (unique) that identifies the transaction
   * @return
   * @throws Exception
   */
  Transaction getTransactionByRefId(String transactionRefId) throws Exception;

  /**
   * Phase 3 :: Reverse a specific transaction.
   *
   * @param instructionRefId instruction reference ID (unique) that identifies the instruction
   * @param transactionRefId transaction reference ID (unique) that identifies the transaction
   * @param profileRefId     profile reference ID (unique) that identifies the profile
   * @param accountRefId     account reference ID (unique) that identifies the account
   */
  void reverseTransaction(String instructionRefId, String transactionRefId, String profileRefId, String accountRefId, String interactionId)
      throws Exception;


  /**
   * Service to retrieve available Prefund Reserve Balance
   *
   * @param accountId unique account identifier
   * @param profileId unique profile identifier
   * @return
   * @throws Exception
   */
  BigDecimal getPrefundReserveBalanceAmount(String accountId, String profileId) throws InvalidFieldException;

  /**
   * Service to retrieve fund hold amount
   *
   * @param accountId unique account identifier
   * @param profileId unique profile identifier
   * @return
   * @throws InvalidFieldException
   */
  BigDecimal getFundHoldAmount(String accountId, String profileId) throws InvalidFieldException;

  /**
   * Internal API::Service to retrieve account balance
   *
   * @param profileRefId    unique profile identifier
   * @param accountRefId    unique account identifier
   * @param interactionId   unique id
   * @param overdraftAmount overdraft amount specific to an account
   * @return
   * @throws InvalidFieldException
   */
  RetrieveBalanceResponse retrieveAccountBalance(String profileRefId, String accountRefId, String interactionId, String overdraftAmount)
      throws InvalidFieldException;

  /**
   * Internal API::Service to rollback orphan transactions
   *
   * @param instructionRefId unique instruction identifier
   * @param profileId        unique profile identifier
   * @param accountId        unique account identifier
   * @param interactionId    unique id
   * @return
   * @throws Exception
   */
  @PerfLogger
  @Transactional
  Boolean rollbackScheduler(String instructionRefId, String profileId, String accountId, String interactionId)
      throws Exception;

  /**
   * Internal API::Service to retrieve previous balances
   *
   * @param profileRefId  unique profile identifier
   * @param accountRefId  unique account identifier
   * @param interactionId unique id
   * @return
   * @throws ResourceNotFoundException
   */

  @PerfLogger
  List<Balance> searchBalanceSnapShots(String profileRefId, String accountRefId, String interactionId,
      LocalDateTime startDate, LocalDateTime endDate, Integer offset, Integer maxResponseItems) throws ResourceNotFoundException;

  /**
   * Service to retrieve latest 25 instructions list
   *
   * @param profileRefId unique profile  identifier
   * @param accountRefId unique account  identifier
   * @return
   * @throws Exception
   */
  List<Instruction> retrieveInstructionList(String profileRefId, String accountRefId, String interactionId)
      throws Exception;


  /**
   * Service to retrieve instructions list
   *
   * @param profileRefId     unique profile  identifier
   * @param paymentRail      payment rail type
   * @param startDate        from date
   * @param endDate          to date
   * @param offset           offset is starting point of payments request filter; if offset if not provided it would be defaulted to zero
   * @param maxResponseItems Maximum number of response items to be returned. All items are returned if this field is absent
   * @return RetrieveTransactionsResponse
   * @throws Exception
   */
  RetrieveTransactionsResponse searchTransaction(String profileRefId, String instructionRefId, String paymentRail,
      LocalDateTime startDate, LocalDateTime endDate, Integer offset, Integer maxResponseItems) throws Exception;

  /**
   * Service to retrieve instructions list
   *
   * @param profileRefId     unique profile  identifier
   * @param accountRefId     unique account  identifier
   * @param paymentRail      payment rail type
   * @param startDate        from date
   * @param endDate          to date
   * @param offset           offset is starting point of payments request filter; if offset if not provided it would be defaulted to zero
   * @param maxResponseItems Maximum number of response items to be returned. All items are returned if this field is absent
   * @return RetrieveInstructionsResponse
   * @throws Exception when found any error
   */
  RetrieveInstructionsResponse searchInstruction(String profileRefId, String accountRefId, String instructionRefId, String paymentRail,
      LocalDateTime startDate, LocalDateTime endDate, Integer offset, Integer maxResponseItems) throws Exception;

  /**
   * update transaction Metadata.
   *
   * @param instructionRefId instruction reference ID (unique) that identifies the instruction
   * @param transactionRefId transaction reference ID (unique) that identifies the transaction
   * @param profileRefId     profile reference ID (unique) that identifies the profile
   * @param accountRefId     account reference ID (unique) that identifies the account
   * @param metaData         metaData update to DB
   */
  boolean updateTransactionMetadata(String instructionRefId, String transactionRefId, String profileRefId, String accountRefId, String interactionId, Metadata metaData)
      throws Exception;

  /**
   * get transaction Metadata.
   *
   * @param instructionRefId instruction reference ID (unique) that identifies the instruction
   * @param transactionRefId transaction reference ID (unique) that identifies the transaction
   * @param profileRefId     profile reference ID (unique) that identifies the profile
   * @param accountRefId     account reference ID (unique) that identifies the account
   * @return RetrieveMetadata of a transaction
   */
  RetrieveMetadata getTransactionMetadata(String instructionRefId, String transactionRefId, String profileRefId, String accountRefId, String interactionId) throws Exception;
}

