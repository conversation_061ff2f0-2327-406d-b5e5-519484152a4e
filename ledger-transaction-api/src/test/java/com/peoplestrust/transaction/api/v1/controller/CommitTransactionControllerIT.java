package com.peoplestrust.transaction.api.v1.controller;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.peoplestrust.transaction.api.v1.common.TestUtil;
import com.peoplestrust.transaction.api.v1.model.Account;
import com.peoplestrust.transaction.api.v1.model.Instruction;
import com.peoplestrust.transaction.api.v1.service.ValidationService;
import com.peoplestrust.transaction.domain.model.CommitLedgerTransactionResponse;
import com.peoplestrust.transaction.persistence.entity.InstructionEntity;

import com.peoplestrust.transaction.persistence.entity.TransactionStatus;
import com.peoplestrust.transaction.persistence.repository.read.ReadInstructionRepository;
import com.peoplestrust.transaction.persistence.repository.write.InstructionRepository;
import com.peoplestrust.util.api.common.config.APICommonUtilConstant;
import java.time.Instant;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

@Slf4j
@ActiveProfiles("test")
@ExtendWith(MockitoExtension.class)
@SpringBootTest
@AutoConfigureMockMvc
public class CommitTransactionControllerIT {
  private static final String URL_COMMIT_TRANSACTION = "/v1/ledger/transaction/{instruction_ref_id}";
  private static final String requestId = UUID.randomUUID().toString();
  private static final String interactionId = UUID.randomUUID().toString();
  private static final String profileRefId = UUID.randomUUID().toString();
  private static final String accountRefId = UUID.randomUUID().toString();

  @Autowired
  private MockMvc mockMvc;
  @MockBean
  ValidationService validationService;
  @MockBean
  ReadInstructionRepository readInstructionRepository;
  private ObjectMapper objectMapper;
  private HttpHeaders headers;
  @MockBean
  InstructionRepository instructionRepository;

  @BeforeEach
  public void setupBeforeTest() {
    objectMapper = new ObjectMapper();
    objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    objectMapper.registerModule(new JavaTimeModule())
        .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
        .disable(DeserializationFeature.ADJUST_DATES_TO_CONTEXT_TIME_ZONE)
        .enable(SerializationFeature.WRITE_DATES_WITH_ZONE_ID);

    headers = new HttpHeaders();
    headers.add(APICommonUtilConstant.HEADER_AMAZON_REQUEST_ID, requestId);
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_ID, interactionId);
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString());
    headers.add(APICommonUtilConstant.HEADER_PROFILE_ID, profileRefId);
    headers.add(APICommonUtilConstant.HEADER_ACCOUNT_ID, accountRefId);
    headers.add(APICommonUtilConstant.AUTHORIZATION, TestUtil.JWT_TOKEN);
  }

  @Test
  public void commitTransaction_success() throws Exception {
    String instructionRefId = UUID.randomUUID().toString();
    Account account = TestUtil.createAccount(accountRefId, profileRefId);

    when(validationService.getAccount(any(), any(), any())).thenReturn(account);
    when(validationService.isProfileAvailable(any(), any())).thenReturn(true);
    when(instructionRepository.save(any())).thenAnswer(invocation -> invocation.getArgument(0));

    Instruction instruction = TestUtil.createInstruction(accountRefId, profileRefId);

    InstructionEntity instructionEntity = TestUtil.getInstructionData(instruction);
    //Change transaction status to pending
    for (int i = 0; i < instructionEntity.getTransactions().size(); i++) {
      instructionEntity.getTransactions().get(i).setStatus(TransactionStatus.PENDING);
    }

    when(instructionRepository.findByProfileRefIdAndAccountRefIdAndInstructionRefId(any(), any(), any()))
        .thenReturn(instructionEntity);

    MvcResult result = this.mockMvc.perform(MockMvcRequestBuilders.patch(URL_COMMIT_TRANSACTION, instructionRefId)
            .headers(headers)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andDo(print())
        .andExpect(status().isOk())
        .andReturn();

    String jsonResponse = result.getResponse().getContentAsString();
    CommitLedgerTransactionResponse response = objectMapper.readValue(jsonResponse, CommitLedgerTransactionResponse.class);
    Assertions.assertNotNull(response);
  }
}
