package com.peoplestrust.transaction.api.v1.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.peoplestrust.transaction.api.v1.common.TestUtil;
import com.peoplestrust.transaction.api.v1.model.Account;
import com.peoplestrust.transaction.api.v1.service.ValidationService;
import com.peoplestrust.transaction.persistence.repository.read.ReadTransactionRepository;
import com.peoplestrust.util.api.common.config.APICommonUtilConstant;
import java.math.BigDecimal;
import java.util.Objects;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.time.Instant;
import java.util.UUID;

@Slf4j
@ActiveProfiles("test")
@ExtendWith(MockitoExtension.class)
@SpringBootTest
@AutoConfigureMockMvc
public class GetBalanceControllerIT {

  private static final String URL_INTERNAL_BALANCE = "/v1/internal/transaction/balance/";
  private static final String requestId = UUID.randomUUID().toString();
  private static final String interactionId = UUID.randomUUID().toString();
  private static final String profileRefId = UUID.randomUUID().toString();
  private static final String accountRefId = UUID.randomUUID().toString();

  @Autowired
  private MockMvc mockMvc;

  @MockBean
  private ValidationService validationService;

  @MockBean
  private ReadTransactionRepository readTransactionRepository;
  private HttpHeaders headers;
  private ObjectMapper objectMapper;

  static {
    System.setProperty(APICommonUtilConstant.ENVIRONMENT_VARIABLE_DISABLE_COGNITO_JWT_VERIFICATION, "true");
  }

  @BeforeEach
  public void setupBeforeTest() {
    objectMapper = new ObjectMapper();
    objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    headers = new HttpHeaders();
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_ID, interactionId);
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString());
    headers.add(APICommonUtilConstant.HEADER_PROFILE_ID, profileRefId);
    headers.add(APICommonUtilConstant.HEADER_ACCOUNT_ID, accountRefId);
    headers.add(APICommonUtilConstant.AUTHORIZATION, TestUtil.JWT_TOKEN);
  }

  @Test
  public void getBalanceTest() throws Exception {
    Account account = TestUtil.createAccount(accountRefId, profileRefId);
    when(validationService.getAccount(any(), any(), any())).thenReturn(account);
    headers.add(APICommonUtilConstant.OVERDRAFT_AMOUNT, "25000");

    BigDecimal mockFundHoldAmount = new BigDecimal("1000.00");
    when(readTransactionRepository.getFundHoldAmount(any(), any(), any(), any())).thenReturn(Optional.of(mockFundHoldAmount));

    this.mockMvc.perform(
            MockMvcRequestBuilders.get(URL_INTERNAL_BALANCE + accountRefId)
                .headers(headers)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andReturn();
  }

  @Disabled
  @Test
  void getBalance_MissingHeader() throws Exception {
    Account account = TestUtil.createAccount(accountRefId, profileRefId);
    when(validationService.getAccount(any(), any(), any())).thenReturn(account);
    headers.add(APICommonUtilConstant.OVERDRAFT_AMOUNT, "25000");
    BigDecimal mockFundHoldAmount = new BigDecimal("1000.00");
    when(readTransactionRepository.getFundHoldAmount(any(), any(), any(), any())).thenReturn(Optional.of(mockFundHoldAmount));
    headers.remove(APICommonUtilConstant.AUTHORIZATION);
    MvcResult result = this.mockMvc.perform(
                    MockMvcRequestBuilders.get(URL_INTERNAL_BALANCE + accountRefId)
                            .headers(headers)
                            .contentType(MediaType.APPLICATION_JSON)
                            .accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isBadRequest())
            .andReturn();
    objectMapper = new ObjectMapper();
    JsonNode jsonNode = objectMapper.readTree(Objects.requireNonNull(result.getResponse().getContentAsString()));
    assertEquals("MISSING_HEADER", jsonNode.get("error").get("code").asText());
    assertEquals("Authorization", jsonNode.get("error").get("additional_information").asText());
  }

  @Disabled
  @Test
  void getBalance_BadToken() throws Exception {
    Account account = TestUtil.createAccount(accountRefId, profileRefId);
    when(validationService.getAccount(any(), any(), any())).thenReturn(account);
    headers.add(APICommonUtilConstant.OVERDRAFT_AMOUNT, "25000");
    BigDecimal mockFundHoldAmount = new BigDecimal("1000.00");
    when(readTransactionRepository.getFundHoldAmount(any(), any(), any(), any())).thenReturn(Optional.of(mockFundHoldAmount));
    headers.set(APICommonUtilConstant.AUTHORIZATION, TestUtil.BAD_JWT_TOKEN);
    MvcResult result = this.mockMvc.perform(
                    MockMvcRequestBuilders.get(URL_INTERNAL_BALANCE + accountRefId)
                            .headers(headers)
                            .contentType(MediaType.APPLICATION_JSON)
                            .accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isUnauthorized())
            .andReturn();
    objectMapper = new ObjectMapper();
    JsonNode jsonNode = objectMapper.readTree(Objects.requireNonNull(result.getResponse().getContentAsString()));
    assertEquals("UNAUTHORIZED", jsonNode.get("error").get("code").asText());
    assertEquals("UNAUTHORIZED", jsonNode.get("error").get("additional_information").asText());
  }
}
