package com.peoplestrust.transaction.api.v1.controller;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.peoplestrust.transaction.api.v1.common.TestUtil;
import com.peoplestrust.transaction.api.v1.model.Account;
import com.peoplestrust.transaction.api.v1.service.ValidationService;
import com.peoplestrust.transaction.domain.model.CommitLedgerTransactionResponse;
import com.peoplestrust.transaction.domain.model.LedgerTransaction;
import com.peoplestrust.transaction.persistence.entity.InstructionEntity;
import com.peoplestrust.transaction.persistence.entity.TransactionEntity;
import com.peoplestrust.transaction.persistence.entity.TransactionStatus;
import com.peoplestrust.transaction.persistence.repository.read.ReadTransactionRepository;
import com.peoplestrust.transaction.persistence.repository.write.TransactionRepository;
import com.peoplestrust.util.api.common.config.APICommonUtilConstant;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import static org.junit.jupiter.api.Assertions.*;

@Slf4j
@ActiveProfiles("test")
@ExtendWith(MockitoExtension.class)
@SpringBootTest
@AutoConfigureMockMvc
public class ReverseTransactionControllerIT {

  private static final String URL = "/v1/ledger/transaction";
  private static final String requestId = UUID.randomUUID().toString();
  private static final String interactionId = UUID.randomUUID().toString();
  private static final String profileRefId = UUID.randomUUID().toString();
  private static final String accountRefId = UUID.randomUUID().toString();

  @Autowired
  private MockMvc mockMvc;

  @MockBean
  private ValidationService validationService;

  private HttpHeaders headers;
  private ObjectMapper objectMapper;

  @MockBean
  private ReadTransactionRepository readTransactionRepository;

  @MockBean
  private TransactionRepository transactionRepository;

  static {
    System.setProperty(APICommonUtilConstant.ENVIRONMENT_VARIABLE_DISABLE_COGNITO_JWT_VERIFICATION, "true");
  }

  @BeforeEach
  public void setupBeforeTest() {
    objectMapper = new ObjectMapper();
    objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    headers = new HttpHeaders();
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_ID, interactionId);
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString());
    headers.add(APICommonUtilConstant.HEADER_PROFILE_ID, profileRefId);
    headers.add(APICommonUtilConstant.HEADER_ACCOUNT_ID, accountRefId);
    headers.add(APICommonUtilConstant.AUTHORIZATION, TestUtil.JWT_TOKEN);
  }

  @Test
  public void reverseTransactionTest() throws Exception {
    Account account = TestUtil.createAccount(accountRefId, profileRefId);
    when(validationService.getAccount(any(), any(), any())).thenReturn(account);
    when(validationService.isProfileAvailable(any(), any())).thenReturn(true);

    CommitLedgerTransactionResponse response1 = TestUtil.getCommitLedgerTransactionResponse();
    LedgerTransaction transaction = response1.getTransactions().get(0);
    String tranRefId = transaction.getTransactionRefId();
    String instRefId = response1.getInstructionRefId();

    InstructionEntity instructionEntity = TestUtil.createInstructionAndTransactionData(accountRefId, profileRefId);

    // Ensure transactions are initialized
    List<TransactionEntity> tlist = instructionEntity.getTransactions();
    for (TransactionEntity transactionEntity : tlist) {
      transactionEntity.setStatus(TransactionStatus.POSTED);
    }

    when(readTransactionRepository.findByInstRefIdTransactionRefIdProfileRefIdAccountRefId(any(), any(), any(), any())).thenReturn(tlist);
    when(transactionRepository.save(any())).thenAnswer(invocation -> invocation.getArgument(0));

    this.mockMvc.perform(MockMvcRequestBuilders.delete(URL + "/" + instRefId + "/" + tranRefId)
            .headers(headers)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isNoContent())
        .andReturn();
  }

  @Test
  public void reverseTransactionShouldSetFinalizationDateTime() throws Exception {
    // Given
    Account account = TestUtil.createAccount(accountRefId, profileRefId);
    when(validationService.getAccount(any(), any(), any())).thenReturn(account);
    when(validationService.isProfileAvailable(any(), any())).thenReturn(true);

    CommitLedgerTransactionResponse response1 = TestUtil.getCommitLedgerTransactionResponse();
    LedgerTransaction transaction = response1.getTransactions().get(0);
    String tranRefId = transaction.getTransactionRefId();
    String instRefId = response1.getInstructionRefId();

    InstructionEntity instructionEntity = TestUtil.createInstructionAndTransactionData(accountRefId, profileRefId);
    List<TransactionEntity> tlist = instructionEntity.getTransactions();

    // Set initial state - POSTED status with null finalizationDateTime
    for (TransactionEntity transactionEntity : tlist) {
      transactionEntity.setStatus(TransactionStatus.POSTED);
      transactionEntity.setFinalizationDateTime(null); // Explicitly set to null to test the new logic
    }

    when(readTransactionRepository.findByInstRefIdTransactionRefIdProfileRefIdAccountRefId(any(), any(), any(), any()))
        .thenReturn(tlist);

    // Track if we found the reversed transaction with finalizationDateTime
    final boolean[] foundReversedWithFinalizationDate = {false};

    // Capture the saved entity to verify finalizationDateTime is set
    when(transactionRepository.save(any())).thenAnswer(invocation -> {
      TransactionEntity savedEntity = invocation.getArgument(0);

      // Check if this is the reversed transaction (first save call)
      if (savedEntity.getStatus() == TransactionStatus.REVERSED) {
        // Verify that finalizationDateTime is set (new logic)
        assertNotNull(savedEntity.getFinalizationDateTime(),
            "FinalizationDateTime should be set when reversing a transaction");
        foundReversedWithFinalizationDate[0] = true;
      }

      return savedEntity;
    });

    // When
    this.mockMvc.perform(MockMvcRequestBuilders.delete(URL + "/" + instRefId + "/" + tranRefId)
            .headers(headers)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isNoContent())
        .andReturn();

    // Then - verify save was called twice (once for reversal, once for new transaction)
    verify(transactionRepository, times(2)).save(any());

    // Verify we found and validated the reversed transaction
    assertTrue(foundReversedWithFinalizationDate[0],
        "Should have found and validated a transaction with REVERSED status and finalizationDateTime");
  }

  @Test
  public void reverseTransactionAlreadyReversedShouldNotUpdateAgain() throws Exception {
    // Given
    Account account = TestUtil.createAccount(accountRefId, profileRefId);
    when(validationService.getAccount(any(), any(), any())).thenReturn(account);
    when(validationService.isProfileAvailable(any(), any())).thenReturn(true);

    CommitLedgerTransactionResponse response1 = TestUtil.getCommitLedgerTransactionResponse();
    LedgerTransaction transaction = response1.getTransactions().get(0);
    String tranRefId = transaction.getTransactionRefId();
    String instRefId = response1.getInstructionRefId();

    InstructionEntity instructionEntity = TestUtil.createInstructionAndTransactionData(accountRefId, profileRefId);
    List<TransactionEntity> tlist = instructionEntity.getTransactions();

    LocalDateTime existingFinalizationTime = LocalDateTime.now().minusDays(1);

    // Set initial state - already REVERSED with existing finalizationDateTime
    for (TransactionEntity transactionEntity : tlist) {
      transactionEntity.setStatus(TransactionStatus.REVERSED);
      transactionEntity.setFinalizationDateTime(existingFinalizationTime);
    }

    when(readTransactionRepository.findByInstRefIdTransactionRefIdProfileRefIdAccountRefId(any(), any(), any(), any()))
        .thenReturn(tlist);

    // When - transaction is already reversed, should not save again
    this.mockMvc.perform(MockMvcRequestBuilders.delete(URL + "/" + instRefId + "/" + tranRefId)
            .headers(headers)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isNoContent())
        .andReturn();

    // Then - verify save was NOT called (transaction already reversed)
    verify(transactionRepository, org.mockito.Mockito.never()).save(any());
  }

  @Test
  public void reverseTransactionWithInvalidStatusShouldThrowException() throws Exception {
    // Given
    Account account = TestUtil.createAccount(accountRefId, profileRefId);
    when(validationService.getAccount(any(), any(), any())).thenReturn(account);
    when(validationService.isProfileAvailable(any(), any())).thenReturn(true);

    CommitLedgerTransactionResponse response1 = TestUtil.getCommitLedgerTransactionResponse();
    LedgerTransaction transaction = response1.getTransactions().get(0);
    String tranRefId = transaction.getTransactionRefId();
    String instRefId = response1.getInstructionRefId();

    InstructionEntity instructionEntity = TestUtil.createInstructionAndTransactionData(accountRefId, profileRefId);
    List<TransactionEntity> tlist = instructionEntity.getTransactions();

    // Set initial state - PENDING status (not POSTED or REVERSED)
    for (TransactionEntity transactionEntity : tlist) {
      transactionEntity.setStatus(TransactionStatus.PENDING);
      transactionEntity.setFinalizationDateTime(null);
    }

    when(readTransactionRepository.findByInstRefIdTransactionRefIdProfileRefIdAccountRefId(any(), any(), any(), any()))
        .thenReturn(tlist);

    // When & Then - should throw InvalidStatusTransitionException
    this.mockMvc.perform(MockMvcRequestBuilders.delete(URL + "/" + instRefId + "/" + tranRefId)
            .headers(headers)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();

    // Verify save was never called
    verify(transactionRepository, org.mockito.Mockito.never()).save(any());
  }
}