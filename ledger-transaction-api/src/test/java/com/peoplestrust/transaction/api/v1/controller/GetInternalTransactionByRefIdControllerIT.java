package com.peoplestrust.transaction.api.v1.controller;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.peoplestrust.transaction.api.v1.common.TestUtil;
import com.peoplestrust.transaction.api.v1.service.ValidationService;
import com.peoplestrust.transaction.persistence.entity.InstructionEntity;
import com.peoplestrust.transaction.persistence.entity.TransactionEntity;
import com.peoplestrust.transaction.persistence.repository.read.ReadTransactionRepository;
import com.peoplestrust.transaction.persistence.repository.write.InstructionRepository;
import com.peoplestrust.util.api.common.config.APICommonUtilConstant;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

@Slf4j
@ActiveProfiles("test")
@ExtendWith(MockitoExtension.class)
@SpringBootTest
@AutoConfigureMockMvc
public class GetInternalTransactionByRefIdControllerIT {

  private static final String URL = "/v1/ledger/transaction";
  private static final String URL_INTERNAL = "/v1/ledger/transaction/internal";
  private static final String requestId = UUID.randomUUID().toString();
  private static final String interactionId = UUID.randomUUID().toString();
  private static final String profileRefId = UUID.randomUUID().toString();
  private static final String accountRefId = UUID.randomUUID().toString();

  @Autowired
  private MockMvc mockMvc;

  @MockBean
  private ReadTransactionRepository readTransactionRepository;
  @MockBean
  private InstructionRepository instructionRepository;

  private HttpHeaders headers;
  private ObjectMapper objectMapper;

  @BeforeEach
  public void setupBeforeTest() {
    objectMapper = new ObjectMapper();
    objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    headers = new HttpHeaders();
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_ID, interactionId);
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString());
    headers.add(APICommonUtilConstant.HEADER_PROFILE_ID, profileRefId);
    headers.add(APICommonUtilConstant.HEADER_ACCOUNT_ID, accountRefId);
    headers.add(APICommonUtilConstant.AUTHORIZATION, TestUtil.JWT_TOKEN);
  }

  @Test
  public void getInternalTransactionByRefIdTest() throws Exception {
    // Create data in DB
    InstructionEntity instructionEntity = TestUtil.createInstructionAndTransactionData(accountRefId, profileRefId);
    when(instructionRepository.save(any())).thenReturn(instructionEntity);
    when(instructionRepository.findByProfileRefIdAndAccountRefIdAndInstructionRefId(any(), any(), any()))
        .thenReturn(instructionEntity);

    // Ensure transactions are initialized
    List<TransactionEntity> tlist = instructionEntity.getTransactions();

    when(readTransactionRepository.findByTransactionRefId(any())).thenReturn(tlist.get(0));

    // Now call the new API get success for each api
    if (instructionEntity.getTransactions() != null && !instructionEntity.getTransactions().isEmpty()) {
      for (TransactionEntity transactionEntity : instructionEntity.getTransactions()) {
        String transactionRefId = transactionEntity.getTransactionRefId();
        this.mockMvc.perform(MockMvcRequestBuilders.get(URL_INTERNAL + "/" + transactionRefId)
                .headers(headers)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andReturn();
      }
    }
  }
}
