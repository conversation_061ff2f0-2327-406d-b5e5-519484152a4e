package com.peoplestrust.transaction.api.v1.service;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.lenient;

import com.peoplestrust.scheduler.persistence.entity.BalanceEntity;
import com.peoplestrust.scheduler.persistence.repository.read.ReadBalanceRepository;
import com.peoplestrust.transaction.api.v1.config.TransactionProperty;
import com.peoplestrust.transaction.domain.model.RetrieveBalanceResponse;
import com.peoplestrust.transaction.persistence.repository.read.ReadTransactionRepository;
import com.peoplestrust.transaction.persistence.repository.write.InstructionRepository;
import com.peoplestrust.transaction.persistence.repository.write.TransactionRepository;
import java.math.BigDecimal;
import java.util.Optional;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.context.ActiveProfiles;

@ActiveProfiles("test")
@ExtendWith(MockitoExtension.class)
@Slf4j
public class RetrieveBalanceServiceTest {
  @Mock
  private ReadTransactionRepository readTransactionRepository;

  @Mock
  private InstructionRepository instructionRepository;

  @Mock
  private ReadBalanceRepository readBalanceRepository;

  @Mock
  private TransactionProperty transactionProperty;

  @Mock
  private TransactionRepository transactionRepository;

  @InjectMocks
  private TransactionServiceImpl transactionService;

  String accountId = UUID.randomUUID().toString();
  private static String profileId = UUID.randomUUID().toString();

  @BeforeEach
  public void setUp() {
    // Configure TransactionProperty mock
    when(transactionProperty.getUseStoreProcedureCalculateSum()).thenReturn(true);
  }

  @Test
  public void retrieveBalanceTest() throws Exception {
    BigDecimal fundHoldAmount = BigDecimal.valueOf(1000);
    BigDecimal credit = BigDecimal.valueOf(500);
    BigDecimal debit = BigDecimal.valueOf(300);
    BalanceEntity balanceEntity = getBalanceEntity();

    // Setup mocks
    when(readBalanceRepository.findFirstByAccountRefIdAndProfileRefIdOrderByCreatedDateTimeDesc(any(), any()))
        .thenReturn(Optional.of(balanceEntity));
    when(readTransactionRepository.getFundHoldAmount(any(), any(), any(), any()))
        .thenReturn(Optional.of(fundHoldAmount));

    // Use lenient to avoid unnecessary stubbing exception
    lenient().when(transactionRepository.sumAmountByDateRangeInstructionId(any(), any(), any(), any(), any()))
        .thenReturn(Optional.of(credit))
        .thenReturn(Optional.of(debit));

    // Also mock the readTransactionRepository method in case it's used instead
    lenient().when(readTransactionRepository.sumAmountByDateRange(any(), any(), any(), any()))
        .thenReturn(Optional.of(credit))
        .thenReturn(Optional.of(debit));

    RetrieveBalanceResponse amountVal = transactionService.retrieveAccountBalance(
        accountId, profileId, UUID.randomUUID().toString(), "100");
    assertNotNull(amountVal);
  }

  private BalanceEntity getBalanceEntity() {
    BalanceEntity balanceEntity = new BalanceEntity();
    balanceEntity.setTotalAmount(BigDecimal.valueOf(500));
    balanceEntity.setTotalReserveAmount(BigDecimal.valueOf(50));
    return balanceEntity;
  }

  @AfterEach
  public void doCleanUpAfterTest() {
    log.trace("cleanup - start");
    instructionRepository.findByProfileRefIdAndAccountRefId(
            UUID.fromString(profileId), UUID.fromString(accountId))
        .stream()
        .forEach(e -> instructionRepository.delete(e));
    log.trace("clean up - end");
  }
}