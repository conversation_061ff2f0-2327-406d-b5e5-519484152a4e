package com.peoplestrust.transaction.api.v1.service;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.peoplestrust.scheduler.persistence.repository.write.BalanceRepository;
import com.peoplestrust.transaction.api.v1.TransactionApplication;
import com.peoplestrust.transaction.api.v1.common.TestUtil;
import com.peoplestrust.transaction.api.v1.model.Account;
import com.peoplestrust.transaction.api.v1.model.Instruction;
import com.peoplestrust.transaction.api.v1.model.Transaction;
import com.peoplestrust.transaction.persistence.entity.PaymentCategoryType;
import com.peoplestrust.transaction.persistence.entity.PaymentRailType;
import com.peoplestrust.transaction.persistence.entity.TransactionFlowType;
import com.peoplestrust.transaction.persistence.entity.TransactionStatus;
import com.peoplestrust.transaction.persistence.repository.write.InstructionRepository;
import com.peoplestrust.transaction.persistence.repository.write.TransactionRepository;
import com.peoplestrust.util.api.common.util.DateUtils;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import net.bytebuddy.utility.RandomString;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@SpringBootTest
@ActiveProfiles("test")
@ContextConfiguration(classes = TransactionApplication.class)
@ExtendWith(SpringExtension.class)
@Slf4j
public class PrefundReserveFunctionalityServiceTestIT {


  private static String profileId = UUID.randomUUID().toString();
  private static String transactionRefId = UUID.randomUUID().toString();
  String accountId = UUID.randomUUID().toString();
  String interactionId = UUID.randomUUID().toString();

  @Autowired
  private TransactionRepository transactionRepository;

  @Autowired
  private TransactionServiceImpl transactionService;

  @Autowired
  private InstructionRepository instructionRepository;

  @Autowired
  BalanceRepository balanceRepository;

  @MockBean
  private ValidationService validationService;

  @Test
  public void getPrefundReserveBalance() throws Exception {

    Account account = TestUtil.createAccount(accountId, profileId);
    when(validationService.getAccount(any(), any(), any())).thenReturn(account);
    when(validationService.isProfileAvailable(any(), any())).thenReturn(true);
    when(validationService.validateProfileAndAccount(any(), any(), any())).thenReturn(true);

    Instruction instructionPrefund = createInstructionForPrefund();
    Instruction instruction = createInstruction();

    Instruction savedInstructionPrefund = transactionService.initiateReserve(instructionPrefund, profileId, accountId, interactionId);
    Instruction savedInstruction = transactionService.initiateInstruction(instruction, profileId, accountId, interactionId);
    BigDecimal balanceAmount = transactionService.getPrefundReserveBalanceAmount(accountId, profileId);
    assertNotNull(balanceAmount);
  }

  @Test
  public void getPrefundReserveAmount() throws Exception {

    Account account = TestUtil.createAccount(accountId, profileId);
    when(validationService.getAccount(any(), any(), any())).thenReturn(account);
    when(validationService.isProfileAvailable(any(), any())).thenReturn(true);
    when(validationService.validateProfileAndAccount(any(), any(), any())).thenReturn(true);

    Instruction instructionPrefund = createInstructionForPrefund();
    Instruction instruction = createInstruction();

    Instruction savedInstructionPrefund = transactionService.initiateReserve(instructionPrefund, profileId, accountId, interactionId);
    Instruction savedInstruction = transactionService.initiateInstruction(instruction, profileId, accountId, interactionId);
    BigDecimal usedAmount = transactionService.getPrefundReserveUsedAmount(accountId, profileId);
    assertNotNull(usedAmount);
  }

  @AfterEach
  public void doCleanUpAfterTest() {
    log.trace("cleanup - start");
    instructionRepository.findByProfileRefIdAndAccountRefId(UUID.fromString(profileId), UUID.fromString(accountId)).stream().
        forEach(e -> instructionRepository.delete(e));
    log.trace("clean up - end");
  }

  private Instruction createInstruction() {
    String word = "TEST_INST" + RandomString.make(7);
    List<Transaction> transactions = createTransactions(accountId, profileId);

    Instruction instruction = Instruction.builder()
        .instructionRefId(word)
        .accountRefId(accountId)
        .paymentRail(PaymentRailType.ETRANSFER)
        .profileRefId(profileId)
        .transactions(transactions)
        .build();
    instruction.setTransactions(transactions);
    return instruction;
  }

  private List<Transaction> createTransactions(String profileId, String accountId) {
    List<Transaction> list = new ArrayList<>();

    Transaction t1 = Transaction.builder()
        .transactionRefId(transactionRefId)
        .profileRefId(profileId)
        .accountRefId(accountId)
        .paymentCategory(PaymentCategoryType.SEND_PAYMENT)
        .status(TransactionStatus.PENDING)
        .amount(new BigDecimal(200))
        .monetaryUnit("CAD")
        .acceptanceDateTime(DateUtils.offsetDateTime())
        .dueDateTime(DateUtils.offsetDateTime().plusDays(2))
        .effectiveDateTime(DateUtils.offsetDateTime().plusDays(2))
        .build();

    Transaction t2 = Transaction.builder()
        .transactionRefId(UUID.randomUUID().toString())
        .profileRefId(profileId).accountRefId(accountId)
        .paymentCategory(PaymentCategoryType.COMPLETE_PAYMENT)
        .status(TransactionStatus.PENDING)
        .amount(new BigDecimal(400))
        .monetaryUnit("CAD")
        .acceptanceDateTime(DateUtils.offsetDateTime())
        .dueDateTime(DateUtils.offsetDateTime().plusDays(2))
        .effectiveDateTime(DateUtils.offsetDateTime().plusDays(2))
        .build();

    list.add(t1);
    list.add(t2);

    return list;
  }


  private Instruction createInstructionForPrefund() {
    String word = "TEST_INST" + RandomString.make(7) + RandomString.make(3);
    List<Transaction> transactions = createTransactionsForPrefund();

    Instruction instruction = Instruction.builder()
        .instructionRefId(word)
        .accountRefId(accountId)
        .paymentRail(PaymentRailType.INTERNAL)
        .profileRefId(profileId)
        .transactions(transactions)
        .build();
    instruction.setTransactions(transactions);
    return instruction;
  }

  private List<Transaction> createTransactionsForPrefund() {
    List<Transaction> list = new ArrayList<>();

    Transaction t1 = Transaction.builder()
        .transactionRefId(UUID.randomUUID().toString())
        .transactionFlow(TransactionFlowType.CREDIT)
        .paymentCategory(PaymentCategoryType.RESERVE)
        .amount(new BigDecimal(400))
        .monetaryUnit("CAD")
        .acceptanceDateTime(DateUtils.offsetDateTime())
        .build();

    list.add(t1);
    return list;
  }
}
