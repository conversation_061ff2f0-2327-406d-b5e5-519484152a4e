package com.peoplestrust.transaction.api.v1.service;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.peoplestrust.scheduler.persistence.repository.write.BalanceRepository;
import com.peoplestrust.transaction.api.v1.TransactionApplication;
import com.peoplestrust.transaction.api.v1.common.TestUtil;
import com.peoplestrust.transaction.api.v1.model.Account;
import com.peoplestrust.transaction.api.v1.model.Instruction;
import com.peoplestrust.transaction.api.v1.model.Transaction;
import com.peoplestrust.transaction.persistence.entity.PaymentCategoryType;
import com.peoplestrust.transaction.persistence.entity.PaymentRailType;
import com.peoplestrust.transaction.persistence.entity.TransactionFlowType;
import com.peoplestrust.transaction.persistence.repository.read.ReadTransactionRepository;
import com.peoplestrust.transaction.persistence.repository.write.InstructionRepository;
import com.peoplestrust.transaction.persistence.repository.write.TransactionRepository;
import com.peoplestrust.util.api.common.util.DateUtils;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import net.bytebuddy.utility.RandomString;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@SpringBootTest
@ActiveProfiles("test")
@ContextConfiguration(classes = TransactionApplication.class)
@ExtendWith(SpringExtension.class)
@Slf4j
public class FundHoldAmountFunctionalityServiceTestIT {


  @Autowired
  private TransactionRepository transactionRepository;
  @Autowired
  ReadTransactionRepository readTransactionRepository;

  @Autowired
  private TransactionServiceImpl transactionService;

  @Autowired
  private InstructionRepository instructionRepository;
  @Autowired
  BalanceRepository balanceRepository;
  @MockBean
  private ValidationService validationService;
  String accountId = UUID.randomUUID().toString();
  String interactionId = UUID.randomUUID().toString();
  private static String profileId = UUID.randomUUID().toString();
  private static String transactionRefId = UUID.randomUUID().toString();

  @Test
  public void getHoldAmount() throws Exception {

    Account account = TestUtil.createAccount(accountId, profileId);
    when(validationService.isProfileAvailable(any(), any())).thenReturn(true);
    when(validationService.getAccount(any(), any(), any())).thenReturn(account);
    when(validationService.validateProfileAndAccount(any(), any(), any())).thenReturn(true);

    Instruction instructionHold = createInstructionForHold();
    Instruction savedInstructionHold = transactionService.initiateInstruction(instructionHold, profileId, accountId, interactionId);
    Instruction postedHoldInstruction = transactionService.commitTransaction(instructionHold.getInstructionRefId(), profileId, accountId, interactionId);
    BigDecimal holdAmount = transactionService.getFundHoldAmount(accountId, profileId);
    assertNotNull(holdAmount);
  }


  @AfterEach
  public void doCleanUpAfterTest() {
    log.trace("cleanup - start");
    instructionRepository.findByProfileRefIdAndAccountRefId(UUID.fromString(profileId), UUID.fromString(accountId)).stream().
        forEach(e -> instructionRepository.delete(e));
    log.trace("clean up - end");
  }

  private Instruction createInstructionForHold() {
    UUID uuid = UUID.randomUUID();
    String word = "TEST_INST" + RandomString.make(7);
    List<Transaction> transactions = createTransactionsForHold();

    Instruction instruction = Instruction.builder().instructionRefId(word).accountRefId(accountId).paymentRail(PaymentRailType.EFT).profileRefId(profileId)
        .transactions(transactions).createdDateTime(DateUtils.offset()).updatedDateTime(DateUtils.offset()).build();
    instruction.setTransactions(transactions);
    return instruction;
  }

  private List<Transaction> createTransactionsForHold() {
    List<Transaction> list = new ArrayList<>();

    Transaction t1 = Transaction.builder().transactionRefId(UUID.randomUUID().toString()).transactionFlow(TransactionFlowType.CREDIT)
        .paymentCategory(PaymentCategoryType.DEBIT_PULL).amount(new BigDecimal(400)).monetaryUnit("CAD").acceptanceDateTime(DateUtils.offsetDateTime())
        .dueDateTime(DateUtils.offsetDateTime().plusDays(2)).build();

    Transaction t2 = Transaction.builder().transactionRefId(UUID.randomUUID().toString()).transactionFlow(TransactionFlowType.CREDIT)
        .paymentCategory(PaymentCategoryType.DEBIT_PULL).amount(new BigDecimal(100)).monetaryUnit("CAD").acceptanceDateTime(DateUtils.offsetDateTime())
        .dueDateTime(DateUtils.offsetDateTime().plusDays(2)).build();

    Transaction t3 = Transaction.builder().transactionRefId(UUID.randomUUID().toString()).transactionFlow(TransactionFlowType.CREDIT)
        .paymentCategory(PaymentCategoryType.DEBIT_PULL).amount(new BigDecimal(200)).monetaryUnit("CAD").acceptanceDateTime(DateUtils.offsetDateTime()).build();

    list.add(t1);
    list.add(t2);
    list.add(t3);

    return list;
  }

}
