package com.peoplestrust.transaction.api.v1.service;

import com.peoplestrust.scheduler.persistence.entity.BalanceEntity;
import com.peoplestrust.scheduler.persistence.repository.read.ReadBalanceRepository;
import com.peoplestrust.transaction.api.v1.common.TestUtil;
import com.peoplestrust.transaction.api.v1.config.TransactionProperty;
import com.peoplestrust.transaction.api.v1.exception.DuplicateTransactionException;
import com.peoplestrust.transaction.api.v1.mapper.TransactionMapper;
import com.peoplestrust.transaction.api.v1.model.Account;
import com.peoplestrust.transaction.api.v1.model.Instruction;
import com.peoplestrust.transaction.api.v1.model.Transaction;
import com.peoplestrust.transaction.api.v1.util.RailCategoryUtil;
import com.peoplestrust.transaction.persistence.entity.*;
import com.peoplestrust.transaction.persistence.repository.read.ReadTransactionRepository;
import com.peoplestrust.transaction.persistence.repository.write.InstructionRepository;
import com.peoplestrust.transaction.persistence.repository.write.TransactionRepository;
import com.peoplestrust.util.api.common.util.DateUtils;
import com.peoplestrust.util.api.common.util.Messages;
import java.lang.reflect.Method;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@Slf4j
public class InitiateInstructionServiceTest {


  @Mock
  private TransactionRepository transactionRepository;

  @Mock
  private ValidationService validationService;

  @InjectMocks
  private TransactionServiceImpl transactionService;

  @Mock
  private TransactionMapper transactionMapper;

  @Mock
  TransactionProperty transactionProperty;

  @Mock
  private InstructionRepository instructionRepository;

  @Mock
  private ReadBalanceRepository readBalanceRepository;

  @Mock
  private ReadTransactionRepository readTransactionRepository;

  private final String instructionRefId = UUID.randomUUID().toString();
  String accountId = UUID.randomUUID().toString();
  String interactionId = UUID.randomUUID().toString();
  private static String profileId = UUID.randomUUID().toString();

  private InstructionEntity pendingInstruction;
  private TransactionEntity pendingTransaction;

  @BeforeEach
  void setup() {
    pendingTransaction = TransactionEntity.builder()
        .transactionRefId(UUID.randomUUID().toString())
        .status(TransactionStatus.PENDING)
        .build();
    pendingInstruction = InstructionEntity.builder()
        .instructionRefId(instructionRefId)
        .status(InstructionStatus.PENDING)
        .transactions(List.of(pendingTransaction))
        .build();
  }

  @Test
  public void saveInstruction() throws Exception {
    Instruction instruction = createInstruction();
    Instruction retInstruction = createInstruction();
    Account account = TestUtil.createAccount(accountId, profileId);
    BalanceEntity balanceEntity = getBalanceEntity();
    InstructionEntity instructionEntity = getInstructionData(instruction);
    List<TransactionEntity> tlist = getTransactionEntities(instruction);
    when(instructionRepository.save(any())).thenReturn(instructionEntity);
    when(transactionRepository.saveAll(any())).thenReturn(tlist);
    when(validationService.isProfileAvailable(any(), any())).thenReturn(Boolean.TRUE);
    when(validationService.getAccount(any(), any(), any())).thenReturn(account);
    when(transactionMapper.fromInstructionToInstructionEntity(any())).thenReturn(instructionEntity);
    when(transactionMapper.fromInstructionEntityToInstruction(any())).thenReturn(retInstruction);
    when(transactionMapper.fromTransactionToTransactionEntity(any())).thenReturn(tlist.get(1));
    when(readBalanceRepository.findFirstByAccountRefIdAndProfileRefIdOrderByCreatedDateTimeDesc(any(), any())).thenReturn(Optional.of(balanceEntity));
    when(transactionProperty.getUseStoreProcedureCalculateSum()).thenReturn(Boolean.FALSE);
    Instruction instruction1 = transactionService.initiateInstruction(instruction, profileId, accountId, interactionId);
    assertEquals(retInstruction, instruction1);
  }

  private Instruction createInstruction() {
    List<Transaction> transactions = createTransactions();

    Instruction instruction = Instruction.builder().instructionRefId("TEST_INS" + UUID.randomUUID().toString()).accountRefId(accountId)
        .paymentRail(PaymentRailType.EFT).profileRefId(profileId).transactions(transactions)
        .createdDateTime(DateUtils.offset()).updatedDateTime(DateUtils.offset()).build();
    instruction.setTransactions(transactions);
    return instruction;
  }


  private BalanceEntity getBalanceEntity() {
    BalanceEntity balanceEntity = new BalanceEntity();
    balanceEntity.setTotalAmount(BigDecimal.valueOf(500));
    balanceEntity.setEffectiveToDateTime(LocalDateTime.now().minusDays(1));
    return balanceEntity;
  }

  private List<Transaction> createTransactions() {
    List<Transaction> list = new ArrayList<>();

    Transaction t1 = Transaction.builder().transactionRefId(UUID.randomUUID().toString()).transactionFlow(TransactionFlowType.CREDIT)
        .paymentCategory(PaymentCategoryType.DEBIT_PULL).amount(new BigDecimal(100)).monetaryUnit(String.valueOf(MonetaryUnit.CAD))
        .acceptanceDateTime(DateUtils.offsetDateTime())
        .dueDateTime(DateUtils.offsetDateTime().plusDays(2)).build();

    Transaction t2 = Transaction.builder().transactionRefId(UUID.randomUUID().toString()).transactionFlow(TransactionFlowType.DEBIT)
        .paymentCategory(PaymentCategoryType.CREDIT_PUSH).amount(new BigDecimal(100)).monetaryUnit(String.valueOf(MonetaryUnit.CAD))
        .acceptanceDateTime(DateUtils.offsetDateTime())
        .build();

    Transaction t3 = Transaction.builder().transactionRefId(UUID.randomUUID().toString()).transactionFlow(TransactionFlowType.CREDIT)
        .paymentCategory(PaymentCategoryType.DEBIT_PULL).amount(new BigDecimal(100)).monetaryUnit(String.valueOf(MonetaryUnit.CAD))
        .acceptanceDateTime(DateUtils.offsetDateTime()).build();

    list.add(t1);
    list.add(t2);
    list.add(t3);
    return list;
  }

  private InstructionEntity getInstructionData(Instruction instruction) {
    InstructionEntity instructionEntity = new InstructionEntity();

    instructionEntity.setInstructionRefId(instruction.getInstructionRefId());
    instructionEntity.setStatus(instruction.getStatus());
    instructionEntity.setAccountRefId(UUID.fromString(instruction.getAccountRefId()));
    instructionEntity.setPaymentRail(instruction.getPaymentRail());
    instructionEntity.setProfileRefId(UUID.fromString(instruction.getProfileRefId()));

    instructionEntity.setTransactions(getTransactionEntities(instruction));
    return instructionEntity;
  }

  public List<TransactionEntity> getTransactionEntities(Instruction instruction) {
    List<TransactionEntity> ts = new ArrayList<>();
    instruction.getTransactions().forEach(t -> {
      TransactionEntity te = TransactionEntity.builder().transactionRefId(t.getTransactionRefId())
          .acceptanceDateTime(t.getAcceptanceDateTime().toLocalDateTime())
          .transactionFlow(RailCategoryUtil.determineTransactionFlow(instruction.getPaymentRail(), t.getPaymentCategory()))
          .paymentCategory(t.getPaymentCategory()).transactionHold(TransactionHoldType.HOLD)
          .monetaryUnit(MonetaryUnit.valueOf(t.getMonetaryUnit())).amount(t.getAmount()).build();
      ts.add(te);
    });
    return ts;
  }

  @Test
  public void duplicateTransactionTest() throws Exception {
    // Create an instruction with transactions
    Instruction instruction = createInstruction();
    Account account = TestUtil.createAccount(accountId, profileId);

    // Mock validation service to return valid profile and account
    when(validationService.isProfileAvailable(any(), any())).thenReturn(Boolean.TRUE);
    when(validationService.getAccount(any(), any(), any())).thenReturn(account);

    // Mock the repository to return an existing transaction for the transaction ID check
    // This simulates finding an existing transaction with the same ID

    List<String> existingTransaction = new ArrayList<>();
    existingTransaction.add(UUID.randomUUID().toString());

    when(transactionRepository.findExistingTransactionRefs(any(), any(), any())).thenReturn(existingTransaction);

    // Verify that attempting to create a transaction with a duplicate ID throws the expected exception
    DuplicateTransactionException exception = assertThrows(DuplicateTransactionException.class, () -> {
      transactionService.initiateInstruction(instruction, profileId, accountId, interactionId);
    });

    // Verify the exception message matches our expected duplicate transaction message
    assertEquals(Messages.DUPLICATE_TRANSACTION, exception.getMessage());
  }

  @Test
  void shouldUpdateTransactionStatusAndSetFinalizationDateTimeForFailedTransactions() throws Exception {
    TransactionStatus failedStatus = TransactionStatus.FAILED;
    LocalDateTime updateTime = LocalDateTime.now();

    pendingInstruction.getTransactions().forEach(t -> t.setStatus(TransactionStatus.PENDING));

    when(transactionRepository.saveAll(any())).thenReturn(pendingInstruction.getTransactions());

    Method method = TransactionServiceImpl.class.getDeclaredMethod(
        "updateInitiateTransactions", Integer.class, TransactionStatus.class,
        List.class, LocalDateTime.class);
    method.setAccessible(true);

    List<TransactionEntity> updatedTransactions = (List<TransactionEntity>) method.invoke(
        transactionService, 1, failedStatus, pendingInstruction.getTransactions(), updateTime);

    assertEquals(TransactionStatus.FAILED, updatedTransactions.get(0).getStatus());
    assertNotNull(updatedTransactions.get(0).getFinalizationDateTime());
    assertEquals(updateTime, updatedTransactions.get(0).getUpdatedDateTime());
    verify(transactionRepository, times(1)).saveAll(any());
  }
  @Test
  void shouldNotSetFinalizationDateTimeForPendingTransactions() throws Exception {
    TransactionStatus pendingStatus = TransactionStatus.PENDING;
    LocalDateTime updateTime = LocalDateTime.now();

    pendingInstruction.getTransactions().forEach(t -> t.setStatus(TransactionStatus.PENDING));

    when(transactionRepository.saveAll(any())).thenReturn(pendingInstruction.getTransactions());

    Method method = TransactionServiceImpl.class.getDeclaredMethod(
        "updateInitiateTransactions", Integer.class, TransactionStatus.class,
        List.class, LocalDateTime.class);
    method.setAccessible(true);

    List<TransactionEntity> updatedTransactions = (List<TransactionEntity>) method.invoke(
        transactionService, 1, pendingStatus, pendingInstruction.getTransactions(), updateTime);

    assertEquals(TransactionStatus.PENDING, updatedTransactions.get(0).getStatus());
    assertNull(updatedTransactions.get(0).getFinalizationDateTime());
    assertEquals(updateTime, updatedTransactions.get(0).getUpdatedDateTime());
    verify(transactionRepository, times(1)).saveAll(any());
  }

  @AfterEach
  public void doCleanUpAfterTest() {
    log.trace("cleanup - start");
    instructionRepository.findByProfileRefIdAndAccountRefId(UUID.fromString(profileId), UUID.fromString(accountId)).stream().
        forEach(e -> instructionRepository.delete(e));
    log.trace("clean up - end");
  }
}
