package com.peoplestrust.transaction.api.v1.controller;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.peoplestrust.transaction.api.v1.common.TestUtil;
import com.peoplestrust.transaction.api.v1.model.Account;
import com.peoplestrust.transaction.api.v1.service.TransactionService;
import com.peoplestrust.transaction.api.v1.service.ValidationService;
import com.peoplestrust.transaction.domain.model.Metadata;
import com.peoplestrust.transaction.persistence.entity.InstructionEntity;
import com.peoplestrust.transaction.persistence.entity.PaymentRailType;
import com.peoplestrust.transaction.persistence.entity.TransactionEntity;
import com.peoplestrust.transaction.persistence.entity.TransactionMetadataEntity;
import com.peoplestrust.transaction.persistence.repository.write.TransactionMetadataRepository;
import com.peoplestrust.transaction.persistence.repository.write.TransactionRepository;
import com.peoplestrust.util.api.common.config.APICommonUtilConstant;
import com.peoplestrust.util.api.common.util.JsonUtil;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@Slf4j
@ActiveProfiles("test")
@ExtendWith(MockitoExtension.class)
@SpringBootTest
@AutoConfigureMockMvc
public class UpdateTransactionMetaDataIT {
  private static final String URL = "/v1/ledger/transaction/{instruction_ref_id}/{transaction_ref_id}/metadata";
  private static final String requestId = UUID.randomUUID().toString();
  private static final String interactionId = UUID.randomUUID().toString();
  private static final String profileRefId = UUID.randomUUID().toString();
  private static final String accountRefId = UUID.randomUUID().toString();
  private static final String instructionRefId = UUID.randomUUID().toString();
  private static final String transactionRefId = UUID.randomUUID().toString();
  @Autowired
  private MockMvc mockMvc;
  @MockBean
  private ValidationService validationService;
  @Autowired
  private TransactionService transactionService;
  @MockBean
  TransactionRepository transactionRepository;
  @MockBean
  TransactionMetadataRepository transactionMetadataRepository;
  private HttpHeaders headers;
  private ObjectMapper objectMapper;
  @BeforeEach
  public void setupBeforeTest() {
    objectMapper = new ObjectMapper();
    headers = new HttpHeaders();
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_ID, interactionId);
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString());
    headers.add(APICommonUtilConstant.HEADER_PROFILE_ID, profileRefId);
    headers.add(APICommonUtilConstant.HEADER_ACCOUNT_ID, accountRefId);
    headers.add(APICommonUtilConstant.AUTHORIZATION, TestUtil.JWT_TOKEN);
  }
  @Test
  public void updateTransactionMetaDataTest_invalid_payment_rail() throws Exception {
    Account account = TestUtil.createAccount(accountRefId, profileRefId);
    when(validationService.getAccount(any(), any(), any())).thenReturn(account);
    when(validationService.isProfileAvailable(any(), any())).thenReturn(true);

    // Create a sample metadata object
    Metadata metadata = new Metadata();
    metadata.setNotes("Sample note");
    metadata.setTicketId("12345");
    metadata.setTicketType(Metadata.TicketTypeEnum.fromValue("JIRA"));
    TransactionEntity transactionEntity = new TransactionEntity();
    InstructionEntity instructionEntity = new InstructionEntity();
    instructionEntity.setPaymentRail(PaymentRailType.ETRANSFER);
    transactionEntity.setInstruction(instructionEntity);
    when(transactionRepository.findByInstRefIdTransactionRefIdProfileRefIdAccountRefId(any(), any(), any(), any())).thenReturn(List.of(transactionEntity));
    // Perform the PUT request
    this.mockMvc.perform(MockMvcRequestBuilders.put(URL, instructionRefId, transactionRefId)
            .headers(headers)
            .content(JsonUtil.toString(metadata))
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest());
  }
  @Test
  public void updateTransactionMetaDataTest_new_metadata() throws Exception {
    Account account = TestUtil.createAccount(accountRefId, profileRefId);
    when(validationService.getAccount(any(), any(), any())).thenReturn(account);
    when(validationService.isProfileAvailable(any(), any())).thenReturn(true);

    // Create a sample metadata object
    Metadata metadata = new Metadata();
    metadata.setNotes("Sample note");
    metadata.setTicketId("12345");
    metadata.setTicketType(Metadata.TicketTypeEnum.fromValue("JIRA"));
    TransactionEntity transactionEntity = new TransactionEntity();
    InstructionEntity instructionEntity = new InstructionEntity();
    instructionEntity.setPaymentRail(PaymentRailType.INTERNAL);
    transactionEntity.setInstruction(instructionEntity);
    when(transactionRepository.findByInstRefIdTransactionRefIdProfileRefIdAccountRefId(any(), any(), any(), any())).thenReturn(List.of(transactionEntity));
    when(transactionMetadataRepository.findByTransactionEntity(any())).thenReturn((Optional.empty()));
    // Perform the PUT request
    this.mockMvc.perform(MockMvcRequestBuilders.put(URL, instructionRefId, transactionRefId)
            .headers(headers)
            .content(JsonUtil.toString(metadata))
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isCreated());
  }

  @Test
  public void updateTransactionMetaDataTest_update_metadata() throws Exception {
    Account account = TestUtil.createAccount(accountRefId, profileRefId);
    when(validationService.getAccount(any(), any(), any())).thenReturn(account);
    when(validationService.isProfileAvailable(any(), any())).thenReturn(true);

    // Create a sample metadata object
    Metadata metadata = new Metadata();
    metadata.setNotes("Sample note");
    metadata.setTicketId("12345");
    metadata.setTicketType(Metadata.TicketTypeEnum.fromValue("JIRA"));
    TransactionEntity transactionEntity = new TransactionEntity();
    InstructionEntity instructionEntity = new InstructionEntity();
    instructionEntity.setPaymentRail(PaymentRailType.INTERNAL);
    transactionEntity.setInstruction(instructionEntity);
    TransactionMetadataEntity transactionMetadataEntity = new TransactionMetadataEntity();
    when(transactionRepository.findByInstRefIdTransactionRefIdProfileRefIdAccountRefId(any(), any(), any(), any())).thenReturn(List.of(transactionEntity));
    when(transactionMetadataRepository.findByTransactionEntity(any())).thenReturn((Optional.of(transactionMetadataEntity)));
    // Perform the PUT request
    this.mockMvc.perform(MockMvcRequestBuilders.put(URL, instructionRefId, transactionRefId)
            .headers(headers)
            .content(JsonUtil.toString(metadata))
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isNoContent());
  }
}