package com.peoplestrust.transaction.api.v1.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.peoplestrust.scheduler.persistence.repository.write.BalanceRepository;
import com.peoplestrust.transaction.api.v1.TransactionApplication;
import com.peoplestrust.transaction.api.v1.common.TestUtil;
import com.peoplestrust.transaction.api.v1.model.Account;
import com.peoplestrust.transaction.api.v1.model.Instruction;
import com.peoplestrust.transaction.api.v1.model.Transaction;
import com.peoplestrust.transaction.persistence.entity.InstructionEntity;
import com.peoplestrust.transaction.persistence.entity.MonetaryUnit;
import com.peoplestrust.transaction.persistence.entity.PaymentCategoryType;
import com.peoplestrust.transaction.persistence.entity.PaymentRailType;
import com.peoplestrust.transaction.persistence.entity.TransactionEntity;
import com.peoplestrust.transaction.persistence.entity.TransactionFlowType;
import com.peoplestrust.transaction.persistence.entity.TransactionHoldType;
import com.peoplestrust.transaction.persistence.repository.write.InstructionRepository;
import com.peoplestrust.transaction.persistence.repository.write.TransactionRepository;
import com.peoplestrust.util.api.common.util.DateUtils;
import com.peoplestrust.util.api.common.util.Messages;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import net.bytebuddy.utility.RandomString;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@SpringBootTest
@ActiveProfiles("test")
@ContextConfiguration(classes = TransactionApplication.class)
@ExtendWith(SpringExtension.class)
@Slf4j
public class RetrieveInstructionServiceTestIT {


  @Autowired
  private TransactionRepository transactionRepository;

  @Autowired
  private TransactionServiceImpl transactionService;

  @Autowired
  private InstructionRepository instructionRepository;

  @Autowired
  BalanceRepository balanceRepository;

  @MockBean
  private ValidationService validationService;
  String accountId = UUID.randomUUID().toString();
  String interactionId = UUID.randomUUID().toString();
  private static String profileId = UUID.randomUUID().toString();


  @Test
  public void getTransaction() throws Exception {

    Account account = TestUtil.createAccount(accountId, profileId);
    when(validationService.getAccount(any(), any(), any())).thenReturn(account);
    when(validationService.isProfileAvailable(any(), any())).thenReturn(true);
    when(validationService.validateProfileAndAccount(any(), any(), any())).thenReturn(true);

    Instruction instruction = createInstruction();

    Instruction savedInstruction = transactionService.initiateInstruction(instruction, profileId, accountId, interactionId);
    Instruction retrievedInstruction = transactionService.getInstruction(profileId, accountId, instruction.getInstructionRefId(), interactionId);

    Exception retrievedInstructionMultipleInvalid = assertThrows(Exception.class, () -> {
      transactionService.getInstruction(UUID.randomUUID().toString(), UUID.randomUUID().toString(), instruction.getInstructionRefId(), interactionId);
    });
    assertTrue(retrievedInstructionMultipleInvalid.getMessage().contains(Messages.INVALID_INPUT));

    Exception retrievedInstructionProfileInvalid = assertThrows(Exception.class, () -> {
      transactionService.getInstruction(accountId, UUID.randomUUID().toString(), instruction.getInstructionRefId(), interactionId);
    });
    assertTrue(retrievedInstructionProfileInvalid.getMessage().contains(Messages.INVALID_INPUT));

    Exception retrievedInstructionAccountInvalid = assertThrows(Exception.class, () -> {
      transactionService.getInstruction(UUID.randomUUID().toString(), profileId, instruction.getInstructionRefId(), interactionId);
    });
    assertTrue(retrievedInstructionAccountInvalid.getMessage().contains(Messages.INVALID_INPUT));

    assertNotNull(savedInstruction);
    assertNotNull(retrievedInstruction);
    assertEquals(savedInstruction.getInstructionRefId(), retrievedInstruction.getInstructionRefId());
  }

  @AfterEach
  public void doCleanUpAfterTest() {
    log.trace("cleanup - start");
    instructionRepository.findByProfileRefIdAndAccountRefId(UUID.fromString(profileId), UUID.fromString(accountId)).stream().
        forEach(e -> instructionRepository.delete(e));
    log.trace("clean up - end");
  }

  private Instruction createInstruction() {
    String word = "TEST_INST" + RandomString.make(7);
    List<Transaction> transactions = createTransactions();

    Instruction instruction = Instruction.builder().instructionRefId(word).accountRefId(accountId).paymentRail(PaymentRailType.EFT)
        .profileRefId(profileId).transactions(transactions).createdDateTime(DateUtils.offset())
        .updatedDateTime(DateUtils.offset()).build();
    instruction.setTransactions(transactions);
    return instruction;
  }

  private List<Transaction> createTransactions() {
    List<Transaction> list = new ArrayList<>();

    Transaction t1 = Transaction.builder().transactionRefId(UUID.randomUUID().toString()).transactionFlow(TransactionFlowType.CREDIT)
        .paymentCategory(PaymentCategoryType.DEBIT_PULL).amount(new BigDecimal(100)).monetaryUnit(String.valueOf(MonetaryUnit.CAD))
        .acceptanceDateTime(DateUtils.offsetDateTime())
        .dueDateTime(DateUtils.offsetDateTime().plusDays(2)).build();

    Transaction t2 = Transaction.builder().transactionRefId(UUID.randomUUID().toString()).transactionFlow(TransactionFlowType.DEBIT)
        .paymentCategory(PaymentCategoryType.CREDIT_PUSH).amount(new BigDecimal(100)).monetaryUnit(String.valueOf(MonetaryUnit.CAD))
        .acceptanceDateTime(DateUtils.offsetDateTime())
        .build();

    Transaction t3 = Transaction.builder().transactionRefId(UUID.randomUUID().toString()).transactionFlow(TransactionFlowType.CREDIT)
        .paymentCategory(PaymentCategoryType.DEBIT_PULL).amount(new BigDecimal(100)).monetaryUnit(String.valueOf(MonetaryUnit.CAD))
        .acceptanceDateTime(DateUtils.offsetDateTime()).build();

    list.add(t1);
    list.add(t2);
    list.add(t3);
    return list;
  }

  public List<TransactionEntity> getTransactionEntities(Instruction instruction) {
    List<TransactionEntity> ts = new ArrayList<>();
    instruction.getTransactions().forEach(t -> {
      TransactionEntity te = TransactionEntity.builder().transactionRefId(t.getTransactionRefId())
          .acceptanceDateTime(t.getAcceptanceDateTime().toLocalDateTime())
          .transactionFlow(t.getTransactionFlow()).paymentCategory(t.getPaymentCategory()).transactionHold(TransactionHoldType.HOLD)
          .monetaryUnit(MonetaryUnit.valueOf(t.getMonetaryUnit())).amount(t.getAmount()).build();
      ts.add(te);
    });
    return ts;
  }
}
