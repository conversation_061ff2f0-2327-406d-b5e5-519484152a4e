package com.peoplestrust.transaction.api.v1.controller;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.peoplestrust.transaction.api.v1.common.TestUtil;
import com.peoplestrust.transaction.api.v1.model.Instruction;
import com.peoplestrust.transaction.domain.model.RetrieveTransactionsResponse;
import com.peoplestrust.transaction.persistence.entity.PaymentRailType;
import com.peoplestrust.transaction.persistence.entity.TransactionEntity;
import com.peoplestrust.transaction.persistence.repository.read.ReadInstructionRepository;
import com.peoplestrust.transaction.persistence.repository.read.ReadTransactionRepository;
import com.peoplestrust.util.api.common.config.APICommonUtilConstant;
import com.peoplestrust.util.api.common.config.ErrorProperty;
import com.peoplestrust.util.api.common.exception.ValidationException;
import com.peoplestrust.util.api.common.util.Messages;

import java.time.LocalDateTime;
import java.util.List;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.domain.SliceImpl;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.time.Instant;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@Slf4j
@ActiveProfiles("test")
@ExtendWith(MockitoExtension.class)
@SpringBootTest
@AutoConfigureMockMvc
public class SearchTransactionControllerIT {

  private static final String URL_SEARCH_Transaction = "/v1/ledger/transaction/search";
  private static final String interactionId = UUID.randomUUID().toString();
  private static final String profileRefId = UUID.randomUUID().toString();

  @Autowired
  private MockMvc mockMvc;

  @MockBean
  ReadInstructionRepository readInstructionRepository;

  @MockBean
  ReadTransactionRepository readTransactionRepository;

  private ObjectMapper objectMapper;
  private HttpHeaders headers;

  static {
    System.setProperty(APICommonUtilConstant.ENVIRONMENT_VARIABLE_DISABLE_COGNITO_JWT_VERIFICATION, "true");
  }

  @BeforeEach
  public void setupBeforeTest() {
    objectMapper = new ObjectMapper();
    objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    objectMapper.registerModule(new JavaTimeModule())
        .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
        .disable(DeserializationFeature.ADJUST_DATES_TO_CONTEXT_TIME_ZONE)
        .enable(SerializationFeature.WRITE_DATES_WITH_ZONE_ID);

    headers = new HttpHeaders();
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_ID, interactionId);
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString());
    headers.add(APICommonUtilConstant.AUTHORIZATION, TestUtil.JWT_TOKEN);
  }

  @Test
  void searchTransaction_validInput_success() throws Exception {
    RetrieveTransactionsResponse response = new RetrieveTransactionsResponse();

    String profileRefId = UUID.randomUUID().toString();
    PaymentRailType paymentRailType = PaymentRailType.ETRANSFER;
    LocalDateTime startDate = LocalDateTime.parse("2022-01-01T00:00:00");
    LocalDateTime endDate = LocalDateTime.parse("2022-12-31T23:59:59");

    Instruction instruction = TestUtil.createInstruction(profileRefId, UUID.randomUUID().toString());
    List<TransactionEntity> transactionEntities = TestUtil.getTransactionEntities(instruction);

    // Convert the List to a Slice
    Pageable pageable = PageRequest.of(0, 25);
    Slice<TransactionEntity> transactionSlice = new SliceImpl<>(transactionEntities, pageable, false);

    // Mock the repository call based on the updated logic
    when(readTransactionRepository.findByProfileRefIdAndPaymentRailAndDateRange(
        any(), any(), any(), any(), any())).thenReturn(transactionSlice);

    MvcResult result = mockMvc.perform(MockMvcRequestBuilders.post(URL_SEARCH_Transaction)
            .headers(headers)
            .param(APICommonUtilConstant.PROFILE_ID, profileRefId)
            .param(APICommonUtilConstant.PAYMENT_RAIL, paymentRailType.name())
            .param(APICommonUtilConstant.START_DATE, startDate.toString())
            .param(APICommonUtilConstant.END_DATE, endDate.toString())
            .param(APICommonUtilConstant.OFFSET, "0")
            .param(APICommonUtilConstant.MAX_ITEMS, "25")
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andReturn();

    String jsonResponse = result.getResponse().getContentAsString();
    RetrieveTransactionsResponse actualResponse = objectMapper.readValue(jsonResponse, RetrieveTransactionsResponse.class);
    assertEquals(3, actualResponse.getTransactions().size());
  }


  @Test
  void searchTransaction_invalidProfileId_failure() throws Exception {
    String invalidProfileId = "invalid-profile-id";

    MvcResult result = mockMvc.perform(MockMvcRequestBuilders.post(URL_SEARCH_Transaction)
            .headers(headers)
            .param(APICommonUtilConstant.PROFILE_ID, invalidProfileId)
            .param(APICommonUtilConstant.START_DATE, "2022-01-01T00:00:00")
            .param(APICommonUtilConstant.END_DATE, "2022-12-31T23:59:59")
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();
  }

  @Test
  void searchTransaction_missingRequiredHeader_failure() throws Exception {
    MvcResult result = mockMvc.perform(MockMvcRequestBuilders.post(URL_SEARCH_Transaction)
            .param(APICommonUtilConstant.PROFILE_ID, profileRefId)
            .param(APICommonUtilConstant.START_DATE, "2022-01-01T00:00:00")
            .param(APICommonUtilConstant.END_DATE, "2022-12-31T23:59:59")
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();
  }

  @Test
  void searchInstruction_MaxItemLessThan1_failure() throws Exception {
    PaymentRailType paymentRailType = PaymentRailType.ETRANSFER;

    MvcResult result = mockMvc.perform(MockMvcRequestBuilders.post(URL_SEARCH_Transaction)
            .headers(headers)
            .param(APICommonUtilConstant.PROFILE_ID, profileRefId)
            .param(APICommonUtilConstant.START_TIME, "2022-01-01T00:00:00")
            .param(APICommonUtilConstant.END_TIME, "2022-12-31T23:59:59")
            .param(APICommonUtilConstant.PAYMENT_RAIL, paymentRailType.name())
            .param(APICommonUtilConstant.OFFSET, "0")
            .param(APICommonUtilConstant.MAX_ITEMS, "0")
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();

    String jsonResponse = result.getResponse().getContentAsString();
    ValidationException error = objectMapper.readValue(jsonResponse, ValidationException.class);
    assertEquals(ErrorProperty.INVALID_PARAM.name(), error.getError().getCode());
  }

  @Test
  void searchInstruction_MaxItemMoreThan25_failure() throws Exception {
    PaymentRailType paymentRailType = PaymentRailType.ETRANSFER;

    MvcResult result = mockMvc.perform(MockMvcRequestBuilders.post(URL_SEARCH_Transaction)
            .headers(headers)
            .param(APICommonUtilConstant.PROFILE_ID, profileRefId)
            .param(APICommonUtilConstant.START_TIME, "2022-01-01T00:00:00")
            .param(APICommonUtilConstant.END_TIME, "2022-12-31T23:59:59")
            .param(APICommonUtilConstant.OFFSET, "0")
            .param(APICommonUtilConstant.PAYMENT_RAIL, paymentRailType.name())
            .param(APICommonUtilConstant.MAX_ITEMS, "260")
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();

    String jsonResponse = result.getResponse().getContentAsString();
    ValidationException error = objectMapper.readValue(jsonResponse, ValidationException.class);
    assertEquals(ErrorProperty.INVALID_PARAM.name(), error.getError().getCode());
  }


  @Test
  void searchInstruction_startTimeAfterEndTime_failure() throws Exception {
    PaymentRailType paymentRailType = PaymentRailType.ETRANSFER;

    MvcResult result = mockMvc.perform(MockMvcRequestBuilders.post(URL_SEARCH_Transaction)
            .headers(headers)
            .param(APICommonUtilConstant.PROFILE_ID, profileRefId)
            .param(APICommonUtilConstant.START_DATE, "2023-01-01T00:00:00")
            .param(APICommonUtilConstant.END_DATE, "2022-12-31T23:59:59")
            .param(APICommonUtilConstant.OFFSET,"10")
            .param(APICommonUtilConstant.PAYMENT_RAIL, paymentRailType.name())
            .param(APICommonUtilConstant.MAX_ITEMS,"25")
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();

    String jsonResponse = result.getResponse().getContentAsString();
    ValidationException error = objectMapper.readValue(jsonResponse, ValidationException.class);
    assertEquals(Messages.END_TIME_LESS_THAN_START_TIME, error.getError().getAdditionalInformation());
  }

  @Test
  void searchTransaction_missingPaymentRail_failure() throws Exception {
    MvcResult result = mockMvc.perform(MockMvcRequestBuilders.post(URL_SEARCH_Transaction)
            .headers(headers)
            .param(APICommonUtilConstant.PROFILE_ID, profileRefId)
            .param(APICommonUtilConstant.START_DATE, "2022-01-01T00:00:00")
            .param(APICommonUtilConstant.END_DATE, "2022-12-31T23:59:59")
            .param(APICommonUtilConstant.OFFSET, "0")
            .param(APICommonUtilConstant.MAX_ITEMS, "25")
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();

    String jsonResponse = result.getResponse().getContentAsString();
    ValidationException error = objectMapper.readValue(jsonResponse, ValidationException.class);
    Assertions.assertEquals(ErrorProperty.MISSING_PARAM.name(), error.getError().getCode());
  }

  @Test
  void searchTransaction_withInstructionRefId_success() throws Exception {
    RetrieveTransactionsResponse response = new RetrieveTransactionsResponse();

    String profileRefId = UUID.randomUUID().toString();
    String instructionRefId = "valid-instruction-id";
    PaymentRailType paymentRailType = PaymentRailType.ETRANSFER;
    LocalDateTime startDate = LocalDateTime.parse("2022-01-01T00:00:00");
    LocalDateTime endDate = LocalDateTime.parse("2022-12-31T23:59:59");

    Instruction instruction = TestUtil.createInstruction(profileRefId, instructionRefId);
    List<TransactionEntity> transactionEntities = TestUtil.getTransactionEntities(instruction);

    // Convert the List to a Slice
    Pageable pageable = PageRequest.of(0, 25);
    Slice<TransactionEntity> transactionSlice = new SliceImpl<>(transactionEntities, pageable, false);

    // Mock the repository call based on the updated logic
    when(readTransactionRepository.findByProfileRefIdAndPaymentRailAndDateRange(
        any(), any(), any(), any(LocalDateTime.class), any())).thenReturn(transactionSlice);

    MvcResult result = mockMvc.perform(MockMvcRequestBuilders.post(URL_SEARCH_Transaction)
            .headers(headers)
            .param(APICommonUtilConstant.PROFILE_ID, profileRefId)
            .param(APICommonUtilConstant.INSTRUCTION_REF_ID, instructionRefId)
            .param(APICommonUtilConstant.PAYMENT_RAIL, paymentRailType.name())
            .param(APICommonUtilConstant.START_DATE, startDate.toString())
            .param(APICommonUtilConstant.END_DATE, endDate.toString())
            .param(APICommonUtilConstant.OFFSET, "0")
            .param(APICommonUtilConstant.MAX_ITEMS, "25")
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andReturn();

    String jsonResponse = result.getResponse().getContentAsString();
    RetrieveTransactionsResponse actualResponse = objectMapper.readValue(jsonResponse, RetrieveTransactionsResponse.class);
    Assertions.assertEquals(3, actualResponse.getTransactions().size());
  }

  @Test
  void searchTransaction_withoutInstructionRefId_success() throws Exception {
    RetrieveTransactionsResponse response = new RetrieveTransactionsResponse();

    when(readInstructionRepository.findByProfileRefIdAndPaymentRail(any(), any(), any()))
        .thenReturn(TestUtil.getInstructionList(UUID.randomUUID().toString(), UUID.randomUUID().toString()));
    Instruction instruction = TestUtil.createInstruction(UUID.randomUUID().toString(), UUID.randomUUID().toString());

    List<TransactionEntity> transactionEntities = TestUtil.getTransactionEntities(instruction);

    // Convert the List to a Slice
    Pageable pageable = PageRequest.of(0, 25);
    Slice<TransactionEntity> transactionSlice = new SliceImpl<>(transactionEntities, pageable, false);

    // Mock the repository call based on the updated logic
    when(readTransactionRepository.findByProfileRefIdAndPaymentRailAndDateRange(
        any(), any(), any(), any(), any())).thenReturn(transactionSlice);

    MvcResult result = mockMvc.perform(MockMvcRequestBuilders.post(URL_SEARCH_Transaction)
            .headers(headers)
            .param(APICommonUtilConstant.PROFILE_ID, profileRefId)
            .param(APICommonUtilConstant.PAYMENT_RAIL, "ETRANSFER")
            .param(APICommonUtilConstant.START_DATE, "2022-01-01T00:00:00")
            .param(APICommonUtilConstant.END_DATE, "2022-12-31T23:59:59")
            .param(APICommonUtilConstant.OFFSET, "0")
            .param(APICommonUtilConstant.MAX_ITEMS, "25")
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andReturn();

    String jsonResponse = result.getResponse().getContentAsString();
    RetrieveTransactionsResponse actualResponse = objectMapper.readValue(jsonResponse, RetrieveTransactionsResponse.class);
    Assertions.assertEquals(3, actualResponse.getTransactions().size());
  }
  @Test
  void searchTransaction_missingOffset_failure() throws Exception {
    MvcResult result = mockMvc.perform(MockMvcRequestBuilders.post(URL_SEARCH_Transaction)
            .headers(headers)
            .param(APICommonUtilConstant.PROFILE_ID, profileRefId)
            .param(APICommonUtilConstant.PAYMENT_RAIL, "ETRANSFER")
            .param(APICommonUtilConstant.START_DATE, "2022-01-01T00:00:00")
            .param(APICommonUtilConstant.END_DATE, "2022-12-31T23:59:59")
            .param(APICommonUtilConstant.MAX_ITEMS, "25")
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();

    String jsonResponse = result.getResponse().getContentAsString();
    ValidationException error = objectMapper.readValue(jsonResponse, ValidationException.class);
    Assertions.assertEquals(ErrorProperty.MISSING_PARAM.name(), error.getError().getCode());
    Assertions.assertEquals("offset", error.getError().getAdditionalInformation());
  }
  @Test
  void searchTransaction_missingProfileId_failure() throws Exception {
    MvcResult result = mockMvc.perform(MockMvcRequestBuilders.post(URL_SEARCH_Transaction)
            .headers(headers)
            .param(APICommonUtilConstant.PAYMENT_RAIL, "ETRANSFER")
            .param(APICommonUtilConstant.START_DATE, "2022-01-01T00:00:00")
            .param(APICommonUtilConstant.END_DATE, "2022-12-31T23:59:59")
            .param(APICommonUtilConstant.OFFSET, "0")
            .param(APICommonUtilConstant.MAX_ITEMS, "25")
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();

    String jsonResponse = result.getResponse().getContentAsString();
    ValidationException error = objectMapper.readValue(jsonResponse, ValidationException.class);
    Assertions.assertEquals(ErrorProperty.MISSING_PARAM.name(), error.getError().getCode());
    Assertions.assertEquals("profile-id", error.getError().getAdditionalInformation());
  }

  @Test
  void searchTransaction_missingMaxItems_failure() throws Exception {
    MvcResult result = mockMvc.perform(MockMvcRequestBuilders.post(URL_SEARCH_Transaction)
            .headers(headers)
            .param(APICommonUtilConstant.PROFILE_ID, profileRefId)
            .param(APICommonUtilConstant.PAYMENT_RAIL, "ETRANSFER")
            .param(APICommonUtilConstant.START_DATE, "2022-01-01T00:00:00")
            .param(APICommonUtilConstant.END_DATE, "2022-12-31T23:59:59")
            .param(APICommonUtilConstant.OFFSET, "0")
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();

    String jsonResponse = result.getResponse().getContentAsString();
    ValidationException error = objectMapper.readValue(jsonResponse, ValidationException.class);
    Assertions.assertEquals(ErrorProperty.MISSING_PARAM.name(), error.getError().getCode());
    Assertions.assertEquals("max_items", error.getError().getAdditionalInformation());

  }

  @Test
  void searchTransaction_invalidPaymentRailEnum_failure() throws Exception {
    MvcResult result = mockMvc.perform(MockMvcRequestBuilders.post(URL_SEARCH_Transaction)
            .headers(headers)
            .param(APICommonUtilConstant.PROFILE_ID, profileRefId)
            .param(APICommonUtilConstant.PAYMENT_RAIL, "INTERNALs") // invalid enum
            .param(APICommonUtilConstant.START_DATE, "2022-01-01T00:00:00")
            .param(APICommonUtilConstant.END_DATE, "2022-12-31T23:59:59")
            .param(APICommonUtilConstant.OFFSET, "0")
            .param(APICommonUtilConstant.MAX_ITEMS, "25")
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();

    String jsonResponse = result.getResponse().getContentAsString();
    JsonNode json = objectMapper.readTree(jsonResponse);
    JsonNode errorNode = json.get("error");

    Assertions.assertEquals("INVALID_PARAM", errorNode.get("code").asText());
    Assertions.assertEquals("Invalid value 'INTERNALs' for enum 'PaymentRailType'",
        errorNode.get("additional_information").asText());
  }
  @Test
  void searchTransaction_invalidOffset_failure() throws Exception {
    MvcResult result = mockMvc.perform(MockMvcRequestBuilders.post(URL_SEARCH_Transaction)
            .headers(headers)
            .param(APICommonUtilConstant.PROFILE_ID, profileRefId)
            .param(APICommonUtilConstant.PAYMENT_RAIL, "ETRANSFER")
            .param(APICommonUtilConstant.START_DATE, "2022-01-01T00:00:00")
            .param(APICommonUtilConstant.END_DATE, "2022-12-31T23:59:59")
            .param(APICommonUtilConstant.OFFSET, "-1")
            .param(APICommonUtilConstant.MAX_ITEMS, "25")
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();

    String jsonResponse = result.getResponse().getContentAsString();
    ValidationException error = objectMapper.readValue(jsonResponse, ValidationException.class);
    Assertions.assertEquals(ErrorProperty.INVALID_PARAM.name(), error.getError().getCode());
    Assertions.assertEquals("offset is invalid input", error.getError().getAdditionalInformation());
  }
  @Test
  void searchTransaction_invalidMaxItems_failure() throws Exception {
    MvcResult result = mockMvc.perform(MockMvcRequestBuilders.post(URL_SEARCH_Transaction)
            .headers(headers)
            .param(APICommonUtilConstant.PROFILE_ID, profileRefId)
            .param(APICommonUtilConstant.PAYMENT_RAIL, "ETRANSFER")
            .param(APICommonUtilConstant.START_DATE, "2022-01-01T00:00:00")
            .param(APICommonUtilConstant.END_DATE, "2022-12-31T23:59:59")
            .param(APICommonUtilConstant.OFFSET, "0")
            .param(APICommonUtilConstant.MAX_ITEMS, "0")
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();

    String jsonResponse = result.getResponse().getContentAsString();
    ValidationException error = objectMapper.readValue(jsonResponse, ValidationException.class);
    Assertions.assertEquals(ErrorProperty.INVALID_PARAM.name(), error.getError().getCode());
    Assertions.assertEquals("max_items is invalid input", error.getError().getAdditionalInformation());
  }

}
