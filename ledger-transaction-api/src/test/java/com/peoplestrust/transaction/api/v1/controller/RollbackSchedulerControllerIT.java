package com.peoplestrust.transaction.api.v1.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.peoplestrust.transaction.api.v1.common.TestUtil;
import com.peoplestrust.transaction.api.v1.model.Account;
import com.peoplestrust.transaction.api.v1.model.Instruction;
import com.peoplestrust.transaction.api.v1.service.ValidationService;
import com.peoplestrust.transaction.persistence.entity.InstructionEntity;
import com.peoplestrust.transaction.persistence.entity.InstructionStatus;
import com.peoplestrust.transaction.persistence.entity.TransactionStatus;
import com.peoplestrust.transaction.persistence.repository.write.InstructionRepository;
import com.peoplestrust.util.api.common.config.APICommonUtilConstant;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import com.peoplestrust.transaction.persistence.repository.read.ReadInstructionRepository;

import java.time.Instant;
import java.util.Objects;
import java.util.UUID;

@Slf4j
@ActiveProfiles("test")
@ExtendWith(MockitoExtension.class)
@SpringBootTest
@AutoConfigureMockMvc
public class RollbackSchedulerControllerIT {

  private static final String URL_INTERNAL_ROLLBACK = "/v1/internal/ledger/transaction/rollback";
  private static final String requestId = UUID.randomUUID().toString();
  private static final String interactionId = UUID.randomUUID().toString();
  private static final String profileRefId = UUID.randomUUID().toString();
  private static final String accountRefId = UUID.randomUUID().toString();

  @Autowired
  private MockMvc mockMvc;

  @MockBean
  private ValidationService validationService;

  @MockBean
  private InstructionRepository instructionRepository;
  @MockBean
  private ReadInstructionRepository readInstructionRepository;
  private HttpHeaders headers;
  private ObjectMapper objectMapper;
  private InstructionEntity instructionEntity;

  static {
    System.setProperty(APICommonUtilConstant.ENVIRONMENT_VARIABLE_DISABLE_COGNITO_JWT_VERIFICATION, "true");
  }

  @BeforeEach
  public void setupBeforeTest() {
    objectMapper = new ObjectMapper();
    objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    headers = new HttpHeaders();
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_ID, interactionId);
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString());
    headers.add(APICommonUtilConstant.HEADER_PROFILE_ID, profileRefId);
    headers.add(APICommonUtilConstant.HEADER_ACCOUNT_ID, accountRefId);
    headers.add(APICommonUtilConstant.AUTHORIZATION, TestUtil.JWT_TOKEN);
    instructionEntity = TestUtil.createInstructionAndTransactionData(accountRefId, profileRefId);
    instructionRepository.save(instructionEntity);
  }

  @Test
  public void rollBackSchedulerTest() throws Exception {
    Account account = TestUtil.createAccount(accountRefId, profileRefId);
    when(validationService.getAccount(any(), any(), any())).thenReturn(account);
    when(validationService.isProfileAvailable(any(), any())).thenReturn(true);

    String insRefId = instructionEntity.getInstructionRefId().toString();

    Instruction instruction = TestUtil.createInstruction(accountRefId, profileRefId);
    InstructionEntity instructionEntity=TestUtil.getInstructionData(instruction);
    //Change transaction status to pending
    for(int i=0;i<instructionEntity.getTransactions().size();i++)
    {
      instructionEntity.getTransactions().get(i).setStatus(TransactionStatus.PENDING);
    }
    instructionEntity.setStatus(InstructionStatus.PENDING);

    when(readInstructionRepository.findByProfileRefIdAndAccountRefIdAndInstructionRefId(any(), any(),any())).thenReturn(instructionEntity);
    when(instructionRepository.save(any())).thenAnswer(invocation -> invocation.getArgument(0));

    MvcResult result = this.mockMvc.perform(
            MockMvcRequestBuilders.delete(URL_INTERNAL_ROLLBACK + "/" + insRefId)
                .headers(headers)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(content().string("true"))
        .andExpect(status().isOk())
        .andReturn();

    Assertions.assertNotNull(result);
  }
}
