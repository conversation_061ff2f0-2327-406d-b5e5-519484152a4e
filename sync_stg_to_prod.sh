#!/usr/bin/env bash
#
# sync_stg_to_prod.sh
# Promote 14-digit image tags from STAGING to PRODUCTION Deployment YAMLs
# – keeps formatting (plain-text replacement only)
# – matches by image **name** (repo/registry may differ)
#
# Usage:
#   ./sync_stg_to_prod.sh                      # default paths
#   ./sync_stg_to_prod.sh path/to/stg path/to/prod
#
set -euo pipefail

STG_DIR="${1:-environments/K8s_STAGING}"
PROD_DIR="${2:-environments/K8s_PRODUCTION}"

python3 - <<'PY' "$STG_DIR" "$PROD_DIR"
import sys, re, pathlib, shutil

stg_dir  = pathlib.Path(sys.argv[1])
prod_dir = pathlib.Path(sys.argv[2])

TAG_14 = r"\d{14}"

# capture: full repo path, image name, tag
IMG_RE = re.compile(
    rf"^\s*image:\s+(?P<full>.+?/(?P<name>[^/:]+)):(?P<tag>{TAG_14})\s*$",
    re.M,
)

DD_RE = re.compile(
    rf'^(\s*tags\.datadoghq\.com/version:\s*)["\']?{TAG_14}["\']?(\s*)$',
    re.M,
)

def sync_deployment(stg_file: pathlib.Path) -> None:
    rel = stg_file.relative_to(stg_dir)
    prod_file = prod_dir / rel
    prod_file.parent.mkdir(parents=True, exist_ok=True)

    stg_txt = stg_file.read_text()
    matches = IMG_RE.findall(stg_txt)      # list of tuples
    if not matches:
        print(f"⚠️  {rel} – no image tag found, skipped")
        return

    if not prod_file.exists():
        shutil.copy2(stg_file, prod_file)  # seed prod file if missing

    prod_txt_orig = prod_file.read_text()
    prod_txt = prod_txt_orig

    for _full, image_name, tag in matches:
        # 1) replace ANY line that ends with /<image_name>:<14digits>
        prod_txt = re.sub(
            rf"(^\s*image:\s+.*?/{re.escape(image_name)}:){TAG_14}(\s*$)",
            rf"\g<1>{tag}\2",
            prod_txt,
            flags=re.M,
        )
        # 2) update Datadog labels
        prod_txt = DD_RE.sub(rf'\1"{tag}"\2', prod_txt)

    if prod_txt != prod_txt_orig:
        prod_file.write_text(prod_txt)
        print(f"✅  {rel} updated → tag {matches[0][2]}")
    else:
        print(f"➖  {rel} already up-to-date")

# main loop
for stg_yaml in stg_dir.rglob("k8s-*deployment.yaml"):
    sync_deployment(stg_yaml)
PY
