package com.peoplestrust.util.api.common.mapper;

import static com.peoplestrust.util.api.common.util.Utils.asByteArray;

import com.peoplestrust.util.api.common.config.APICommonUtilConstant;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.temporal.ChronoUnit;
import java.util.Base64;
import java.util.UUID;
import org.mapstruct.Named;

public class CommonMapper {

  /**
   * Utility function for UUID handling; converts a string to UUID.
   *
   * @param uuid uuid
   * @return
   */
  @Named(value = "stringToUUID")
  protected UUID stringToUUID(String uuid) {
    return UUID.fromString(uuid);
  }

  /**
   * Utility function for UUID handling; converts UUID to a string.
   *
   * @param uuid uuid
   * @return
   */
  @Named(value = "uuidToString")
  protected String uuidToString(UUID uuid) {
    Base64.getEncoder().encode(asByteArray(uuid));
    return uuid.toString();
  }

  /**
   * Utility function for date handling; converts local date to offset date.
   *
   * @param dt
   * @return
   */
  @Named("dateTimeToOffSet")
  public static OffsetDateTime dateTimeToOffSet(LocalDateTime dt) {
    if (dt == null) {
      return null;
    }
    // truncate (or round down) to the nearest millisecond
    LocalDateTime millisOnly = dt.truncatedTo(ChronoUnit.MILLIS);
    return millisOnly.atOffset(ZoneOffset.UTC);
  }

  /**
   * Utility function for date handling; Utility to map offset date time to UTC time zone
   *
   * @param dt
   * @return
   */
  @Named(value = "offSetToOffSet")
  public static OffsetDateTime offSetToOffSet(OffsetDateTime dt) {
    if (dt != null) {
      return dt.atZoneSameInstant(ZoneId.of(APICommonUtilConstant.UTC)).toOffsetDateTime();
    } else {
      return null;
    }
  }

  /**
   * Utility function for date handling; Utility to map offset date time to local date time
   *
   * @param offsetDateTime
   * @return
   */
  public static LocalDateTime toLocalDateTime(OffsetDateTime offsetDateTime) {
    if (offsetDateTime == null) {
      return null;
    }
    return offsetDateTime.toLocalDateTime();
  }
}