package com.peoplestrust.schedulers.controller;

import com.peoplestrust.account.persistence.entity.AccountEntity;
import com.peoplestrust.account.persistence.repository.read.ReadAccountRepository;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest
@Slf4j
@ActiveProfiles("test")
@AutoConfigureMockMvc
public class BalanceSnapshotControllerTest {

  @MockBean
  ReadAccountRepository readAccountRepository;
  @Autowired
  private MockMvc mockMvc;

  @Test
  public void postBalanceSnapshot_success() throws Exception {
    AccountEntity accountEntity = new AccountEntity();
    List<AccountEntity> accountEntities = new ArrayList<>();
    accountEntities.add(accountEntity);
    when(readAccountRepository.findAll()).thenReturn(accountEntities);
    this.mockMvc.perform(MockMvcRequestBuilders.post("/v1/util/balance/snapshots"))
        .andDo(print())
        .andExpect(status().isOk());
  }

  @Test

  public void postBalanceSnapshot_NoAcccounts() throws Exception {
    when(readAccountRepository.findAll()).thenReturn(null);
    this.mockMvc.perform(MockMvcRequestBuilders.post("/v1/util/balance/snapshots"))
        .andDo(print())
        .andExpect(status().isOk());

  }

}