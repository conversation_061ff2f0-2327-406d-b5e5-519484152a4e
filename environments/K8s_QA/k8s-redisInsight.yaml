apiVersion: apps/v1
kind: Deployment
metadata:
  name: redisinsight-deployment #deployment name
  namespace: pg-ledger-qa
  labels:
    app: redisinsight #deployment label
spec:
  replicas: 1 #a single replica pod
  selector:
    matchLabels:
      app: redisinsight #which pods is the deployment managing, as defined by the pod template
  template: #pod template
    metadata:
      labels:
        app: redisinsight #label for pod/s
    spec:
      containers:
        - name:  redisinsight #Container name (DNS_LABEL, unique)
          image: redislabs/redisinsight:latest #repo/image
          imagePullPolicy: IfNotPresent #Always pull image
          env:
            - name: RIHOST
              value: "0.0.0.0"
            - name: RIPORT
              value: "8001"
          volumeMounts:
            - name: db #Pod volumes to mount into the container's filesystem. Cannot be updated.
              mountPath: /db
          ports:
            - containerPort: 8001 #exposed container port and protocol
              protocol: TCP
          livenessProbe:
            httpGet:
              path : /healthcheck/ # exposed RI endpoint for healthcheck
              port: 8001 # exposed container port
            initialDelaySeconds: 5 # number of seconds to wait after the container starts to perform liveness probe
            periodSeconds: 5 # period in seconds after which liveness probe is performed
            failureThreshold: 1 # number of liveness probe failures after which container restarts
      volumes:
        - name: db
          emptyDir: {} # node-ephemeral volume https://kubernetes.io/docs/concepts/storage/volumes/#emptydir
