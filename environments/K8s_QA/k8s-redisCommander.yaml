apiVersion: apps/v1
kind: Deployment
metadata:
  name: rediscommander-deployment
  namespace: pg-ledger-qa
  labels:
    app: rediscommander
spec:
  replicas: 1
  selector:
    matchLabels:
      app: rediscommander
  template:
    metadata:
      labels:
        app: rediscommander
    spec:
      containers:
        - name: rediscommander
          image: rediscommander/redis-commander:latest
          ports:
            - containerPort: 8081
          env:
            - name: REDIS_HOST
              value: 'gl-replication-group-qa.b6qume.clustercfg.cac1.cache.amazonaws.com'
            - name: REDIS_PORT
              value: '6379'
            - name: REDIS_DB
              value: '0'