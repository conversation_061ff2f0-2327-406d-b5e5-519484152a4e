# Namespace-scoped Role with minimal required permissions
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: lambda-job-creator-role
  namespace: pg-ledger-qa
rules:
  - apiGroups: ["batch"]
    resources: ["cronjobs"]
    verbs: ["get", "list"]
  - apiGroups: ["batch"]
    resources: ["jobs"]
    verbs: ["create", "get", "list", "watch"]
  - apiGroups: [""]
    resources: ["pods"]
    verbs: ["get", "list"]
---
# Direct USER binding (not group)
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: lambda-job-creator-direct-binding
  namespace: pg-ledger-qa
subjects:
  - apiGroup: rbac.authorization.k8s.io
    kind: User
    name: lambda-job-creator
roleRef:
  kind: Role
  name: lambda-job-creator-role
  apiGroup: rbac.authorization.k8s.io