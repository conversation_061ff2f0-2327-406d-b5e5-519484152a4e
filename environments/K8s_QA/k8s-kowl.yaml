apiVersion: apps/v1
kind: Deployment
metadata:
  name: kowl-deployment
  namespace: pg-ledger-qa
  labels:
    app: kowl-v1
spec:
  replicas: 1
  selector:
    matchLabels:
      app: kowl-v1
  template:
    metadata:
      labels:
        app: kowl-v1
    spec:
      containers:
        - name: query-container
          image: docker.redpanda.com/redpandadata/console:v2.5.1
          ports:
            - containerPort: 8080
          env:
            - name: TZ
              value: 'America/Toronto'
            - name: KAFKA_BROKERS
              value: 'b-2.glkafkaclusterqa.a7wk7h.c4.kafka.ca-central-1.amazonaws.com:9096'
            - name: KAFKA_SASL_ENABLED
              value: 'true'
            - name: KAFKA_SASL_USERNAME
              valueFrom:
                secretKeyRef:
                  name: kafka
                  key: sys.kafka.sasl.username
            - name: K<PERSON><PERSON>_SASL_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: kafka
                  key: sys.kafka.sasl.password
            - name: KAFKA_SASL_HANDSHAKE
              value: 'true'
            - name: KAFKA_SASL_MECHANISM
              value: SCRAM-SHA-512
            - name: KAFKA_TLS_ENABLED
              value: 'true'
