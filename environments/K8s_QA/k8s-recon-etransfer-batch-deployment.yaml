apiVersion: apps/v1
kind: Deployment
metadata:
  name: recon-etransfer-batch-deployment
  namespace: pg-ledger-qa
  labels:
    app: recon-etransfer-batch-v1
    domain: general-ledger
    tags.datadoghq.com/env: qa
    tags.datadoghq.com/service: gl-recon-etransfer-batch-v1
    tags.datadoghq.com/version: "20250514117205"
spec:
  replicas: 1
  selector:
    matchLabels:
      app: recon-etransfer-batch-v1
  template:
    metadata:
      annotations:
        admission.datadoghq.com/python-lib.version: latest
      labels:
        app: recon-etransfer-batch-v1
        admission.datadoghq.com/enabled: "false"
        domain: general-ledger
        tags.datadoghq.com/env: qa
        tags.datadoghq.com/service: gl-recon-etransfer-batch-v1
        tags.datadoghq.com/version: "20250514117205"
    spec:
      containers:
        - name: recon-etransfer-batch-container
          image: 799455639446.dkr.ecr.ca-central-1.amazonaws.com/recon-etransfer-batch:20250722093350
          resources:
            requests:
              memory: "384Mi"
              cpu: "100m"
            limits:
              memory: "512Mi"
              cpu: "400m"
          ports:
            - containerPort: 8080
          # Startup probe has higher priority over the two other probe types. Until the Startup Probe succeeds, all the other Probes are disabled.
          startupProbe:
            httpGet:
              path: /actuator/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 10
            timeoutSeconds: 2
            periodSeconds: 15
            successThreshold: 1
            failureThreshold: 20
          # Kubelet uses liveness probes to know when to restart a container. If the liveness probe fails, the kubelet kills the container, and the container is subjected to its restart policy.
          livenessProbe:
            httpGet:
              path: /actuator/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 20
            timeoutSeconds: 30
            periodSeconds: 300
            successThreshold: 1
            failureThreshold: 3
          # Kubernetes makes sure the readiness probe passes before allowing a service to send traffic to the pod. Unlike a liveness probe, a readiness probe doesn’t kill the container. If the readiness probe fails, Kubernetes simply hides the container’s Pod from corresponding Services, so that no traffic is redirected to it.
          readinessProbe:
            httpGet:
              path: /actuator/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 20
            timeoutSeconds: 2
            periodSeconds: 5
            successThreshold: 1
            failureThreshold: 3
          env:
            - name: TZ
              value: 'America/Toronto'
            - name: TRANSACTION_DB_SCHEMA
              valueFrom:
                secretKeyRef:
                  name: transaction-database
                  key: sys.database.schema
            - name: READ_DB_HOST
              valueFrom:
                secretKeyRef:
                  name: transaction-database
                  key: sys.database.read.host
            - name: READ_DB_PORT
              value: '5432'
            - name: READ_DB_NAME
              valueFrom:
                secretKeyRef:
                  name: transaction-database
                  key: sys.database.name
            - name: READ_DB_USERNAME
              valueFrom:
                secretKeyRef:
                  name: transaction-database
                  key: sys.database.username
            - name: READ_DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: transaction-database
                  key: sys.database.password
            - name: S3_BUCKET
              valueFrom:
                secretKeyRef:
                  name: gl-recon-aws
                  key: sys.pg.gl.aws.bucket
            - name: S3_INPUT_PREFIX
              valueFrom:
                secretKeyRef:
                  name: gl-recon-aws
                  key: sys.pg.gl.aws.input.prefix
            - name: S3_FAILED_PREFIX
              valueFrom:
                secretKeyRef:
                  name: gl-recon-aws
                  key: sys.pg.gl.aws.failed.prefix
            - name: S3_ARCHIVE_PREFIX
              valueFrom:
                secretKeyRef:
                  name: gl-recon-aws
                  key: sys.pg.gl.aws.archive.prefix
            - name: AWS_REGION
              valueFrom:
                secretKeyRef:
                  name: gl-recon-aws
                  key: sys.pg.gl.aws.region
            - name: S3_POLL_INTERVAL
              valueFrom:
                secretKeyRef:
                  name: gl-recon-aws
                  key: sys.pg.gl.aws.s3.poll.interval
            - name: AWS_ACCESS_KEY_ID
              valueFrom:
                secretKeyRef:
                  name: gl-recon-aws
                  key: sys.pg.gl.aws.key
            - name: AWS_SECRET_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: gl-recon-aws
                  key: sys.pg.gl.aws.secret