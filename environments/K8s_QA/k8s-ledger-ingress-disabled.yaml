---
apiVersion: v1
kind: ConfigMap
metadata:
  name: dummy-nginx-config
  namespace: pg-ledger-qa
data:
  default.conf: |
    server {
        listen 80;

        default_type application/json;

        error_page 503 = @maintenance;

        location / {
            return 503;
        }

        location @maintenance {
            return 503 '{"status":503,"code":"ERR-5030","details":{"message":"The system is undergoing maintenance."}}';
        }
    }
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: dummy
  namespace: pg-ledger-qa
spec:
  replicas: 1
  selector:
    matchLabels:
      app: dummy
  template:
    metadata:
      labels:
        app: dummy
    spec:
      containers:
        - name: nginx
          image: nginx:alpine
          volumeMounts:
            - name: nginx-config
              mountPath: /etc/nginx/conf.d
      volumes:
        - name: nginx-config
          configMap:
            name: dummy-nginx-config
---
apiVersion: v1
kind: Service
metadata:
  name: dummy-service
  namespace: pg-ledger-qa
spec:
  ports:
    - port: 80
      targetPort: 80
  selector:
    app: dummy
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ledger-ingress
  namespace: pg-ledger-qa
  annotations:
    kubernetes.io/ingress.class: alb
    alb.ingress.kubernetes.io/scheme: internal
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/group.name: shared
    alb.ingress.kubernetes.io/group.order: '5'
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 8080}]'
    alb.ingress.kubernetes.io/healthcheck-path: /actuator/health
spec:
  rules: []
  defaultBackend:
    service:
      name: dummy-service
      port:
        number: 80
