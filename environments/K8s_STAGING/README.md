#

## Install K8s dashboard
Note: Grab latest version URL from https://github.com/kubernetes/dashboard

kubectl apply -f https://raw.githubusercontent.com/kubernetes/dashboard/v2.7.0/aio/deploy/recommended.yaml

## Configure admin user and roles
vi k8s-dashboard-admin-user.yaml

apiVersion: v1
kind: ServiceAccount
metadata:
  name: admin-user
  namespace: kubernetes-dashboard



vi k8s-dashboard-role.yaml

apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: admin-user
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: cluster-admin
subjects:
- kind: ServiceAccount
  name: admin-user
  namespace: kubernetes-dashboard


## Create proxy for PosgreSQL
### Copy sample k8s-proxy-postgres.yml
cp ../K8s_STAGING/k8s-proxy-postgres.yml .

### Update proxy info with <PERSON>, update namespace if needed
vi k8s-proxy-postgres.yml

### Create mgmt namespace
kubectl create namespace pg-ledger-mgmt

### Load it
kubectl apply -f k8s-proxy-postgres.yml
