apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ledger-ingress
  namespace: pg-ledger-staging
  annotations:
    kubernetes.io/ingress.class: alb
    alb.ingress.kubernetes.io/scheme: internal
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/group.name: shared
    alb.ingress.kubernetes.io/group.order: '5'
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 8080}]'
    alb.ingress.kubernetes.io/healthcheck-path: /actuator/health
spec:
  rules:
  - host: ledger-stg-api.peoplescloud.io
    http:
      paths:
      - path: /v1/ledger/account
        pathType: Prefix
        backend:
          service:
            name: account-service
            port:
              number: 8080
      - path: /v1/ledger/account/external/
        pathType: Prefix
        backend:
          service:
            name: account-external-service
            port:
              number: 8080
      - path: /v1/ledger/health
        pathType: Prefix
        backend:
          service:
            name: health-service
            port:
              number: 8080
      - path: /v1/ledger/profile
        pathType: Prefix
        backend:
          service:
            name: profile-service
            port:
              number: 8080
      - path: /v1/ledger/transaction
        pathType: Prefix
        backend:
          service:
            name: transaction-service
            port:
              number: 8080
      - path: /v1/ledger/transaction/async
        pathType: Prefix
        backend:
          service:
            name: transaction-async-service
            port:
              number: 8080
  - host: ledger-stg-util-api.peoplescloud.io
    http:
      paths:
      - path: /v1/util
        pathType: Prefix
        backend:
          service:
            name: staging-util-service
            port:
              number: 8080
