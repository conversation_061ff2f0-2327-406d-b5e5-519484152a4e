apiVersion: apps/v1
kind: Deployment
metadata:
  name: util-deployment
  namespace: pg-ledger-staging
  labels:
    app: qa-util-v1
#    domain: general-ledger
#    tags.datadoghq.com/env: staging
#    tags.datadoghq.com/service: gl-qa-util-v1
#    tags.datadoghq.com/version: "20250609141336"
spec:
  replicas: 1
  selector:
    matchLabels:
      app: qa-util-v1
  template:
    metadata:
#      annotations:
#        admission.datadoghq.com/java-lib.version: latest
      labels:
        app: qa-util-v1
#        admission.datadoghq.com/enabled: "true"
#        domain: general-ledger
#        tags.datadoghq.com/env: staging
#        tags.datadoghq.com/service: gl-qa-util-v1
#        tags.datadoghq.com/version: "20250609141336"
    spec:
      containers:
        - name: qa-util-container
          image: 799455639446.dkr.ecr.ca-central-1.amazonaws.com/qa-util:20250814170332
          ports:
            - containerPort: 8080
          # readinessProbe:
          #   httpGet:
          #     path: /actuator/health
          #     port: 8080
          #   initialDelaySeconds: 10
          #   periodSeconds: 5
          #   timeoutSeconds: 2
          #   failureThreshold: 3
          #   successThreshold: 1
          # livenessProbe:
          #   httpGet:
          #     path: /actuator/health
          #     port: 8080
          #   initialDelaySeconds: 60
          #   periodSeconds: 3
          #   timeoutSeconds: 2
          #   failureThreshold: 3
          #   successThreshold: 1
          env:
            - name: TZ
              value: 'America/Toronto'
            - name: SPRING_PROFILES_ACTIVE
              value: 'cloud'
            - name: TRANSACTION_DB_HOST
              valueFrom:
                secretKeyRef:
                  name: transaction-database
                  key: sys.database.host
            - name: TRANSACTION_DB_SCHEMA
              valueFrom:
                secretKeyRef:
                  name: transaction-database
                  key: sys.database.schema
            - name: TRANSACTION_DB_PORT
              value: '5432'
            - name: TRANSACTION_DB_NAME
              valueFrom:
                secretKeyRef:
                  name: transaction-database
                  key: sys.database.name
            - name: TRANSACTION_DB_USERNAME
              valueFrom:
                secretKeyRef:
                  name: transaction-database
                  key: sys.database.username
            - name: TRANSACTION_DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: transaction-database
                  key: sys.database.password
            - name:  REDIS_DB_CLUSTER
              valueFrom:
                secretKeyRef:
                  name: redis-cache
                  key: sys.database.cluster
            - name: SASL_USERNAME
              valueFrom:
                secretKeyRef:
                  name: kafka
                  key: sys.kafka.sasl.username
            - name: SASL_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: kafka
                  key: sys.kafka.sasl.password
            - name: KAFKA_SERVER
              valueFrom:
                secretKeyRef:
                  name: kafka
                  key: sys.kafka.host
            - name: SSL_ENABLE
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.ssl.enable
            - name: TOPIC_NAME
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.kafka.topic
            - name: KAFKA_WAIT_TIME
              value: '10'
            - name: START_KAFKA_LISTENER
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.kafka-start.url
            - name: STOP_KAFKA_LISTENER
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.kafka-stop.url
