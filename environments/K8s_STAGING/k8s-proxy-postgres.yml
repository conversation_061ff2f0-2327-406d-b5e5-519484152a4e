apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: postgres-proxy
  name: postgres-proxy
  namespace: pg-ledger-mgmt
spec:
  selector:
    matchLabels:
      app: postgres-proxy
  template:
    metadata:
      labels:
        app: postgres-proxy
    spec:
      containers:
      - image: tecnativa/tcp-proxy:latest
        imagePullPolicy: Always
        env:
          - name: LISTEN
            value: ":5432" # The listen address that it will be exposed to.
          - name: TALK
            value: "gl-db-staging.cluster-csfhkewhn9xz.ca-central-1.rds.amazonaws.com:5432"
          - name: TIMEOUT_CLIENT
            value: "120s"
          - name: TIMEOUT_SERVER
            value: "120s"
          - name: TIMEOUT_TUNNEL
            value: "120s"
        name: postgres-proxy
        # resources:
        #   requests:
        #     cpu: 10
        #     memory: 10
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      securityContext: {}
