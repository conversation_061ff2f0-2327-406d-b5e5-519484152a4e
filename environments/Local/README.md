# Local database

## Launch
cd environments/Local
docker-compose up -d

## Connect to PostgreSQL Database Admin
Open http://localhost:5050
 Username: postgres
 Password: postgres

## Connect to Redis Insights
Open http://localhost:5060


## Create all schema
CREATE SCHEMA relationship
CREATE SCHEMA ledger_account
CREATE SCHEMA ledger_transaction

## Create DDLs
Run ledger-profile-persistence/src/datamodel/0.0.0/create.sql
Run ledger-account-persistence/src/datamodel/0.0.0/create.sql
Run ledger-transaction-persistence/src/datamodel/0.0.0/create.sql
< for all three, dont run last two lines that assign permissions >

## Destroy
docker-compose down

