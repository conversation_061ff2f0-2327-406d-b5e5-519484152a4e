https://app.datadoghq.com/account/settings/agent/latest?platform=kubernetes

helm repo add datadog https://helm.datadoghq.com
helm install -f k8s-datadog-operator.yaml datadog-operator datadog/datadog-operator --namespace datadog 
kubectl create secret generic datadog-secret --from-literal api-key=XXXXXXXXXXXXXXXXXXXXXXX --namespace datadog


.. upgrade dd-agent
kubectl apply -f k8s-datadog-agent.yaml



.. upgrade dd-agent-operator with values from k8s-datadog-operator.yaml

helm upgrade -f k8s-datadog-operator.yaml datadog-operator datadog/datadog-operator --namespace datadog --version 2.8.0
