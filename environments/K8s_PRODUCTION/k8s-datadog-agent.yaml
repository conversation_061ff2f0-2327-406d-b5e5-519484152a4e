apiVersion: datadoghq.com/v2alpha1
kind: DatadogAgent
metadata:
  name: datadog
  namespace: datadog
spec:
  global:
    clusterName: gl-eks-main-prod
    site: datadoghq.com
    registry: public.ecr.aws/datadog
    originDetectionUnified:
      enabled: true
    podLabelsAsTags:
      domain: domain
    credentials:
      apiSecret:
        secretName: datadog-secret
        keyName: api-key
    tags:
      - env:prod
  features:
    apm:
      enabled: true
      instrumentation:
        enabled: false
    admissionController:
      enabled: true
      mutateUnlabelled: false
      failurePolicy: Fail
  override:
    nodeAgent:
      env:
        - name: DD_APM_FEATURES
          value: enable_cid_stats
        - name: DD_CHECKS_TAG_CARDINALITY
          value: orchestrator
    clusterAgent:
      replicas: 2
