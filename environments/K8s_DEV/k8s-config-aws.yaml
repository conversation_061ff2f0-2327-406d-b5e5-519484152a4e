apiVersion: v1
kind: Secret
metadata:
  name: gl-recon-aws
  namespace: pg-ledger-dev
type: Opaque
stringData:
  sys.pg.gl.s3.access.key: "********************"
  sys.pg.gl.s3.secret.key: "OZDAwTAItvS+jLLG41rcP6xeO2BsY4LdDDSrT3dj"
  sys.pg.gl.s3.bucket: "pg-gl-recon-dev"
  sys.pg.gl.s3.input.prefix: "input"
  sys.pg.gl.s3.archive.prefix: "archive"
  sys.pg.gl.s3.failed.prefix: "failed"
  sys.pg.gl.s3.region: "ca-central-1"
  sys.pg.gl.s3.s3.poll.interval: "30"