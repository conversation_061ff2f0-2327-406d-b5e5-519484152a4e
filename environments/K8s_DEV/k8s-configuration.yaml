apiVersion: v1
kind: Secret
metadata:
  name: profile-database
  namespace: pg-ledger-dev
type: Opaque
stringData:
  sys.database.host: "gl-db-devqa.cluster-csfhkewhn9xz.ca-central-1.rds.amazonaws.com"
  sys.database.read.host: "gl-db-devqa.cluster-ro-csfhkewhn9xz.ca-central-1.rds.amazonaws.com"
  sys.database.name: "gl_db_dev"
  sys.database.schema: "relationship_dev"
  sys.database.username: "relationship_dev_user"
  sys.database.password: "A7UJ45UesU^R>VdW"
---
apiVersion: v1
kind: Secret
metadata:
  name: account-database
  namespace: pg-ledger-dev
type: Opaque
stringData:
  sys.database.host: "gl-db-devqa.cluster-csfhkewhn9xz.ca-central-1.rds.amazonaws.com"
  sys.database.read.host: "gl-db-devqa.cluster-ro-csfhkewhn9xz.ca-central-1.rds.amazonaws.com"
  sys.database.name: "gl_db_dev"
  sys.database.schema: "account_dev"
  sys.database.username: "account_dev_user"
  sys.database.password: "u8@.8[gr_Cpg^-&?"

---
apiVersion: v1
kind: Secret
metadata:
  name: transaction-database
  namespace: pg-ledger-dev
type: Opaque
stringData:
  sys.database.host: "gl-db-devqa.cluster-csfhkewhn9xz.ca-central-1.rds.amazonaws.com"
  sys.database.read.host: "gl-db-devqa.cluster-ro-csfhkewhn9xz.ca-central-1.rds.amazonaws.com"
  sys.database.name: "gl_db_dev"
  sys.database.schema: "transaction_dev"
  sys.database.username: "transaction_user_dev"
  sys.database.password: "s1@+1[gr_CpK^-&A"

---
apiVersion: v1
kind: Secret
metadata:
  name: kafka
  namespace: pg-ledger-dev
type: Opaque
stringData:
  sys.kafka.host: "b-2.glkafkaclusterqa.a7wk7h.c4.kafka.ca-central-1.amazonaws.com:9096,b-1.glkafkaclusterqa.a7wk7h.c4.kafka.ca-central-1.amazonaws.com:9096"
  sys.kafka.sasl.username: "ledger-kafka-user-qa"
  sys.kafka.sasl.password: "dFw1/PX@!u&S+DQXLHtKcF"

---
apiVersion: v1
kind: Secret
metadata:
  name: aws-secret
  namespace: pg-ledger-dev
type: Opaque
stringData:
  aws.access.key: "********************"
  aws.secret.key: "U4k0uwS59tGQ0gQRl4NjYWVVjBgsVt7mDYR9q7zB"

---

apiVersion: v1
kind: ConfigMap
metadata:
  name: application-dependencies
  namespace: pg-ledger-dev
data:
  sys.profile.url: "http://profile-service.pg-ledger-dev.svc.cluster.local:8080/v1/internal/profile"
  sys.account.url: "http://account-service.pg-ledger-dev.svc.cluster.local:8080/v1/internal/account"
  sys.account.validate.url: "http://account-service.pg-ledger-dev.svc.cluster.local:8080/v1/internal/account/validate"
  sys.transaction.url: "http://transaction-service.pg-ledger-dev.svc.cluster.local:8080/v1/internal/transaction/balance"
  sys.accounts.retrieve.url: "http://account-service.pg-ledger-dev.svc.cluster.local:8080/v1/internal/accounts/all"
  sys.rollback.internal.url: "http://transaction-service.pg-ledger-dev.svc.cluster.local:8080/v1/internal/ledger/transaction/rollback"
  app.balance-snapshot.schedule: "0 10 0/1 ? * *"
  app.rollback-transactions.schedule: "0 0 4 * * *"
  app.rollback-transactions.expiry.time: "5"
  app.transactions.expiry.day: "32"
  app.instructions.fetch.limit: "250"
  app.use.store.procedure.for.sum: "true"
  app.ssl.enable: "true"
  app.kafka.topic: "PENDING_TRANSACTIONS_DEV"
  app.kafka.topic.v2: "PENDING_TRANSACTIONS_V2_DEV"
  app.kafka.error.topic: "MANUAL_INTERVENTION_TRANSACTIONS_DEV"
  app.kafka.failed.status.topic: "FAILED_TRANSACTIONS_DEV"
  app.kafka.consumer.group.id: "ASYNCHRONOUS_API_DEV"
  app.kafka.backoff.maxfailure: "3"
  app.kafka.backoff.interval: "2000"
  app.kafka.max.poll.idle: "1000"
  app.kafka.max.poll.interval: "700000"
  app.kafka.max.poll.records: "100"
  app.kafka.listener.number: "8"
  sys.gl.transaction.url: "http://transaction-service.pg-ledger-dev.svc.cluster.local:8080/v1/ledger/transaction"
  app.transaction.validation-service.timeout.connection: "2000"
  app.transaction.validation-service.timeout.read: "10000"
  app.transaction.api.timeout.connection: "5000"
  app.transaction.api.timeout.read: "10000"
  app.redis.read.timeout: "120"
  app.redis.connection.timeout: "150"
  app.redis.cache.expiry: "5"
  app.db.maximum.pool.size: "50"
  app.db.connection.timeout: "1000"
  app.account.validation-service.timeout.connection: "2000"
  app.account.validation-service.timeout.read: "10000"
  app.health.eks-name-space: "pg-ledger-dev"
  app.health.cloud-watch-log-group: "/non-prod/dev-qa/general-ledger/eks/gl-eks-main-devqa/application"
  app.jwt.token.check.disable: "false"
  app.kafka-start.url: "http://adbaa1885f84b49aaa088263413b4579-2c7922fd882b9561.elb.ca-central-1.amazonaws.com:8080/v1/util/start-kafka-listener"
  app.kafka-stop.url: "http://adbaa1885f84b49aaa088263413b4579-2c7922fd882b9561.elb.ca-central-1.amazonaws.com:8080/v1/util/stop-kafka-listener"
  app.async.transaction.producer.v2: "false"
  app.log.level.root: "INFO"
  app.log.level.com.peoplestrust: "INFO"
  app.log.level.hibernate: "INFO"
  app.log.level.api.payload: "INFO"
  app.log.level.perf: "INFO"
  app.log.level.flow: "INFO"
  app.gl-account-id: '************'
  app.jwt.issuer.cognito.url: "https://cognito-idp.us-east-2.amazonaws.com/us-east-2_oH2B5QIUo"
  app.jwt.issuer.auth0.url: "https://deadalus-dev.ca.auth0.com/"
  app.jwt.issuer.auth0Namespace.url: "https://ledger-dev-api.peoplescloud.io/"