apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: account-hpa
  namespace: pg-ledger-dev
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: account-deployment
  minReplicas: 0
  maxReplicas: 1
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 80
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: 80