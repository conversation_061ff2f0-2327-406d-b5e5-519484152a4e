apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: postgres-proxy-ro
  name: postgres-proxy-ro
  namespace: pg-ledger-mgmt
spec:
  selector:
    matchLabels:
      app: postgres-proxy-ro
  template:
    metadata:
      labels:
        app: postgres-proxy-ro
    spec:
      containers:
      - image: tecnativa/tcp-proxy:latest
        imagePullPolicy: Always
        env:
          - name: LISTEN
            value: ":5433" # The listen address that it will be exposed to.
          - name: TALK
            value: "gl-db-devqa.cluster-ro-csfhkewhn9xz.ca-central-1.rds.amazonaws.com:5432"
          - name: TIMEOUT_CLIENT
            value: "120s"
          - name: TIMEOUT_SERVER
            value: "120s"
          - name: TIMEOUT_TUNNEL
            value: "120s"
        name: postgres-proxy
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      securityContext: {}
