{"id": "75e526d8-50c1-4802-b0e8-8caf6fe1ab04", "name": "QAS API GW (Securiy)", "values": [{"key": "PROFILE_API_HOST", "value": "https://ledger-qas-api.peoplescloud.io", "enabled": true}, {"key": "ACCOUNT_API_HOST", "value": "https://ledger-qas-api.peoplescloud.io", "enabled": true}, {"key": "TRANSACTION_API_HOST", "value": "https://ledger-qas-api.peoplescloud.io", "enabled": true}, {"key": "CLIENT_API_HOST", "value": "https://ledger-qas-api.peoplescloud.io", "enabled": true}, {"key": "ACCOUNT_EXT_API_HOST", "value": "https://ledger-qas-api.peoplescloud.io", "enabled": true}, {"key": "aws_iam_access_key", "value": "********************", "enabled": true}, {"key": "aws_iam_secret_key", "value": "si5F3aN9dfqrWbEfq4Qo2wuLkMaqPDInhU7vOJ0i", "enabled": true}, {"key": "oauth2_client_id", "value": "6fce6kvabklju4cnpe0db5dme5", "enabled": true}, {"key": "oauth2_secret", "value": "u745kbp28jthsinpjrrghj81aud5gi0tq8cm2232s481ihet38d", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2023-01-26T17:13:35.651Z", "_postman_exported_using": "Postman/9.0.9"}