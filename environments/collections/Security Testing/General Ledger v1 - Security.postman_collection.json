{"info": {"_postman_id": "820601c3-bb89-4c67-ab04-0fb0e6714228", "name": "General <PERSON><PERSON> v1 - Security", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Public Facing APIs", "item": [{"name": "Retrieve balance (Public API)", "event": [{"listen": "prerequest", "script": {"exec": ["// Interaction Timestamp", "const moment = require('moment');", "const timestamp = moment.utc();", "pm.globals.set(\"timestamp\", timestamp);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Existing acount retrieved\", function () {", "    var jsonBody = pm.response.json();", "    pm.expect(jsonBody.ref_id).not.equal(null);", "    pm.expect(jsonBody.status).equal('DISABLED');", "    ", "    pm.expect(jsonBody.created_date_time).not.equal(null);", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "oauth2", "oauth2": [{"key": "accessTokenUrl", "value": "https://internal-user-pool-test.auth.us-east-2.amazoncognito.com/oauth2/token", "type": "string"}, {"key": "addTokenTo", "value": "header", "type": "string"}, {"key": "tokenName", "value": "", "type": "string"}, {"key": "client_authentication", "value": "header", "type": "string"}, {"key": "scope", "value": "com.peoplesgroup.ledger.qa/publicRestAPI.all", "type": "string"}, {"key": "clientSecret", "value": "{{oauth2_secret}}", "type": "string"}, {"key": "clientId", "value": "{{oauth2_client_id}}", "type": "string"}, {"key": "grant_type", "value": "client_credentials", "type": "string"}]}, "method": "GET", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{timestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}], "url": {"raw": "{{ACCOUNT_EXT_API_HOST}}/v1/ledger/account/external/{{account_ref_id}}/balance", "host": ["{{ACCOUNT_EXT_API_HOST}}"], "path": ["v1", "ledger", "account", "external", "{{account_ref_id}}", "balance"]}}, "response": []}, {"name": "Retrieve balance (Public API) - Example", "event": [{"listen": "prerequest", "script": {"exec": ["// Interaction Timestamp", "const moment = require('moment');", "const timestamp = moment.utc();", "pm.globals.set(\"timestamp\", timestamp);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Existing acount retrieved\", function () {", "    var jsonBody = pm.response.json();", "    pm.expect(jsonBody.ref_id).not.equal(null);", "    pm.expect(jsonBody.status).equal('DISABLED');", "    ", "    pm.expect(jsonBody.created_date_time).not.equal(null);", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "oauth2", "oauth2": [{"key": "accessTokenUrl", "value": "https://internal-user-pool-test.auth.us-east-2.amazoncognito.com/oauth2/token", "type": "string"}, {"key": "addTokenTo", "value": "header", "type": "string"}, {"key": "tokenName", "value": "", "type": "string"}, {"key": "client_authentication", "value": "header", "type": "string"}, {"key": "scope", "value": "com.peoplesgroup.ledger.qa/publicRestAPI.all", "type": "string"}, {"key": "clientSecret", "value": "{{oauth2_secret}}", "type": "string"}, {"key": "clientId", "value": "{{oauth2_client_id}}", "type": "string"}, {"key": "grant_type", "value": "client_credentials", "type": "string"}]}, "method": "GET", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{timestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "f588ce0e-21bc-473d-8e32-cd378a3156fe", "type": "text"}], "url": {"raw": "{{ACCOUNT_EXT_API_HOST}}/v1/ledger/account/external/ccedc394-f310-42d5-a2ab-ae7cfbe9f67d/balance", "host": ["{{ACCOUNT_EXT_API_HOST}}"], "path": ["v1", "ledger", "account", "external", "ccedc394-f310-42d5-a2ab-ae7cfbe9f67d", "balance"]}}, "response": []}]}, {"name": "Internal APIs (System to System)", "item": [{"name": "Profile", "item": [{"name": "Create new profile", "event": [{"listen": "prerequest", "script": {"exec": ["// Interaction Timestamp", "const moment = require('moment');", "const timestamp = moment.utc();", "pm.globals.set(\"timestamp\", timestamp);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test(\"New profile created\", function () {", "    var jsonBody = pm.response.json();", "    pm.expect(jsonBody.ref_id).not.equal(null);", "    pm.expect(jsonBody.status).equal('DISABLED');", "    ", "    pm.expect(jsonBody.created_date_time).not.equal(null);", "", "    // Populate customer_id for next request", "    pm.collectionVariables.set(\"profile_ref_id\", jsonBody.ref_id);", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{timestamp}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"crm_id\": \"{{$randomWord}}{{$randomInt}}\",\n    \"legal_name\": \"Legal Name of {{$randomWord}}\",\n    \"display_name\": \"Display Name of {{$randomWord}} {{$randomInt}}{{$randomInt}}{{$randomInt}}{{$randomInt}} Inc.\"\n}\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{PROFILE_API_HOST}}/v1/ledger/profile", "host": ["{{PROFILE_API_HOST}}"], "path": ["v1", "ledger", "profile"]}}, "response": []}, {"name": "Retrieve an existing profile", "event": [{"listen": "prerequest", "script": {"exec": ["// Interaction Timestamp", "const moment = require('moment');", "const timestamp = moment.utc();", "pm.globals.set(\"timestamp\", timestamp);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Existing profile retrieved\", function () {", "    var jsonBody = pm.response.json();", "    pm.expect(jsonBody.ref_id).not.equal(null);", "    pm.expect(jsonBody.status).equal('DISABLED');", "    ", "    pm.expect(jsonBody.created_date_time).not.equal(null);", "});", ""], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{timestamp}}", "type": "text"}], "url": {"raw": "{{PROFILE_API_HOST}}/v1/ledger/profile/{{profile_ref_id}}", "host": ["{{PROFILE_API_HOST}}"], "path": ["v1", "ledger", "profile", "{{profile_ref_id}}"]}}, "response": []}, {"name": "Retrieve all existing profiles", "event": [{"listen": "prerequest", "script": {"exec": ["// Interaction Timestamp", "const moment = require('moment');", "const timestamp = moment.utc();", "pm.globals.set(\"timestamp\", timestamp);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Existing profile retrieved\", function () {", "    var jsonBody = pm.response.json();", "    pm.expect(jsonBody.ref_id).not.equal(null);", "    pm.expect(jsonBody.status).equal('DISABLED');", "    ", "    pm.expect(jsonBody.created_date_time).not.equal(null);", "});", ""], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{timestamp}}", "type": "text"}], "url": {"raw": "{{PROFILE_API_HOST}}/v1/ledger/profile", "host": ["{{PROFILE_API_HOST}}"], "path": ["v1", "ledger", "profile"]}}, "response": []}, {"name": "Update an existing profile", "event": [{"listen": "prerequest", "script": {"exec": ["// Interaction Timestamp", "const moment = require('moment');", "const timestamp = moment.utc();", "pm.globals.set(\"timestamp\", timestamp);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Existing profile retrieved\", function () {", "    var jsonBody = pm.response.json();", "    pm.expect(jsonBody.ref_id).not.equal(null);", "    pm.expect(jsonBody.status).equal('DISABLED');", "    ", "    pm.expect(jsonBody.created_date_time).not.equal(null);", "});", ""], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{timestamp}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"crm_id\": \"{{$randomWord}}\",\n    \"legal_name\": \"Legal Name of {{$randomWord}}\",\n    \"display_name\": \"Display Name of {{$randomWord}}\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{PROFILE_API_HOST}}/v1/ledger/profile/{{profile_ref_id}}", "host": ["{{PROFILE_API_HOST}}"], "path": ["v1", "ledger", "profile", "{{profile_ref_id}}"]}}, "response": []}, {"name": "Update profile status (ENABLED)", "event": [{"listen": "prerequest", "script": {"exec": ["// Interaction Timestamp", "const moment = require('moment');", "const timestamp = moment.utc();", "pm.globals.set(\"timestamp\", timestamp);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 204\", function () {", "    pm.response.to.have.status(204);", "});", "", "pm.test(\"Status updated with empty response\", function () {", "    pm.expect(responseBody).equal('');", "});", ""], "type": "text/javascript"}}], "request": {"method": "PATCH", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{timestamp}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"status\":\"ENABLED\",\n  \"reason\": \"Business relationship was activated\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{PROFILE_API_HOST}}/v1/ledger/profile/{{profile_ref_id}}/status", "host": ["{{PROFILE_API_HOST}}"], "path": ["v1", "ledger", "profile", "{{profile_ref_id}}", "status"]}}, "response": []}, {"name": "Update profile status (DISABLED)", "event": [{"listen": "prerequest", "script": {"exec": ["// Interaction Timestamp", "const moment = require('moment');", "const timestamp = moment.utc();", "pm.globals.set(\"timestamp\", timestamp);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 204\", function () {", "    pm.response.to.have.status(204);", "});", "", "pm.test(\"Status updated with empty response\", function () {", "    pm.expect(responseBody).equal('');", "});", ""], "type": "text/javascript"}}], "request": {"method": "PATCH", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{timestamp}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"status\": \"DISABLED\",\n    \"reason\": \"Business relationship was suspended\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{PROFILE_API_HOST}}/v1/ledger/profile/{{profile_ref_id}}/status", "host": ["{{PROFILE_API_HOST}}"], "path": ["v1", "ledger", "profile", "{{profile_ref_id}}", "status"]}}, "response": []}]}, {"name": "Account", "item": [{"name": "Create new account", "event": [{"listen": "prerequest", "script": {"exec": ["// Interaction Timestamp", "const moment = require('moment');", "const timestamp = moment.utc();", "pm.globals.set(\"timestamp\", timestamp);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test(\"New acount created\", function () {", "    var jsonBody = pm.response.json();", "    pm.expect(jsonBody.ref_id).not.equal(null);", "    pm.expect(jsonBody.status).equal('INACTIVE');", "    ", "    pm.expect(jsonBody.created_date_time).not.equal(null);", "", "    // Populate customer_id for next request", "    pm.collectionVariables.set(\"account_ref_id\", jsonBody.ref_id);", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{timestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Account {{$randomWord}}\",\n  \"description\": \"Account description {{$randomWord}}\",\n  \"monetary_unit\": \"CAD\",\n  \"options\": {\n    \"overdraft_amount\": 10,\n    \"fund_hold_days\": 0\n  }\n}\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{ACCOUNT_API_HOST}}/v1/ledger/account", "host": ["{{ACCOUNT_API_HOST}}"], "path": ["v1", "ledger", "account"]}}, "response": []}, {"name": "Retrieve all accounts", "event": [{"listen": "prerequest", "script": {"exec": ["// Interaction Timestamp", "const moment = require('moment');", "const timestamp = moment.utc();", "pm.globals.set(\"timestamp\", timestamp);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Existing acount retrieved\", function () {", "    var jsonBody = pm.response.json();", "    pm.expect(jsonBody.ref_id).not.equal(null);", "    pm.expect(jsonBody.status).equal('DISABLED');", "    ", "    pm.expect(jsonBody.created_date_time).not.equal(null);", "});", ""], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{timestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}], "url": {"raw": "{{ACCOUNT_API_HOST}}/v1/ledger/account", "host": ["{{ACCOUNT_API_HOST}}"], "path": ["v1", "ledger", "account"]}}, "response": []}, {"name": "Retrieve balance", "event": [{"listen": "prerequest", "script": {"exec": ["// Interaction Timestamp", "const moment = require('moment');", "const timestamp = moment.utc();", "pm.globals.set(\"timestamp\", timestamp);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Existing acount retrieved\", function () {", "    var jsonBody = pm.response.json();", "    pm.expect(jsonBody.ref_id).not.equal(null);", "    pm.expect(jsonBody.status).equal('DISABLED');", "    ", "    pm.expect(jsonBody.created_date_time).not.equal(null);", "});", ""], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{timestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}], "url": {"raw": "{{ACCOUNT_API_HOST}}/v1/ledger/account/{{account_ref_id}}/balance", "host": ["{{ACCOUNT_API_HOST}}"], "path": ["v1", "ledger", "account", "{{account_ref_id}}", "balance"]}}, "response": []}, {"name": "Retrieve an account by id", "event": [{"listen": "prerequest", "script": {"exec": ["// Interaction Timestamp", "const moment = require('moment');", "const timestamp = moment.utc();", "pm.globals.set(\"timestamp\", timestamp);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Existing acount retrieved\", function () {", "    var jsonBody = pm.response.json();", "    pm.expect(jsonBody.ref_id).not.equal(null);", "    pm.expect(jsonBody.status).equal('DISABLED');", "    ", "    pm.expect(jsonBody.created_date_time).not.equal(null);", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "basic", "basic": [{"key": "password", "value": "password", "type": "string"}, {"key": "username", "value": "username", "type": "string"}]}, "method": "GET", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{timestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "6889a24f-a26c-4f8a-a8e7-779987bd5964", "type": "text"}], "url": {"raw": "{{ACCOUNT_API_HOST}}/v1/ledger/account/{{account_ref_id}}", "host": ["{{ACCOUNT_API_HOST}}"], "path": ["v1", "ledger", "account", "{{account_ref_id}}"]}}, "response": []}, {"name": "Update an account by id", "event": [{"listen": "prerequest", "script": {"exec": ["// Interaction Timestamp", "const moment = require('moment');", "const timestamp = moment.utc();", "pm.globals.set(\"timestamp\", timestamp);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Existing acount retrieved\", function () {", "    var jsonBody = pm.response.json();", "    pm.expect(jsonBody.ref_id).not.equal(null);", "    pm.expect(jsonBody.status).equal('DISABLED');", "    ", "    pm.expect(jsonBody.created_date_time).not.equal(null);", "});", ""], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{timestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Account Name - {{$randomWord}}\",\n  \"description\": \"Account description {{$randomWord}}\",\n  \"monetary_unit\": \"CAD\",\n  \"options\": {\n    \"prefund_reserve_amount\": 100000.12,\n    \"overdraft_amount\": 100000,\n    \"fund_hold_days\": 5\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{ACCOUNT_API_HOST}}/v1/ledger/account/{{account_ref_id}}", "host": ["{{ACCOUNT_API_HOST}}"], "path": ["v1", "ledger", "account", "{{account_ref_id}}"]}}, "response": []}, {"name": "Update account status (ACTIVE)", "event": [{"listen": "prerequest", "script": {"exec": ["// Interaction Timestamp", "const moment = require('moment');", "const timestamp = moment.utc();", "pm.globals.set(\"timestamp\", timestamp);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 204\", function () {", "    pm.response.to.have.status(204);", "});", "", "pm.test(\"Status updated with empty response\", function () {", "    pm.expect(responseBody).equal('');", "});", ""], "type": "text/javascript"}}], "request": {"method": "PATCH", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{timestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"ACTIVE\",\n  \"reason\": \"Launched on 2022-07-27\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{ACCOUNT_API_HOST}}/v1/ledger/account/{{account_ref_id}}/status", "host": ["{{ACCOUNT_API_HOST}}"], "path": ["v1", "ledger", "account", "{{account_ref_id}}", "status"]}}, "response": []}, {"name": "Update account status (INACTIVE)", "event": [{"listen": "prerequest", "script": {"exec": ["// Interaction Timestamp", "const moment = require('moment');", "const timestamp = moment.utc();", "pm.globals.set(\"timestamp\", timestamp);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 204\", function () {", "    pm.response.to.have.status(204);", "});", "", "pm.test(\"Status updated with empty response\", function () {", "    pm.expect(responseBody).equal('');", "});", ""], "type": "text/javascript"}}], "request": {"method": "PATCH", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{timestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"INACTIVE\",\n  \"reason\": \"Account was inactive\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{ACCOUNT_API_HOST}}/v1/ledger/account/{{account_ref_id}}/status", "host": ["{{ACCOUNT_API_HOST}}"], "path": ["v1", "ledger", "account", "{{account_ref_id}}", "status"]}}, "response": []}, {"name": "Update account status (SUSPENDED)", "event": [{"listen": "prerequest", "script": {"exec": ["// Interaction Timestamp", "const moment = require('moment');", "const timestamp = moment.utc();", "pm.globals.set(\"timestamp\", timestamp);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 204\", function () {", "    pm.response.to.have.status(204);", "});", "", "pm.test(\"Status updated with empty response\", function () {", "    pm.expect(responseBody).equal('');", "});", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "basic", "basic": [{"key": "password", "value": "password", "type": "string"}, {"key": "username", "value": "username", "type": "string"}]}, "method": "PATCH", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{timestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"SUSPENDED\",\n  \"reason\": \"Suspended the business relationship\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{ACCOUNT_API_HOST}}/v1/ledger/account/{{account_ref_id}}/status", "host": ["{{ACCOUNT_API_HOST}}"], "path": ["v1", "ledger", "account", "{{account_ref_id}}", "status"]}}, "response": []}]}, {"name": "Transaction", "item": [{"name": "Phase 0 -- Initial funding", "item": [{"name": "Internal transaction (Reserve funds)", "event": [{"listen": "prerequest", "script": {"exec": ["// Interaction Timestamp\r", "const moment = require('moment');\r", "const timestamp = moment.utc();\r", "const futuretimestamp = moment().add(2, 'days').utc();\r", "pm.globals.set(\"timestamp\", timestamp);\r", "pm.globals.set(\"futuretimestamp\", futuretimestamp);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test(\"New transaction initiated\", function () {", "    var jsonBody = pm.response.json();", "    pm.expect(jsonBody.instruction_ref_id).not.equal(null);", "        ", "    pm.expect(jsonBody.status).equal('PENDING');", "    ", "    // Populate customer_id for next request", "    pm.collectionVariables.set(\"instruction_ref_id\", jsonBody.instruction_ref_id);", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{timestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}, {"key": "x-pg-account-id", "value": "{{account_ref_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"instruction_ref_id\": \"{{$randomUUID}}\",\n    \"payment_rail\": \"INTERNAL\",\n    \"transactions\": [\n        {\n            \"transaction_ref_id\": \"{{$randomUUID}}\",\n            \"payment_category\": \"PREFUND_RESERVE\",\n            \"transaction_flow\":\"CREDIT\",\n            \"amount\": 200,\n            \"monetary_unit\": \"CAD\"\n\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{TRANSACTION_API_HOST}}/v1/ledger/transaction/internal", "host": ["{{TRANSACTION_API_HOST}}"], "path": ["v1", "ledger", "transaction", "internal"]}}, "response": []}]}, {"name": "Phase 1 -- Initiate (pick ONE)", "item": [{"name": "Initiate Instruction (CREDIT and DEBIT)", "event": [{"listen": "prerequest", "script": {"exec": ["// Interaction Timestamp\r", "const moment = require('moment');\r", "const timestamp = moment.utc();\r", "const futuretimestamp = moment().add(1, 'days').utc();\r", "pm.globals.set(\"timestamp\", timestamp);\r", "pm.globals.set(\"futuretimestamp\", futuretimestamp);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});\r", "\r", "pm.test(\"New transaction initiated\", function () {\r", "    var jsonBody = pm.response.json();\r", "    pm.expect(jsonBody.instruction_ref_id).not.equal(null);\r", "        \r", "    pm.expect(jsonBody.status).equal('PENDING');\r", "    \r", "    // Populate customer_id for next request\r", "    pm.collectionVariables.set(\"instruction_ref_id\", jsonBody.instruction_ref_id);\r", "});\r", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{timestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}, {"key": "x-pg-account-id", "value": "{{account_ref_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"instruction_ref_id\": \"{{$randomWord}}{{$randomInt}}\",\n    \"payment_rail\": \"ETRANSFER\",\n    \"transactions\": [\n              {\n            \"transaction_ref_id\": \"{{$randomUUID}}\",\n            \"payment_category\": \"COMPLETE_PAYMENT\",\n            \"amount\": 60,\n            \"monetary_unit\": \"CAD\",\n            \"acceptance_date_time\": \"{{timestamp}}\"\n            \n        },\n         {\n            \"transaction_ref_id\": \"{{$randomUUID}}\",\n            \"payment_category\": \"SEND_PAYMENT\",\n            \"amount\": 50,\n            \"monetary_unit\": \"CAD\",\n            \"acceptance_date_time\": \"{{timestamp}}\"\n\n            \n        }\n\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{TRANSACTION_API_HOST}}/v1/ledger/transaction", "host": ["{{TRANSACTION_API_HOST}}"], "path": ["v1", "ledger", "transaction"]}}, "response": []}, {"name": "Initiate Instruction (CREDIT )", "event": [{"listen": "prerequest", "script": {"exec": ["// Interaction Timestamp\r", "const moment = require('moment');\r", "const timestamp = moment.utc();\r", "const futuretimestamp = moment().add(1, 'days').utc();\r", "pm.globals.set(\"timestamp\", timestamp);\r", "pm.globals.set(\"futuretimestamp\", futuretimestamp);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});\r", "\r", "pm.test(\"New transaction initiated\", function () {\r", "    var jsonBody = pm.response.json();\r", "    pm.expect(jsonBody.instruction_ref_id).not.equal(null);\r", "        \r", "    pm.expect(jsonBody.status).equal('PENDING');\r", "    \r", "    // Populate customer_id for next request\r", "    pm.collectionVariables.set(\"instruction_ref_id\", jsonBody.instruction_ref_id);\r", "});\r", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{timestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}, {"key": "x-pg-account-id", "value": "{{account_ref_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"instruction_ref_id\": \"{{$randomWord}}{{$randomInt}}\",\n    \"payment_rail\": \"ETRANSFER\",\n    \"transactions\": [\n              {\n            \"transaction_ref_id\": \"{{$randomUUID}}\",\n            \"payment_category\": \"COMPLETE_PAYMENT\",\n            \"amount\": 60,\n            \"monetary_unit\": \"CAD\",\n            \"acceptance_date_time\": \"{{timestamp}}\"\n            \n        }\n\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{TRANSACTION_API_HOST}}/v1/ledger/transaction", "host": ["{{TRANSACTION_API_HOST}}"], "path": ["v1", "ledger", "transaction"]}}, "response": []}, {"name": "Initiate Instruction (DEBIT only)", "event": [{"listen": "prerequest", "script": {"exec": ["// Interaction Timestamp\r", "const moment = require('moment');\r", "const timestamp = moment.utc();\r", "const futuretimestamp = moment().add(2, 'days').utc();\r", "pm.globals.set(\"timestamp\", timestamp);\r", "pm.globals.set(\"futuretimestamp\", futuretimestamp);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test(\"New transaction initiated\", function () {", "    var jsonBody = pm.response.json();", "    pm.expect(jsonBody.instruction_ref_id).not.equal(null);", "        ", "    pm.expect(jsonBody.status).equal('PENDING');", "    ", "    // Populate customer_id for next request", "    pm.collectionVariables.set(\"instruction_ref_id\", jsonBody.instruction_ref_id);", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{timestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}, {"key": "x-pg-account-id", "value": "{{account_ref_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"instruction_ref_id\": \"{{$randomWord}}{{$randomInt}}\",\n    \"payment_rail\": \"ETRANSFER\",\n    \"transactions\": [\n        \n        {\n            \"transaction_ref_id\": \"{{$randomUUID}}\",\n            \"payment_category\": \"SEND_PAYMENT\",\n            \"amount\": 1007.5,\n            \"monetary_unit\": \"CAD\",\n            \"acceptance_date_time\": \"{{timestamp}}\",\n            \"due_date_time\":\"{{timestamp}}\"\n        },\n\n        {\n            \"transaction_ref_id\": \"{{$randomUUID}}\",\n            \"payment_category\": \"SEND_PAYMENT\",\n            \"amount\": 1007.5,\n            \"monetary_unit\": \"CAD\",\n            \"acceptance_date_time\": \"{{timestamp}}\"\n            \n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{TRANSACTION_API_HOST}}/v1/ledger/transaction", "host": ["{{TRANSACTION_API_HOST}}"], "path": ["v1", "ledger", "transaction"]}}, "response": []}, {"name": "Initiate Instruction (CREDIT (1) + DEBIT (1)); SUM=0", "event": [{"listen": "prerequest", "script": {"exec": ["// Interaction Timestamp", "const moment = require('moment');", "const timestamp = moment.utc();", "pm.globals.set(\"timestamp\", timestamp);", "", "const acceptance_date_time = moment.utc();", "pm.globals.set(\"acceptance_date_time\", acceptance_date_time);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test(\"New transaction initiated\", function () {", "    var jsonBody = pm.response.json();", "    pm.expect(jsonBody.instruction_ref_id).not.equal(null);", "        ", "    pm.expect(jsonBody.status).equal('PENDING');", "    ", "    // Populate customer_id for next request", "    pm.collectionVariables.set(\"instruction_ref_id\", jsonBody.instruction_ref_id);", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{timestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}, {"key": "x-pg-account-id", "value": "{{account_ref_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"instruction_ref_id\": \"{{$randomUUID}}\",\n    \"payment_rail\": \"ETRANSFER\",\n    \"transactions\": [\n        {\n            \"transaction_ref_id\": \"{{$randomUUID}}\",\n            \"payment_category\": \"COMPLETE_PAYMENT\",\n            \"amount\": 250.00,\n            \"monetary_unit\": \"CAD\",\n            \"acceptance_date_time\": \"{{acceptance_date_time}}\"\n        },\n        {\n            \"transaction_ref_id\": \"{{$randomUUID}}\",\n            \"payment_category\": \"SEND_PAYMENT\",\n            \"amount\": 250.00,\n            \"monetary_unit\": \"CAD\",\n            \"acceptance_date_time\": \"{{acceptance_date_time}}\"\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{TRANSACTION_API_HOST}}/v1/ledger/transaction", "host": ["{{TRANSACTION_API_HOST}}"], "path": ["v1", "ledger", "transaction"]}}, "response": []}, {"name": "Initiate Instruction (CREDIT (5) + DEBIT (5)); SUM=0", "event": [{"listen": "prerequest", "script": {"exec": ["// Interaction Timestamp", "const moment = require('moment');", "const timestamp = moment.utc();", "pm.globals.set(\"timestamp\", timestamp);", "", "const acceptance_date_time = moment.utc();", "pm.globals.set(\"acceptance_date_time\", acceptance_date_time);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test(\"New transaction initiated\", function () {", "    var jsonBody = pm.response.json();", "    pm.expect(jsonBody.instruction_ref_id).not.equal(null);", "        ", "    pm.expect(jsonBody.status).equal('PENDING');", "    ", "    // Populate customer_id for next request", "    pm.collectionVariables.set(\"instruction_ref_id\", jsonBody.instruction_ref_id);", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{timestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}, {"key": "x-pg-account-id", "value": "{{account_ref_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"instruction_ref_id\": \"{{$randomUUID}}\",\n    \"payment_rail\": \"ETRANSFER\",\n    \"transactions\": [\n        {\n            \"transaction_ref_id\": \"{{$randomUUID}}\",\n            \"payment_category\": \"COMPLETE_PAYMENT\",\n            \"amount\": 250.00,\n            \"monetary_unit\": \"CAD\",\n            \"acceptance_date_time\": \"{{acceptance_date_time}}\"\n        },\n        {\n            \"transaction_ref_id\": \"{{$randomUUID}}\",\n            \"payment_category\": \"COMPLETE_PAYMENT\",\n            \"amount\": 250.00,\n            \"monetary_unit\": \"CAD\",\n            \"acceptance_date_time\": \"{{acceptance_date_time}}\"\n        },\n        {\n            \"transaction_ref_id\": \"{{$randomUUID}}\",\n            \"payment_category\": \"COMPLETE_PAYMENT\",\n            \"amount\": 250.00,\n            \"monetary_unit\": \"CAD\",\n            \"acceptance_date_time\": \"{{acceptance_date_time}}\"\n        },\n        {\n            \"transaction_ref_id\": \"{{$randomUUID}}\",\n            \"payment_category\": \"COMPLETE_PAYMENT\",\n            \"amount\": 250.00,\n            \"monetary_unit\": \"CAD\",\n            \"acceptance_date_time\": \"{{acceptance_date_time}}\"\n        },\n        {\n            \"transaction_ref_id\": \"{{$randomUUID}}\",\n            \"payment_category\": \"COMPLETE_PAYMENT\",\n            \"amount\": 250.00,\n            \"monetary_unit\": \"CAD\",\n            \"acceptance_date_time\": \"{{acceptance_date_time}}\"\n        },\n        {\n            \"transaction_ref_id\": \"{{$randomUUID}}\",\n            \"payment_category\": \"SEND_PAYMENT\",\n            \"amount\": 250.00,\n            \"monetary_unit\": \"CAD\",\n            \"acceptance_date_time\": \"{{acceptance_date_time}}\"\n        },\n        {\n            \"transaction_ref_id\": \"{{$randomUUID}}\",\n            \"payment_category\": \"SEND_PAYMENT\",\n            \"amount\": 250.00,\n            \"monetary_unit\": \"CAD\",\n            \"acceptance_date_time\": \"{{acceptance_date_time}}\"\n        },\n        {\n            \"transaction_ref_id\": \"{{$randomUUID}}\",\n            \"payment_category\": \"SEND_PAYMENT\",\n            \"amount\": 250.00,\n            \"monetary_unit\": \"CAD\",\n            \"acceptance_date_time\": \"{{acceptance_date_time}}\"\n        },\n        {\n            \"transaction_ref_id\": \"{{$randomUUID}}\",\n            \"payment_category\": \"SEND_PAYMENT\",\n            \"amount\": 250.00,\n            \"monetary_unit\": \"CAD\",\n            \"acceptance_date_time\": \"{{acceptance_date_time}}\"\n        },\n        {\n            \"transaction_ref_id\": \"{{$randomUUID}}\",\n            \"payment_category\": \"SEND_PAYMENT\",\n            \"amount\": 250.00,\n            \"monetary_unit\": \"CAD\",\n            \"acceptance_date_time\": \"{{acceptance_date_time}}\"\n        }\n        \n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{TRANSACTION_API_HOST}}/v1/ledger/transaction", "host": ["{{TRANSACTION_API_HOST}}"], "path": ["v1", "ledger", "transaction"]}}, "response": []}, {"name": "Initiate Instruction (CREDIT (5) + DEBIT (5)); SUM > 0", "event": [{"listen": "prerequest", "script": {"exec": ["// Interaction Timestamp", "const moment = require('moment');", "const timestamp = moment.utc();", "pm.globals.set(\"timestamp\", timestamp);", "", "const acceptance_date_time = moment.utc();", "pm.globals.set(\"acceptance_date_time\", acceptance_date_time);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test(\"New transaction initiated\", function () {", "    var jsonBody = pm.response.json();", "    pm.expect(jsonBody.instruction_ref_id).not.equal(null);", "        ", "    pm.expect(jsonBody.status).equal('PENDING');", "    ", "    // Populate customer_id for next request", "    pm.collectionVariables.set(\"instruction_ref_id\", jsonBody.instruction_ref_id);", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{timestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}, {"key": "x-pg-account-id", "value": "{{account_ref_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"instruction_ref_id\": \"{{$randomUUID}}\",\n    \"payment_rail\": \"ETRANSFER\",\n    \"transactions\": [\n        {\n            \"transaction_ref_id\": \"{{$randomUUID}}\",\n            \"payment_category\": \"SEND_PAYMENT\",\n            \"amount\": 250.00,\n            \"monetary_unit\": \"CAD\",\n            \"acceptance_date_time\": \"{{acceptance_date_time}}\"\n        },\n        {\n            \"transaction_ref_id\": \"{{$randomUUID}}\",\n            \"payment_category\": \"SEND_PAYMENT\",\n            \"amount\": 1250.00,\n            \"monetary_unit\": \"CAD\",\n            \"acceptance_date_time\": \"{{acceptance_date_time}}\"\n        },\n        {\n            \"transaction_ref_id\": \"{{$randomUUID}}\",\n            \"payment_category\": \"SEND_PAYMENT\",\n            \"amount\": 750.00,\n            \"monetary_unit\": \"CAD\",\n            \"acceptance_date_time\": \"{{acceptance_date_time}}\"\n        },\n        {\n            \"transaction_ref_id\": \"{{$randomUUID}}\",\n            \"payment_category\": \"SEND_PAYMENT\",\n            \"amount\": 1750.00,\n            \"monetary_unit\": \"CAD\",\n            \"acceptance_date_time\": \"{{acceptance_date_time}}\"\n        },\n        {\n            \"transaction_ref_id\": \"{{$randomUUID}}\",\n            \"payment_category\": \"SEND_PAYMENT\",\n            \"amount\": 1250.00,\n            \"monetary_unit\": \"CAD\",\n            \"acceptance_date_time\": \"{{acceptance_date_time}}\"\n        },\n        {\n            \"transaction_ref_id\": \"{{$randomUUID}}\",\n            \"payment_category\": \"COMPLETE_PAYMENT\",\n            \"amount\": 11250.00,\n            \"monetary_unit\": \"CAD\",\n            \"acceptance_date_time\": \"{{acceptance_date_time}}\"\n        },\n        {\n            \"transaction_ref_id\": \"{{$randomUUID}}\",\n            \"payment_category\": \"COMPLETE_PAYMENT\",\n            \"amount\": 2500.00,\n            \"monetary_unit\": \"CAD\",\n            \"acceptance_date_time\": \"{{acceptance_date_time}}\"\n        },\n        {\n            \"transaction_ref_id\": \"{{$randomUUID}}\",\n            \"payment_category\": \"COMPLETE_PAYMENT\",\n            \"amount\": 12500.00,\n            \"monetary_unit\": \"CAD\",\n            \"acceptance_date_time\": \"{{acceptance_date_time}}\"\n        },\n        {\n            \"transaction_ref_id\": \"{{$randomUUID}}\",\n            \"payment_category\": \"COMPLETE_PAYMENT\",\n            \"amount\": 5000.00,\n            \"monetary_unit\": \"CAD\",\n            \"acceptance_date_time\": \"{{acceptance_date_time}}\"\n        },\n        {\n            \"transaction_ref_id\": \"{{$randomUUID}}\",\n            \"payment_category\": \"COMPLETE_PAYMENT\",\n            \"amount\": 15000.00,\n            \"monetary_unit\": \"CAD\",\n            \"acceptance_date_time\": \"{{acceptance_date_time}}\"\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{TRANSACTION_API_HOST}}/v1/ledger/transaction", "host": ["{{TRANSACTION_API_HOST}}"], "path": ["v1", "ledger", "transaction"]}}, "response": []}]}, {"name": "Phase 2 -- Commit or rollback (pick ONE)", "item": [{"name": "Commit Transaction", "event": [{"listen": "prerequest", "script": {"exec": ["// Interaction Timestamp\r", "const moment = require('moment');\r", "const timestamp = moment.utc();\r", "pm.globals.set(\"timestamp\", timestamp);"], "type": "text/javascript"}}], "request": {"method": "PATCH", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{timestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}, {"key": "x-pg-account-id", "value": "{{account_ref_id}}", "type": "text"}], "url": {"raw": "{{TRANSACTION_API_HOST}}/v1/ledger/transaction/{{instruction_ref_id}}", "host": ["{{TRANSACTION_API_HOST}}"], "path": ["v1", "ledger", "transaction", "{{instruction_ref_id}}"]}}, "response": []}, {"name": "Rollback instruction", "event": [{"listen": "prerequest", "script": {"exec": ["// Interaction Timestamp\r", "const moment = require('moment');\r", "const timestamp = moment.utc();\r", "pm.globals.set(\"timestamp\", timestamp);"], "type": "text/javascript"}}], "request": {"method": "DELETE", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{timestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}, {"key": "x-pg-account-id", "value": "{{account_ref_id}}", "type": "text"}], "url": {"raw": "{{TRANSACTION_API_HOST}}/v1/ledger/transaction/{{instruction_ref_id}}", "host": ["{{TRANSACTION_API_HOST}}"], "path": ["v1", "ledger", "transaction", "{{instruction_ref_id}}"]}}, "response": []}]}, {"name": "Phase 3 -- Reverse (only if Initiate > Commit)", "item": [{"name": "Reverse Transaction", "event": [{"listen": "prerequest", "script": {"exec": ["// Interaction Timestamp\r", "const moment = require('moment');\r", "const timestamp = moment.utc();\r", "const futuretimestamp = moment().add(2, 'days').utc();\r", "pm.globals.set(\"timestamp\", timestamp);\r", "pm.globals.set(\"futuretimestamp\", futuretimestamp);"], "type": "text/javascript"}}], "request": {"method": "DELETE", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{timestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}, {"key": "x-pg-account-id", "value": "{{account_ref_id}}", "type": "text"}], "url": {"raw": "{{TRANSACTION_API_HOST}}/v1/ledger/transaction/{{instruction_ref_id}}/********-00c3-446f-bfb9-f917faf5aef9", "host": ["{{TRANSACTION_API_HOST}}"], "path": ["v1", "ledger", "transaction", "{{instruction_ref_id}}", "********-00c3-446f-bfb9-f917faf5aef9"]}}, "response": []}]}, {"name": "Phase 9 -- After the fact correction", "item": [{"name": "Internal transaction (Correction)", "event": [{"listen": "prerequest", "script": {"exec": ["// Interaction Timestamp\r", "const moment = require('moment');\r", "const timestamp = moment.utc();\r", "const futuretimestamp = moment().add(2, 'days').utc();\r", "pm.globals.set(\"timestamp\", timestamp);\r", "pm.globals.set(\"futuretimestamp\", futuretimestamp);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test(\"New transaction initiated\", function () {", "    var jsonBody = pm.response.json();", "    pm.expect(jsonBody.instruction_ref_id).not.equal(null);", "        ", "    pm.expect(jsonBody.status).equal('PENDING');", "    ", "    // Populate customer_id for next request", "    pm.collectionVariables.set(\"instruction_ref_id\", jsonBody.instruction_ref_id);", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{timestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}, {"key": "x-pg-account-id", "value": "{{account_ref_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"instruction_ref_id\": \"{{$randomUUID}}\",\n    \"payment_rail\": \"INTERNAL\",\n    \"transactions\": [\n        {\n            \"transaction_ref_id\": \"{{$randomUUID}}\",\n            \"payment_category\": \"CORRECTION\",\n            \"transaction_flow\":\"CREDIT\",\n            \"amount\": 200,\n            \"monetary_unit\": \"CAD\"\n\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{TRANSACTION_API_HOST}}/v1/ledger/transaction/internal", "host": ["{{TRANSACTION_API_HOST}}"], "path": ["v1", "ledger", "transaction", "internal"]}}, "response": []}]}, {"name": "Retrieve Instruction", "event": [{"listen": "prerequest", "script": {"exec": ["// Interaction Timestamp\r", "const moment = require('moment');\r", "const timestamp = moment.utc();\r", "const futuretimestamp = moment().add(2, 'days').utc();\r", "pm.globals.set(\"timestamp\", timestamp);\r", "pm.globals.set(\"futuretimestamp\", futuretimestamp);"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{timestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}, {"key": "x-pg-account-id", "value": "{{account_ref_id}}", "type": "text"}], "url": {"raw": "{{TRANSACTION_API_HOST}}/v1/ledger/transaction/{{instruction_ref_id}}", "host": ["{{TRANSACTION_API_HOST}}"], "path": ["v1", "ledger", "transaction", "{{instruction_ref_id}}"]}}, "response": []}, {"name": "Retrieve Transaction", "event": [{"listen": "prerequest", "script": {"exec": ["// Interaction Timestamp\r", "const moment = require('moment');\r", "const timestamp = moment.utc();\r", "const futuretimestamp = moment().add(2, 'days').utc();\r", "pm.globals.set(\"timestamp\", timestamp);\r", "pm.globals.set(\"futuretimestamp\", futuretimestamp);"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{timestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}, {"key": "x-pg-account-id", "value": "{{account_ref_id}}", "type": "text"}], "url": {"raw": "{{TRANSACTION_API_HOST}}/v1/ledger/transaction/Group159/c2e555fb-18df-4dc7-9c74-52b8fd86f7b3", "host": ["{{TRANSACTION_API_HOST}}"], "path": ["v1", "ledger", "transaction", "Group159", "c2e555fb-18df-4dc7-9c74-52b8fd86f7b3"]}}, "response": []}]}]}, {"name": "Examples (end to end)", "item": [{"name": "Sample end to end CREDIT flow", "item": [{"name": "Create new profile", "event": [{"listen": "prerequest", "script": {"exec": ["// Interaction Timestamp", "const moment = require('moment');", "const timestamp = moment.utc();", "pm.globals.set(\"timestamp\", timestamp);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test(\"New profile created\", function () {", "    var jsonBody = pm.response.json();", "    pm.expect(jsonBody.ref_id).not.equal(null);", "    pm.expect(jsonBody.status).equal('DISABLED');", "    ", "    pm.expect(jsonBody.created_date_time).not.equal(null);", "", "    // Populate customer_id for next request", "    pm.collectionVariables.set(\"profile_ref_id\", jsonBody.ref_id);", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{timestamp}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"crm_id\": \"{{$randomWord}}{{$randomInt}}\",\n    \"legal_name\": \"Legal Name of {{$randomWord}}\",\n    \"display_name\": \"Display Name of {{$randomWord}} {{$randomInt}}{{$randomInt}}{{$randomInt}}{{$randomInt}} Inc.\"\n}\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{PROFILE_API_HOST}}/v1/ledger/profile", "host": ["{{PROFILE_API_HOST}}"], "path": ["v1", "ledger", "profile"]}}, "response": []}, {"name": "Update profile status (ENABLED)", "event": [{"listen": "prerequest", "script": {"exec": ["// Interaction Timestamp", "const moment = require('moment');", "const timestamp = moment.utc();", "pm.globals.set(\"timestamp\", timestamp);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 204\", function () {", "    pm.response.to.have.status(204);", "});", "", "pm.test(\"Status updated with empty response\", function () {", "    pm.expect(responseBody).equal('');", "});", ""], "type": "text/javascript"}}], "request": {"method": "PATCH", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{timestamp}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"status\":\"ENABLED\",\n  \"reason\": \"Business relationship was activated\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{PROFILE_API_HOST}}/v1/ledger/profile/{{profile_ref_id}}/status", "host": ["{{PROFILE_API_HOST}}"], "path": ["v1", "ledger", "profile", "{{profile_ref_id}}", "status"]}}, "response": []}, {"name": "Create new account", "event": [{"listen": "prerequest", "script": {"exec": ["// Interaction Timestamp", "const moment = require('moment');", "const timestamp = moment.utc();", "pm.globals.set(\"timestamp\", timestamp);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test(\"New acount created\", function () {", "    var jsonBody = pm.response.json();", "    pm.expect(jsonBody.ref_id).not.equal(null);", "    pm.expect(jsonBody.status).equal('INACTIVE');", "    ", "    pm.expect(jsonBody.created_date_time).not.equal(null);", "", "    // Populate customer_id for next request", "    pm.collectionVariables.set(\"account_ref_id\", jsonBody.ref_id);", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{timestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Account {{$randomWord}}\",\n  \"description\": \"Account description {{$randomWord}}\",\n  \"monetary_unit\": \"CAD\",\n  \"options\": {\n    \"overdraft_amount\": 100000,\n    \"fund_hold_days\": 0\n  }\n}\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{ACCOUNT_API_HOST}}/v1/ledger/account", "host": ["{{ACCOUNT_API_HOST}}"], "path": ["v1", "ledger", "account"]}}, "response": []}, {"name": "Update account status (ACTIVE)", "event": [{"listen": "prerequest", "script": {"exec": ["// Interaction Timestamp", "const moment = require('moment');", "const timestamp = moment.utc();", "pm.globals.set(\"timestamp\", timestamp);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 204\", function () {", "    pm.response.to.have.status(204);", "});", "", "pm.test(\"Status updated with empty response\", function () {", "    pm.expect(responseBody).equal('');", "});", ""], "type": "text/javascript"}}], "request": {"method": "PATCH", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{timestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"ACTIVE\",\n  \"reason\": \"Launched on 2022-07-27\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{ACCOUNT_API_HOST}}/v1/ledger/account/{{account_ref_id}}/status", "host": ["{{ACCOUNT_API_HOST}}"], "path": ["v1", "ledger", "account", "{{account_ref_id}}", "status"]}}, "response": []}, {"name": "Initiate Instruction (CREDIT only)", "event": [{"listen": "prerequest", "script": {"exec": ["// Interaction Timestamp\r", "const moment = require('moment');\r", "const timestamp = moment.utc();\r", "const futuretimestamp = moment().add(2, 'days').utc();\r", "pm.globals.set(\"timestamp\", timestamp);\r", "pm.globals.set(\"futuretimestamp\", futuretimestamp);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});\r", "\r", "pm.test(\"New transaction initiated\", function () {\r", "    var jsonBody = pm.response.json();\r", "    pm.expect(jsonBody.instruction_ref_id).not.equal(null);\r", "        \r", "    pm.expect(jsonBody.status).equal('PENDING');\r", "    \r", "    // Populate customer_id for next request\r", "    pm.collectionVariables.set(\"instruction_ref_id\", jsonBody.instruction_ref_id);\r", "});\r", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{timestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}, {"key": "x-pg-account-id", "value": "{{account_ref_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"instruction_ref_id\": \"{{$randomWord}}{{$randomInt}}\",\n    \"payment_rail\": \"ETRANSFER\",\n    \"transactions\": [\n        {\n            \"transaction_ref_id\": \"{{$randomUUID}}\",\n            \"payment_category\": \"COMPLETE_PAYMENT\",\n            \"amount\": {{$randomInt}}.00,\n            \"monetary_unit\": \"CAD\",\n            \"acceptance_date_time\": \"2022-09-24T20:12:00.001Z\"\n            \n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{TRANSACTION_API_HOST}}/v1/ledger/transaction", "host": ["{{TRANSACTION_API_HOST}}"], "path": ["v1", "ledger", "transaction"]}}, "response": []}, {"name": "Commit Transaction", "event": [{"listen": "prerequest", "script": {"exec": ["// Interaction Timestamp\r", "const moment = require('moment');\r", "const timestamp = moment.utc();\r", "pm.globals.set(\"timestamp\", timestamp);"], "type": "text/javascript"}}], "request": {"method": "PATCH", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{timestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}, {"key": "x-pg-account-id", "value": "{{account_ref_id}}", "type": "text"}], "url": {"raw": "{{TRANSACTION_API_HOST}}/v1/ledger/transaction/{{instruction_ref_id}}", "host": ["{{TRANSACTION_API_HOST}}"], "path": ["v1", "ledger", "transaction", "{{instruction_ref_id}}"]}}, "response": []}, {"name": "Retrieve Instruction", "event": [{"listen": "prerequest", "script": {"exec": ["// Interaction Timestamp\r", "const moment = require('moment');\r", "const timestamp = moment.utc();\r", "const futuretimestamp = moment().add(2, 'days').utc();\r", "pm.globals.set(\"timestamp\", timestamp);\r", "pm.globals.set(\"futuretimestamp\", futuretimestamp);"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{timestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}, {"key": "x-pg-account-id", "value": "{{account_ref_id}}", "type": "text"}], "url": {"raw": "{{TRANSACTION_API_HOST}}/v1/ledger/transaction/{{instruction_ref_id}}", "host": ["{{TRANSACTION_API_HOST}}"], "path": ["v1", "ledger", "transaction", "{{instruction_ref_id}}"]}}, "response": []}]}, {"name": "Sample end to end DEBIT flow", "item": [{"name": "Create new profile", "event": [{"listen": "prerequest", "script": {"exec": ["// Interaction Timestamp", "const moment = require('moment');", "const timestamp = moment.utc();", "pm.globals.set(\"timestamp\", timestamp);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test(\"New profile created\", function () {", "    var jsonBody = pm.response.json();", "    pm.expect(jsonBody.ref_id).not.equal(null);", "    pm.expect(jsonBody.status).equal('DISABLED');", "    ", "    pm.expect(jsonBody.created_date_time).not.equal(null);", "", "    // Populate customer_id for next request", "    pm.collectionVariables.set(\"profile_ref_id\", jsonBody.ref_id);", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{timestamp}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"crm_id\": \"{{$randomWord}}{{$randomInt}}\",\n    \"legal_name\": \"Legal Name of {{$randomWord}}\",\n    \"display_name\": \"Display Name of {{$randomWord}} {{$randomInt}}{{$randomInt}}{{$randomInt}}{{$randomInt}} Inc.\"\n}\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{PROFILE_API_HOST}}/v1/ledger/profile", "host": ["{{PROFILE_API_HOST}}"], "path": ["v1", "ledger", "profile"]}}, "response": []}, {"name": "Update profile status (ENABLED)", "event": [{"listen": "prerequest", "script": {"exec": ["// Interaction Timestamp", "const moment = require('moment');", "const timestamp = moment.utc();", "pm.globals.set(\"timestamp\", timestamp);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 204\", function () {", "    pm.response.to.have.status(204);", "});", "", "pm.test(\"Status updated with empty response\", function () {", "    pm.expect(responseBody).equal('');", "});", ""], "type": "text/javascript"}}], "request": {"method": "PATCH", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{timestamp}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"status\":\"ENABLED\",\n  \"reason\": \"Business relationship was activated\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{PROFILE_API_HOST}}/v1/ledger/profile/{{profile_ref_id}}/status", "host": ["{{PROFILE_API_HOST}}"], "path": ["v1", "ledger", "profile", "{{profile_ref_id}}", "status"]}}, "response": []}, {"name": "Create new account", "event": [{"listen": "prerequest", "script": {"exec": ["// Interaction Timestamp", "const moment = require('moment');", "const timestamp = moment.utc();", "pm.globals.set(\"timestamp\", timestamp);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test(\"New acount created\", function () {", "    var jsonBody = pm.response.json();", "    pm.expect(jsonBody.ref_id).not.equal(null);", "    pm.expect(jsonBody.status).equal('INACTIVE');", "    ", "    pm.expect(jsonBody.created_date_time).not.equal(null);", "", "    // Populate customer_id for next request", "    pm.collectionVariables.set(\"account_ref_id\", jsonBody.ref_id);", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{timestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Account {{$randomWord}}\",\n  \"description\": \"Account description {{$randomWord}}\",\n  \"monetary_unit\": \"CAD\",\n  \"options\": {\n    \"overdraft_amount\": 100000,\n    \"fund_hold_days\": 0\n  }\n}\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{ACCOUNT_API_HOST}}/v1/ledger/account", "host": ["{{ACCOUNT_API_HOST}}"], "path": ["v1", "ledger", "account"]}}, "response": []}, {"name": "Update account status (ACTIVE)", "event": [{"listen": "prerequest", "script": {"exec": ["// Interaction Timestamp", "const moment = require('moment');", "const timestamp = moment.utc();", "pm.globals.set(\"timestamp\", timestamp);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 204\", function () {", "    pm.response.to.have.status(204);", "});", "", "pm.test(\"Status updated with empty response\", function () {", "    pm.expect(responseBody).equal('');", "});", ""], "type": "text/javascript"}}], "request": {"method": "PATCH", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{timestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"ACTIVE\",\n  \"reason\": \"Launched on 2022-07-27\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{ACCOUNT_API_HOST}}/v1/ledger/account/{{account_ref_id}}/status", "host": ["{{ACCOUNT_API_HOST}}"], "path": ["v1", "ledger", "account", "{{account_ref_id}}", "status"]}}, "response": []}, {"name": "Internal transaction (Reserve funds)", "event": [{"listen": "prerequest", "script": {"exec": ["// Interaction Timestamp\r", "const moment = require('moment');\r", "const timestamp = moment.utc();\r", "const futuretimestamp = moment().add(2, 'days').utc();\r", "pm.globals.set(\"timestamp\", timestamp);\r", "pm.globals.set(\"futuretimestamp\", futuretimestamp);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test(\"New transaction initiated\", function () {", "    var jsonBody = pm.response.json();", "    pm.expect(jsonBody.instruction_ref_id).not.equal(null);", "        ", "    pm.expect(jsonBody.status).equal('PENDING');", "    ", "    // Populate customer_id for next request", "    pm.collectionVariables.set(\"instruction_ref_id\", jsonBody.instruction_ref_id);", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{timestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}, {"key": "x-pg-account-id", "value": "{{account_ref_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"instruction_ref_id\": \"{{$randomUUID}}\",\n    \"payment_rail\": \"INTERNAL\",\n    \"transactions\": [\n        {\n            \"transaction_ref_id\": \"{{$randomUUID}}\",\n            \"payment_category\": \"PREFUND_RESERVE\",\n            \"transaction_flow\":\"CREDIT\",\n            \"amount\": 1000000.00,\n            \"monetary_unit\": \"CAD\"\n\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{TRANSACTION_API_HOST}}/v1/ledger/transaction/internal", "host": ["{{TRANSACTION_API_HOST}}"], "path": ["v1", "ledger", "transaction", "internal"]}}, "response": []}, {"name": "Initiate Instruction (DEBIT only)", "event": [{"listen": "prerequest", "script": {"exec": ["// Interaction Timestamp\r", "const moment = require('moment');\r", "const timestamp = moment.utc();\r", "const futuretimestamp = moment().add(2, 'days').utc();\r", "pm.globals.set(\"timestamp\", timestamp);\r", "pm.globals.set(\"futuretimestamp\", futuretimestamp);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});\r", "\r", "pm.test(\"New transaction initiated\", function () {\r", "    var jsonBody = pm.response.json();\r", "    pm.expect(jsonBody.instruction_ref_id).not.equal(null);\r", "        \r", "    pm.expect(jsonBody.status).equal('PENDING');\r", "    \r", "    // Populate customer_id for next request\r", "    pm.collectionVariables.set(\"instruction_ref_id\", jsonBody.instruction_ref_id);\r", "});\r", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{timestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}, {"key": "x-pg-account-id", "value": "{{account_ref_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"instruction_ref_id\": \"{{$randomWord}}{{$randomInt}}\",\n    \"payment_rail\": \"ETRANSFER\",\n    \"transactions\": [\n        \n        {\n            \"transaction_ref_id\": \"{{$randomUUID}}\",\n            \"payment_category\": \"SEND_PAYMENT\",\n            \"amount\": 1007.5,\n            \"monetary_unit\": \"CAD\",\n            \"acceptance_date_time\": \"{{timestamp}}\",\n            \"due_date_time\":\"{{timestamp}}\"\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{TRANSACTION_API_HOST}}/v1/ledger/transaction", "host": ["{{TRANSACTION_API_HOST}}"], "path": ["v1", "ledger", "transaction"]}}, "response": []}, {"name": "Commit Transaction", "event": [{"listen": "prerequest", "script": {"exec": ["// Interaction Timestamp\r", "const moment = require('moment');\r", "const timestamp = moment.utc();\r", "pm.globals.set(\"timestamp\", timestamp);"], "type": "text/javascript"}}], "request": {"method": "PATCH", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{timestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}, {"key": "x-pg-account-id", "value": "{{account_ref_id}}", "type": "text"}], "url": {"raw": "{{TRANSACTION_API_HOST}}/v1/ledger/transaction/{{instruction_ref_id}}", "host": ["{{TRANSACTION_API_HOST}}"], "path": ["v1", "ledger", "transaction", "{{instruction_ref_id}}"]}}, "response": []}, {"name": "Retrieve Instruction", "event": [{"listen": "prerequest", "script": {"exec": ["// Interaction Timestamp\r", "const moment = require('moment');\r", "const timestamp = moment.utc();\r", "const futuretimestamp = moment().add(2, 'days').utc();\r", "pm.globals.set(\"timestamp\", timestamp);\r", "pm.globals.set(\"futuretimestamp\", futuretimestamp);"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{timestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}, {"key": "x-pg-account-id", "value": "{{account_ref_id}}", "type": "text"}], "url": {"raw": "{{TRANSACTION_API_HOST}}/v1/ledger/transaction/{{instruction_ref_id}}", "host": ["{{TRANSACTION_API_HOST}}"], "path": ["v1", "ledger", "transaction", "{{instruction_ref_id}}"]}}, "response": []}]}, {"name": "Sample end to end ROLLBACK flow", "item": [{"name": "Create new profile", "event": [{"listen": "prerequest", "script": {"exec": ["// Interaction Timestamp", "const moment = require('moment');", "const timestamp = moment.utc();", "pm.globals.set(\"timestamp\", timestamp);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test(\"New profile created\", function () {", "    var jsonBody = pm.response.json();", "    pm.expect(jsonBody.ref_id).not.equal(null);", "    pm.expect(jsonBody.status).equal('DISABLED');", "    ", "    pm.expect(jsonBody.created_date_time).not.equal(null);", "", "    // Populate customer_id for next request", "    pm.collectionVariables.set(\"profile_ref_id\", jsonBody.ref_id);", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{timestamp}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"crm_id\": \"{{$randomWord}}{{$randomInt}}\",\n    \"legal_name\": \"Legal Name of {{$randomWord}}\",\n    \"display_name\": \"Display Name of {{$randomWord}} {{$randomInt}}{{$randomInt}}{{$randomInt}}{{$randomInt}} Inc.\"\n}\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{PROFILE_API_HOST}}/v1/ledger/profile", "host": ["{{PROFILE_API_HOST}}"], "path": ["v1", "ledger", "profile"]}}, "response": []}, {"name": "Update profile status (ENABLED)", "event": [{"listen": "prerequest", "script": {"exec": ["// Interaction Timestamp", "const moment = require('moment');", "const timestamp = moment.utc();", "pm.globals.set(\"timestamp\", timestamp);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 204\", function () {", "    pm.response.to.have.status(204);", "});", "", "pm.test(\"Status updated with empty response\", function () {", "    pm.expect(responseBody).equal('');", "});", ""], "type": "text/javascript"}}], "request": {"method": "PATCH", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{timestamp}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"status\":\"ENABLED\",\n  \"reason\": \"Business relationship was activated\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{PROFILE_API_HOST}}/v1/ledger/profile/{{profile_ref_id}}/status", "host": ["{{PROFILE_API_HOST}}"], "path": ["v1", "ledger", "profile", "{{profile_ref_id}}", "status"]}}, "response": []}, {"name": "Create new account", "event": [{"listen": "prerequest", "script": {"exec": ["// Interaction Timestamp", "const moment = require('moment');", "const timestamp = moment.utc();", "pm.globals.set(\"timestamp\", timestamp);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test(\"New acount created\", function () {", "    var jsonBody = pm.response.json();", "    pm.expect(jsonBody.ref_id).not.equal(null);", "    pm.expect(jsonBody.status).equal('INACTIVE');", "    ", "    pm.expect(jsonBody.created_date_time).not.equal(null);", "", "    // Populate customer_id for next request", "    pm.collectionVariables.set(\"account_ref_id\", jsonBody.ref_id);", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{timestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Account {{$randomWord}}\",\n  \"description\": \"Account description {{$randomWord}}\",\n  \"monetary_unit\": \"CAD\",\n  \"options\": {\n    \"overdraft_amount\": 100000,\n    \"fund_hold_days\": 0\n  }\n}\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{ACCOUNT_API_HOST}}/v1/ledger/account", "host": ["{{ACCOUNT_API_HOST}}"], "path": ["v1", "ledger", "account"]}}, "response": []}, {"name": "Update account status (ACTIVE)", "event": [{"listen": "prerequest", "script": {"exec": ["// Interaction Timestamp", "const moment = require('moment');", "const timestamp = moment.utc();", "pm.globals.set(\"timestamp\", timestamp);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 204\", function () {", "    pm.response.to.have.status(204);", "});", "", "pm.test(\"Status updated with empty response\", function () {", "    pm.expect(responseBody).equal('');", "});", ""], "type": "text/javascript"}}], "request": {"method": "PATCH", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{timestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"ACTIVE\",\n  \"reason\": \"Launched on 2022-07-27\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{ACCOUNT_API_HOST}}/v1/ledger/account/{{account_ref_id}}/status", "host": ["{{ACCOUNT_API_HOST}}"], "path": ["v1", "ledger", "account", "{{account_ref_id}}", "status"]}}, "response": []}, {"name": "Initiate Instruction (CREDIT only)", "event": [{"listen": "prerequest", "script": {"exec": ["// Interaction Timestamp\r", "const moment = require('moment');\r", "const timestamp = moment.utc();\r", "const futuretimestamp = moment().add(2, 'days').utc();\r", "pm.globals.set(\"timestamp\", timestamp);\r", "pm.globals.set(\"futuretimestamp\", futuretimestamp);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});\r", "\r", "pm.test(\"New transaction initiated\", function () {\r", "    var jsonBody = pm.response.json();\r", "    pm.expect(jsonBody.instruction_ref_id).not.equal(null);\r", "        \r", "    pm.expect(jsonBody.status).equal('PENDING');\r", "    \r", "    // Populate customer_id for next request\r", "    pm.collectionVariables.set(\"instruction_ref_id\", jsonBody.instruction_ref_id);\r", "});\r", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{timestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}, {"key": "x-pg-account-id", "value": "{{account_ref_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"instruction_ref_id\": \"{{$randomWord}}{{$randomInt}}\",\n    \"payment_rail\": \"ETRANSFER\",\n    \"transactions\": [\n        {\n            \"transaction_ref_id\": \"{{$randomUUID}}\",\n            \"payment_category\": \"COMPLETE_PAYMENT\",\n            \"amount\": {{$randomInt}}.00,\n            \"monetary_unit\": \"CAD\",\n            \"acceptance_date_time\": \"2022-09-24T20:12:00.001Z\"\n            \n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{TRANSACTION_API_HOST}}/v1/ledger/transaction", "host": ["{{TRANSACTION_API_HOST}}"], "path": ["v1", "ledger", "transaction"]}}, "response": []}, {"name": "Rollback instruction", "event": [{"listen": "prerequest", "script": {"exec": ["// Interaction Timestamp\r", "const moment = require('moment');\r", "const timestamp = moment.utc();\r", "pm.globals.set(\"timestamp\", timestamp);"], "type": "text/javascript"}}], "request": {"method": "DELETE", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{timestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}, {"key": "x-pg-account-id", "value": "{{account_ref_id}}", "type": "text"}], "url": {"raw": "{{TRANSACTION_API_HOST}}/v1/ledger/transaction/{{instruction_ref_id}}", "host": ["{{TRANSACTION_API_HOST}}"], "path": ["v1", "ledger", "transaction", "{{instruction_ref_id}}"]}}, "response": []}, {"name": "Retrieve Instruction", "event": [{"listen": "prerequest", "script": {"exec": ["// Interaction Timestamp\r", "const moment = require('moment');\r", "const timestamp = moment.utc();\r", "const futuretimestamp = moment().add(2, 'days').utc();\r", "pm.globals.set(\"timestamp\", timestamp);\r", "pm.globals.set(\"futuretimestamp\", futuretimestamp);"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{timestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}, {"key": "x-pg-account-id", "value": "{{account_ref_id}}", "type": "text"}], "url": {"raw": "{{TRANSACTION_API_HOST}}/v1/ledger/transaction/{{instruction_ref_id}}", "host": ["{{TRANSACTION_API_HOST}}"], "path": ["v1", "ledger", "transaction", "{{instruction_ref_id}}"]}}, "response": []}]}, {"name": "Sample end to end REVERSE flow", "item": [{"name": "Create new profile", "event": [{"listen": "prerequest", "script": {"exec": ["// Interaction Timestamp", "const moment = require('moment');", "const timestamp = moment.utc();", "pm.globals.set(\"timestamp\", timestamp);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test(\"New profile created\", function () {", "    var jsonBody = pm.response.json();", "    pm.expect(jsonBody.ref_id).not.equal(null);", "    pm.expect(jsonBody.status).equal('DISABLED');", "    ", "    pm.expect(jsonBody.created_date_time).not.equal(null);", "", "    // Populate customer_id for next request", "    pm.collectionVariables.set(\"profile_ref_id\", jsonBody.ref_id);", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{timestamp}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"crm_id\": \"{{$randomWord}}{{$randomInt}}\",\n    \"legal_name\": \"Legal Name of {{$randomWord}}\",\n    \"display_name\": \"Display Name of {{$randomWord}} {{$randomInt}}{{$randomInt}}{{$randomInt}}{{$randomInt}} Inc.\"\n}\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{PROFILE_API_HOST}}/v1/ledger/profile", "host": ["{{PROFILE_API_HOST}}"], "path": ["v1", "ledger", "profile"]}}, "response": []}, {"name": "Update profile status (ENABLED)", "event": [{"listen": "prerequest", "script": {"exec": ["// Interaction Timestamp", "const moment = require('moment');", "const timestamp = moment.utc();", "pm.globals.set(\"timestamp\", timestamp);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 204\", function () {", "    pm.response.to.have.status(204);", "});", "", "pm.test(\"Status updated with empty response\", function () {", "    pm.expect(responseBody).equal('');", "});", ""], "type": "text/javascript"}}], "request": {"method": "PATCH", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{timestamp}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"status\":\"ENABLED\",\n  \"reason\": \"Business relationship was activated\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{PROFILE_API_HOST}}/v1/ledger/profile/{{profile_ref_id}}/status", "host": ["{{PROFILE_API_HOST}}"], "path": ["v1", "ledger", "profile", "{{profile_ref_id}}", "status"]}}, "response": []}, {"name": "Create new account", "event": [{"listen": "prerequest", "script": {"exec": ["// Interaction Timestamp", "const moment = require('moment');", "const timestamp = moment.utc();", "pm.globals.set(\"timestamp\", timestamp);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test(\"New acount created\", function () {", "    var jsonBody = pm.response.json();", "    pm.expect(jsonBody.ref_id).not.equal(null);", "    pm.expect(jsonBody.status).equal('INACTIVE');", "    ", "    pm.expect(jsonBody.created_date_time).not.equal(null);", "", "    // Populate customer_id for next request", "    pm.collectionVariables.set(\"account_ref_id\", jsonBody.ref_id);", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{timestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Account {{$randomWord}}\",\n  \"description\": \"Account description {{$randomWord}}\",\n  \"monetary_unit\": \"CAD\",\n  \"options\": {\n    \"overdraft_amount\": 100000,\n    \"fund_hold_days\": 0\n  }\n}\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{ACCOUNT_API_HOST}}/v1/ledger/account", "host": ["{{ACCOUNT_API_HOST}}"], "path": ["v1", "ledger", "account"]}}, "response": []}, {"name": "Update account status (ACTIVE)", "event": [{"listen": "prerequest", "script": {"exec": ["// Interaction Timestamp", "const moment = require('moment');", "const timestamp = moment.utc();", "pm.globals.set(\"timestamp\", timestamp);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 204\", function () {", "    pm.response.to.have.status(204);", "});", "", "pm.test(\"Status updated with empty response\", function () {", "    pm.expect(responseBody).equal('');", "});", ""], "type": "text/javascript"}}], "request": {"method": "PATCH", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{timestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"ACTIVE\",\n  \"reason\": \"Launched on 2022-07-27\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{ACCOUNT_API_HOST}}/v1/ledger/account/{{account_ref_id}}/status", "host": ["{{ACCOUNT_API_HOST}}"], "path": ["v1", "ledger", "account", "{{account_ref_id}}", "status"]}}, "response": []}, {"name": "Internal transaction (Reserve funds)", "event": [{"listen": "prerequest", "script": {"exec": ["// Interaction Timestamp\r", "const moment = require('moment');\r", "const timestamp = moment.utc();\r", "const futuretimestamp = moment().add(2, 'days').utc();\r", "pm.globals.set(\"timestamp\", timestamp);\r", "pm.globals.set(\"futuretimestamp\", futuretimestamp);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test(\"New transaction initiated\", function () {", "    var jsonBody = pm.response.json();", "    pm.expect(jsonBody.instruction_ref_id).not.equal(null);", "        ", "    pm.expect(jsonBody.status).equal('PENDING');", "    ", "    // Populate customer_id for next request", "    pm.collectionVariables.set(\"instruction_ref_id\", jsonBody.instruction_ref_id);", "});", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{timestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}, {"key": "x-pg-account-id", "value": "{{account_ref_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"instruction_ref_id\": \"{{$randomUUID}}\",\n    \"payment_rail\": \"INTERNAL\",\n    \"transactions\": [\n        {\n            \"transaction_ref_id\": \"{{$randomUUID}}\",\n            \"payment_category\": \"PREFUND_RESERVE\",\n            \"transaction_flow\":\"CREDIT\",\n            \"amount\": 1000000.00,\n            \"monetary_unit\": \"CAD\"\n\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{TRANSACTION_API_HOST}}/v1/ledger/transaction/internal", "host": ["{{TRANSACTION_API_HOST}}"], "path": ["v1", "ledger", "transaction", "internal"]}}, "response": []}, {"name": "Initiate Instruction (DEBIT only)", "event": [{"listen": "prerequest", "script": {"exec": ["// Interaction Timestamp\r", "const moment = require('moment');\r", "const timestamp = moment.utc();\r", "const futuretimestamp = moment().add(2, 'days').utc();\r", "pm.globals.set(\"timestamp\", timestamp);\r", "pm.globals.set(\"futuretimestamp\", futuretimestamp);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});\r", "\r", "pm.test(\"New transaction initiated\", function () {\r", "    var jsonBody = pm.response.json();\r", "    pm.expect(jsonBody.instruction_ref_id).not.equal(null);\r", "        \r", "    pm.expect(jsonBody.status).equal('PENDING');\r", "    \r", "    // Populate IDs for next request\r", "    pm.collectionVariables.set(\"instruction_ref_id\", jsonBody.instruction_ref_id);\r", "    pm.collectionVariables.set(\"transaction_ref_id\", jsonBody.transactions[0].transaction_ref_id);\r", "});\r", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{timestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}, {"key": "x-pg-account-id", "value": "{{account_ref_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"instruction_ref_id\": \"{{$randomWord}}{{$randomInt}}\",\n    \"payment_rail\": \"ETRANSFER\",\n    \"transactions\": [\n        \n        {\n            \"transaction_ref_id\": \"{{$randomUUID}}\",\n            \"payment_category\": \"SEND_PAYMENT\",\n            \"amount\": 1007.5,\n            \"monetary_unit\": \"CAD\",\n            \"acceptance_date_time\": \"{{timestamp}}\",\n            \"due_date_time\":\"{{timestamp}}\"\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{TRANSACTION_API_HOST}}/v1/ledger/transaction", "host": ["{{TRANSACTION_API_HOST}}"], "path": ["v1", "ledger", "transaction"]}}, "response": []}, {"name": "Commit Transaction", "event": [{"listen": "prerequest", "script": {"exec": ["// Interaction Timestamp\r", "const moment = require('moment');\r", "const timestamp = moment.utc();\r", "pm.globals.set(\"timestamp\", timestamp);"], "type": "text/javascript"}}], "request": {"method": "PATCH", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{timestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}, {"key": "x-pg-account-id", "value": "{{account_ref_id}}", "type": "text"}], "url": {"raw": "{{TRANSACTION_API_HOST}}/v1/ledger/transaction/{{instruction_ref_id}}", "host": ["{{TRANSACTION_API_HOST}}"], "path": ["v1", "ledger", "transaction", "{{instruction_ref_id}}"]}}, "response": []}, {"name": "Retrieve Instruction", "event": [{"listen": "prerequest", "script": {"exec": ["// Interaction Timestamp\r", "const moment = require('moment');\r", "const timestamp = moment.utc();\r", "const futuretimestamp = moment().add(2, 'days').utc();\r", "pm.globals.set(\"timestamp\", timestamp);\r", "pm.globals.set(\"futuretimestamp\", futuretimestamp);"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{timestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}, {"key": "x-pg-account-id", "value": "{{account_ref_id}}", "type": "text"}], "url": {"raw": "{{TRANSACTION_API_HOST}}/v1/ledger/transaction/{{instruction_ref_id}}", "host": ["{{TRANSACTION_API_HOST}}"], "path": ["v1", "ledger", "transaction", "{{instruction_ref_id}}"]}}, "response": []}, {"name": "Reverse Transaction", "event": [{"listen": "prerequest", "script": {"exec": ["// Interaction Timestamp\r", "const moment = require('moment');\r", "const timestamp = moment.utc();\r", "const futuretimestamp = moment().add(2, 'days').utc();\r", "pm.globals.set(\"timestamp\", timestamp);\r", "pm.globals.set(\"futuretimestamp\", futuretimestamp);"], "type": "text/javascript"}}], "request": {"method": "DELETE", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{timestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}, {"key": "x-pg-account-id", "value": "{{account_ref_id}}", "type": "text"}], "url": {"raw": "{{TRANSACTION_API_HOST}}/v1/ledger/transaction/{{instruction_ref_id}}/{{transaction_ref_id}}", "host": ["{{TRANSACTION_API_HOST}}"], "path": ["v1", "ledger", "transaction", "{{instruction_ref_id}}", "{{transaction_ref_id}}"]}}, "response": []}, {"name": "Retrieve Instruction", "event": [{"listen": "prerequest", "script": {"exec": ["// Interaction Timestamp\r", "const moment = require('moment');\r", "const timestamp = moment.utc();\r", "const futuretimestamp = moment().add(2, 'days').utc();\r", "pm.globals.set(\"timestamp\", timestamp);\r", "pm.globals.set(\"futuretimestamp\", futuretimestamp);"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{timestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}, {"key": "x-pg-account-id", "value": "{{account_ref_id}}", "type": "text"}], "url": {"raw": "{{TRANSACTION_API_HOST}}/v1/ledger/transaction/{{instruction_ref_id}}", "host": ["{{TRANSACTION_API_HOST}}"], "path": ["v1", "ledger", "transaction", "{{instruction_ref_id}}"]}}, "response": []}]}]}], "auth": {"type": "awsv4", "awsv4": [{"key": "addAuthDataToQuery", "value": false, "type": "boolean"}, {"key": "service", "value": "", "type": "string"}, {"key": "region", "value": "ca-central-1", "type": "string"}, {"key": "secret<PERSON>ey", "value": "{{aws_iam_secret_key}}", "type": "string"}, {"key": "accessKey", "value": "{{aws_iam_access_key}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "profile_ref_id", "value": "bd990028-dfb8-4aba-b451-6cbbc022a142"}, {"key": "account_ref_id", "value": "4e79848a-33ca-43fd-9ba8-c94b9f2ad768"}, {"key": "instruction_ref_id", "value": "Account204"}, {"key": "transaction_ref_id", "value": "0f4ad19d-bc8b-4454-a661-c61fb6a59108"}, {"key": "oauth2_client_id", "value": "6fce6kvabklju4cnpe0db5dme5"}, {"key": "oauth2_secret", "value": "u745kbp28jthsinpjrrghj81aud5gi0tq8cm2232s481ihet38d"}, {"key": "aws_iam_access_key", "value": "********************"}, {"key": "aws_iam_secret_key", "value": "si5F3aN9dfqrWbEfq4Qo2wuLkMaqPDInhU7vOJ0i"}]}