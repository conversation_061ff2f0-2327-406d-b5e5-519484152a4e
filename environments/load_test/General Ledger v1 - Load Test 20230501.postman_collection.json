{"info": {"_postman_id": "f957c88c-cdc2-45f5-be85-207e204b6372", "name": "General Ledger v1 - Load Test ********", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Service Account A", "item": [{"name": "Scenario 01 -- CREDIT", "item": [{"name": "Initiate Instruction (CREDIT only)", "event": [{"listen": "prerequest", "script": {"exec": ["var uuid = require('uuid'); var pg_uuid = uuid.v4();", "pm.collectionVariables.set(\"instruction_ref_id\", pg_uuid);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});\r", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{$isoTimestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}, {"key": "x-pg-account-id", "value": "{{account_ref_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"instruction_ref_id\": \"{{instruction_ref_id}}\",\n    \"payment_rail\": \"ETRANSFER\",\n    \"transactions\": [\n        {\n            \"transaction_ref_id\": \"{{instruction_ref_id}}\",\n            \"payment_category\": \"COMPLETE_PAYMENT\",\n            \"amount\": {{$randomInt}}.00,\n            \"monetary_unit\": \"CAD\",\n            \"acceptance_date_time\": \"{{$isoTimestamp}}\"\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{TRANSACTION_API_HOST}}/v1/ledger/transaction", "host": ["{{TRANSACTION_API_HOST}}"], "path": ["v1", "ledger", "transaction"]}}, "response": []}, {"name": "Commit Transaction", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 200\", function () {", "    pm.response.to.have.status(200);", "});", "", ""], "type": "text/javascript"}}], "request": {"method": "PATCH", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{$isoTimestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}, {"key": "x-pg-account-id", "value": "{{account_ref_id}}", "type": "text"}], "url": {"raw": "{{TRANSACTION_API_HOST}}/v1/ledger/transaction/{{instruction_ref_id}}", "host": ["{{TRANSACTION_API_HOST}}"], "path": ["v1", "ledger", "transaction", "{{instruction_ref_id}}"]}}, "response": []}]}, {"name": "Scenario 02 -- DEBIT", "item": [{"name": "Initiate Instruction (DEBIT only)", "event": [{"listen": "prerequest", "script": {"exec": ["var uuid = require('uuid'); var pg_uuid = uuid.v4();", "pm.collectionVariables.set(\"instruction_ref_id\", pg_uuid);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});\r", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{$isoTimestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}, {"key": "x-pg-account-id", "value": "{{account_ref_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"instruction_ref_id\": \"{{instruction_ref_id}}\",\n    \"payment_rail\": \"ETRANSFER\",\n    \"transactions\": [\n        {\n            \"transaction_ref_id\": \"{{instruction_ref_id}}\",\n            \"payment_category\": \"SEND_PAYMENT\",\n            \"amount\": 1007.5,\n            \"monetary_unit\": \"CAD\",\n            \"acceptance_date_time\": \"{{$isoTimestamp}}\",\n            \"due_date_time\": \"{{$isoTimestamp}}\"\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{TRANSACTION_API_HOST}}/v1/ledger/transaction", "host": ["{{TRANSACTION_API_HOST}}"], "path": ["v1", "ledger", "transaction"]}}, "response": []}, {"name": "Commit Transaction", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 200\", function () {", "    pm.response.to.have.status(200);", "});", ""], "type": "text/javascript"}}], "request": {"method": "PATCH", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{$isoTimestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}, {"key": "x-pg-account-id", "value": "{{account_ref_id}}", "type": "text"}], "url": {"raw": "{{TRANSACTION_API_HOST}}/v1/ledger/transaction/{{instruction_ref_id}}", "host": ["{{TRANSACTION_API_HOST}}"], "path": ["v1", "ledger", "transaction", "{{instruction_ref_id}}"]}}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["pm.collectionVariables.set(\"profile_ref_id\", \"534c68e4-f706-4e21-b33f-49845e6d50b0\");", "", "pm.collectionVariables.set(\"account_ref_id\", \"d882edfc-7016-42b4-bd33-1d6cbbb8db60\");", "", "", "\t"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}]}, {"name": "Service Account B", "item": [{"name": "Scenario 01 -- CREDIT", "item": [{"name": "Initiate Instruction (CREDIT only)", "event": [{"listen": "prerequest", "script": {"exec": ["var uuid = require('uuid'); var pg_uuid = uuid.v4();", "pm.collectionVariables.set(\"instruction_ref_id\", pg_uuid);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});\r", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{$isoTimestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}, {"key": "x-pg-account-id", "value": "{{account_ref_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"instruction_ref_id\": \"{{instruction_ref_id}}\",\n    \"payment_rail\": \"ETRANSFER\",\n    \"transactions\": [\n        {\n            \"transaction_ref_id\": \"{{instruction_ref_id}}\",\n            \"payment_category\": \"COMPLETE_PAYMENT\",\n            \"amount\": {{$randomInt}}.00,\n            \"monetary_unit\": \"CAD\",\n            \"acceptance_date_time\": \"{{$isoTimestamp}}\"\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{TRANSACTION_API_HOST}}/v1/ledger/transaction", "host": ["{{TRANSACTION_API_HOST}}"], "path": ["v1", "ledger", "transaction"]}}, "response": []}, {"name": "Commit Transaction", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 200\", function () {", "    pm.response.to.have.status(200);", "});", ""], "type": "text/javascript"}}], "request": {"method": "PATCH", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{$isoTimestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}, {"key": "x-pg-account-id", "value": "{{account_ref_id}}", "type": "text"}], "url": {"raw": "{{TRANSACTION_API_HOST}}/v1/ledger/transaction/{{instruction_ref_id}}", "host": ["{{TRANSACTION_API_HOST}}"], "path": ["v1", "ledger", "transaction", "{{instruction_ref_id}}"]}}, "response": []}]}, {"name": "Scenario 02 -- DEBIT", "item": [{"name": "Initiate Instruction (DEBIT only)", "event": [{"listen": "prerequest", "script": {"exec": ["var uuid = require('uuid'); var pg_uuid = uuid.v4();", "pm.collectionVariables.set(\"instruction_ref_id\", pg_uuid);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});\r", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{$isoTimestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}, {"key": "x-pg-account-id", "value": "{{account_ref_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"instruction_ref_id\": \"{{instruction_ref_id}}\",\n    \"payment_rail\": \"ETRANSFER\",\n    \"transactions\": [\n        {\n            \"transaction_ref_id\": \"{{instruction_ref_id}}\",\n            \"payment_category\": \"SEND_PAYMENT\",\n            \"amount\": 1007.5,\n            \"monetary_unit\": \"CAD\",\n            \"acceptance_date_time\": \"{{$isoTimestamp}}\",\n            \"due_date_time\": \"{{$isoTimestamp}}\"\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{TRANSACTION_API_HOST}}/v1/ledger/transaction", "host": ["{{TRANSACTION_API_HOST}}"], "path": ["v1", "ledger", "transaction"]}}, "response": []}, {"name": "Commit Transaction", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 200\", function () {", "    pm.response.to.have.status(200);", "});", ""], "type": "text/javascript"}}], "request": {"method": "PATCH", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{$isoTimestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}, {"key": "x-pg-account-id", "value": "{{account_ref_id}}", "type": "text"}], "url": {"raw": "{{TRANSACTION_API_HOST}}/v1/ledger/transaction/{{instruction_ref_id}}", "host": ["{{TRANSACTION_API_HOST}}"], "path": ["v1", "ledger", "transaction", "{{instruction_ref_id}}"]}}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["pm.collectionVariables.set(\"profile_ref_id\", \"0e6806af-d127-4196-864b-ac2fb383d77b\");", "", "pm.collectionVariables.set(\"account_ref_id\", \"85eb3436-f439-4bcc-8c29-65de1194d96d\");", ""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}]}, {"name": "Service Account C", "item": [{"name": "Scenario 01 -- CREDIT", "item": [{"name": "Initiate Instruction (CREDIT only)", "event": [{"listen": "prerequest", "script": {"exec": ["var uuid = require('uuid'); var pg_uuid = uuid.v4();", "pm.collectionVariables.set(\"instruction_ref_id\", pg_uuid);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});\r", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{$isoTimestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}, {"key": "x-pg-account-id", "value": "{{account_ref_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"instruction_ref_id\": \"{{instruction_ref_id}}\",\n    \"payment_rail\": \"ETRANSFER\",\n    \"transactions\": [\n        {\n            \"transaction_ref_id\": \"{{instruction_ref_id}}\",\n            \"payment_category\": \"COMPLETE_PAYMENT\",\n            \"amount\": {{$randomInt}}.00,\n            \"monetary_unit\": \"CAD\",\n            \"acceptance_date_time\": \"{{$isoTimestamp}}\"\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{TRANSACTION_API_HOST}}/v1/ledger/transaction", "host": ["{{TRANSACTION_API_HOST}}"], "path": ["v1", "ledger", "transaction"]}}, "response": []}, {"name": "Commit Transaction", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 200\", function () {", "    pm.response.to.have.status(200);", "});", ""], "type": "text/javascript"}}], "request": {"method": "PATCH", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{$isoTimestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}, {"key": "x-pg-account-id", "value": "{{account_ref_id}}", "type": "text"}], "url": {"raw": "{{TRANSACTION_API_HOST}}/v1/ledger/transaction/{{instruction_ref_id}}", "host": ["{{TRANSACTION_API_HOST}}"], "path": ["v1", "ledger", "transaction", "{{instruction_ref_id}}"]}}, "response": []}]}, {"name": "Scenario 02 -- DEBIT", "item": [{"name": "Initiate Instruction (DEBIT only)", "event": [{"listen": "prerequest", "script": {"exec": ["var uuid = require('uuid'); var pg_uuid = uuid.v4();", "pm.collectionVariables.set(\"instruction_ref_id\", pg_uuid);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});\r", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{$isoTimestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}, {"key": "x-pg-account-id", "value": "{{account_ref_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"instruction_ref_id\": \"{{instruction_ref_id}}\",\n    \"payment_rail\": \"ETRANSFER\",\n    \"transactions\": [\n        {\n            \"transaction_ref_id\": \"{{instruction_ref_id}}\",\n            \"payment_category\": \"SEND_PAYMENT\",\n            \"amount\": 1007.5,\n            \"monetary_unit\": \"CAD\",\n            \"acceptance_date_time\": \"{{$isoTimestamp}}\",\n            \"due_date_time\": \"{{$isoTimestamp}}\"\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{TRANSACTION_API_HOST}}/v1/ledger/transaction", "host": ["{{TRANSACTION_API_HOST}}"], "path": ["v1", "ledger", "transaction"]}}, "response": []}, {"name": "Commit Transaction", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 200\", function () {", "    pm.response.to.have.status(200);", "});", ""], "type": "text/javascript"}}], "request": {"method": "PATCH", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{$isoTimestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}, {"key": "x-pg-account-id", "value": "{{account_ref_id}}", "type": "text"}], "url": {"raw": "{{TRANSACTION_API_HOST}}/v1/ledger/transaction/{{instruction_ref_id}}", "host": ["{{TRANSACTION_API_HOST}}"], "path": ["v1", "ledger", "transaction", "{{instruction_ref_id}}"]}}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["pm.collectionVariables.set(\"profile_ref_id\", \"0367b04c-03e1-4754-bbf3-c2010a56415f\");", "", "pm.collectionVariables.set(\"account_ref_id\", \"********-eddf-449e-b9a1-2a91a2c3d7fb\");", ""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}]}, {"name": "Service Account D", "item": [{"name": "Scenario 01 -- CREDIT", "item": [{"name": "Initiate Instruction (CREDIT only)", "event": [{"listen": "prerequest", "script": {"exec": ["var uuid = require('uuid'); var pg_uuid = uuid.v4();", "pm.collectionVariables.set(\"instruction_ref_id\", pg_uuid);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});\r", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{$isoTimestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}, {"key": "x-pg-account-id", "value": "{{account_ref_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"instruction_ref_id\": \"{{instruction_ref_id}}\",\n    \"payment_rail\": \"ETRANSFER\",\n    \"transactions\": [\n        {\n            \"transaction_ref_id\": \"{{instruction_ref_id}}\",\n            \"payment_category\": \"COMPLETE_PAYMENT\",\n            \"amount\": {{$randomInt}}.00,\n            \"monetary_unit\": \"CAD\",\n            \"acceptance_date_time\": \"{{$isoTimestamp}}\"\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{TRANSACTION_API_HOST}}/v1/ledger/transaction", "host": ["{{TRANSACTION_API_HOST}}"], "path": ["v1", "ledger", "transaction"]}}, "response": []}, {"name": "Commit Transaction", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 200\", function () {", "    pm.response.to.have.status(200);", "});", ""], "type": "text/javascript"}}], "request": {"method": "PATCH", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{$isoTimestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}, {"key": "x-pg-account-id", "value": "{{account_ref_id}}", "type": "text"}], "url": {"raw": "{{TRANSACTION_API_HOST}}/v1/ledger/transaction/{{instruction_ref_id}}", "host": ["{{TRANSACTION_API_HOST}}"], "path": ["v1", "ledger", "transaction", "{{instruction_ref_id}}"]}}, "response": []}]}, {"name": "Scenario 02 -- DEBIT", "item": [{"name": "Initiate Instruction (DEBIT only)", "event": [{"listen": "prerequest", "script": {"exec": ["var uuid = require('uuid'); var pg_uuid = uuid.v4();", "pm.collectionVariables.set(\"instruction_ref_id\", pg_uuid);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});\r", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{$isoTimestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}, {"key": "x-pg-account-id", "value": "{{account_ref_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"instruction_ref_id\": \"{{instruction_ref_id}}\",\n    \"payment_rail\": \"ETRANSFER\",\n    \"transactions\": [\n        {\n            \"transaction_ref_id\": \"{{instruction_ref_id}}\",\n            \"payment_category\": \"SEND_PAYMENT\",\n            \"amount\": 1007.5,\n            \"monetary_unit\": \"CAD\",\n            \"acceptance_date_time\": \"{{$isoTimestamp}}\",\n            \"due_date_time\": \"{{$isoTimestamp}}\"\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{TRANSACTION_API_HOST}}/v1/ledger/transaction", "host": ["{{TRANSACTION_API_HOST}}"], "path": ["v1", "ledger", "transaction"]}}, "response": []}, {"name": "Commit Transaction", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 200\", function () {", "    pm.response.to.have.status(200);", "});", ""], "type": "text/javascript"}}], "request": {"method": "PATCH", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{$isoTimestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}, {"key": "x-pg-account-id", "value": "{{account_ref_id}}", "type": "text"}], "url": {"raw": "{{TRANSACTION_API_HOST}}/v1/ledger/transaction/{{instruction_ref_id}}", "host": ["{{TRANSACTION_API_HOST}}"], "path": ["v1", "ledger", "transaction", "{{instruction_ref_id}}"]}}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["pm.collectionVariables.set(\"profile_ref_id\", \"0367b04c-03e1-4754-bbf3-c2010a56415f\");", "", "pm.collectionVariables.set(\"account_ref_id\", \"9354c50d-a196-419c-817c-60e9aecb8289\");", ""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}]}, {"name": "Service Account E", "item": [{"name": "Scenario 01 -- CREDIT", "item": [{"name": "Initiate Instruction (CREDIT only)", "event": [{"listen": "prerequest", "script": {"exec": ["var uuid = require('uuid'); var pg_uuid = uuid.v4();", "pm.collectionVariables.set(\"instruction_ref_id\", pg_uuid);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});\r", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{$isoTimestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}, {"key": "x-pg-account-id", "value": "{{account_ref_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"instruction_ref_id\": \"{{instruction_ref_id}}\",\n    \"payment_rail\": \"ETRANSFER\",\n    \"transactions\": [\n        {\n            \"transaction_ref_id\": \"{{instruction_ref_id}}\",\n            \"payment_category\": \"COMPLETE_PAYMENT\",\n            \"amount\": {{$randomInt}}.00,\n            \"monetary_unit\": \"CAD\",\n            \"acceptance_date_time\": \"{{$isoTimestamp}}\"\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{TRANSACTION_API_HOST}}/v1/ledger/transaction", "host": ["{{TRANSACTION_API_HOST}}"], "path": ["v1", "ledger", "transaction"]}}, "response": []}, {"name": "Commit Transaction", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 200\", function () {", "    pm.response.to.have.status(200);", "});", ""], "type": "text/javascript"}}], "request": {"method": "PATCH", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{$isoTimestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}, {"key": "x-pg-account-id", "value": "{{account_ref_id}}", "type": "text"}], "url": {"raw": "{{TRANSACTION_API_HOST}}/v1/ledger/transaction/{{instruction_ref_id}}", "host": ["{{TRANSACTION_API_HOST}}"], "path": ["v1", "ledger", "transaction", "{{instruction_ref_id}}"]}}, "response": []}]}, {"name": "Scenario 02 -- DEBIT", "item": [{"name": "Initiate Instruction (DEBIT only)", "event": [{"listen": "prerequest", "script": {"exec": ["var uuid = require('uuid'); var pg_uuid = uuid.v4();", "pm.collectionVariables.set(\"instruction_ref_id\", pg_uuid);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});\r", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{$isoTimestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}, {"key": "x-pg-account-id", "value": "{{account_ref_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"instruction_ref_id\": \"{{instruction_ref_id}}\",\n    \"payment_rail\": \"ETRANSFER\",\n    \"transactions\": [\n        {\n            \"transaction_ref_id\": \"{{instruction_ref_id}}\",\n            \"payment_category\": \"SEND_PAYMENT\",\n            \"amount\": 1007.5,\n            \"monetary_unit\": \"CAD\",\n            \"acceptance_date_time\": \"{{$isoTimestamp}}\",\n            \"due_date_time\": \"{{$isoTimestamp}}\"\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{TRANSACTION_API_HOST}}/v1/ledger/transaction", "host": ["{{TRANSACTION_API_HOST}}"], "path": ["v1", "ledger", "transaction"]}}, "response": []}, {"name": "Commit Transaction", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 200\", function () {", "    pm.response.to.have.status(200);", "});", ""], "type": "text/javascript"}}], "request": {"method": "PATCH", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{$isoTimestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}, {"key": "x-pg-account-id", "value": "{{account_ref_id}}", "type": "text"}], "url": {"raw": "{{TRANSACTION_API_HOST}}/v1/ledger/transaction/{{instruction_ref_id}}", "host": ["{{TRANSACTION_API_HOST}}"], "path": ["v1", "ledger", "transaction", "{{instruction_ref_id}}"]}}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["pm.collectionVariables.set(\"profile_ref_id\", \"f588ce0e-21bc-473d-8e32-cd378a3156fe\");", "", "pm.collectionVariables.set(\"account_ref_id\", \"ccedc394-f310-42d5-a2ab-ae7cfbe9f67d\");", ""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}]}, {"name": "Service Account F", "item": [{"name": "Scenario 01 -- CREDIT", "item": [{"name": "Initiate Instruction (CREDIT only)", "event": [{"listen": "prerequest", "script": {"exec": ["var uuid = require('uuid'); var pg_uuid = uuid.v4();", "pm.collectionVariables.set(\"instruction_ref_id\", pg_uuid);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});\r", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{$isoTimestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}, {"key": "x-pg-account-id", "value": "{{account_ref_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"instruction_ref_id\": \"{{instruction_ref_id}}\",\n    \"payment_rail\": \"ETRANSFER\",\n    \"transactions\": [\n        {\n            \"transaction_ref_id\": \"{{instruction_ref_id}}\",\n            \"payment_category\": \"COMPLETE_PAYMENT\",\n            \"amount\": {{$randomInt}}.00,\n            \"monetary_unit\": \"CAD\",\n            \"acceptance_date_time\": \"{{$isoTimestamp}}\"\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{TRANSACTION_API_HOST}}/v1/ledger/transaction", "host": ["{{TRANSACTION_API_HOST}}"], "path": ["v1", "ledger", "transaction"]}}, "response": []}, {"name": "Commit Transaction", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 200\", function () {", "    pm.response.to.have.status(200);", "});", ""], "type": "text/javascript"}}], "request": {"method": "PATCH", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{$isoTimestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}, {"key": "x-pg-account-id", "value": "{{account_ref_id}}", "type": "text"}], "url": {"raw": "{{TRANSACTION_API_HOST}}/v1/ledger/transaction/{{instruction_ref_id}}", "host": ["{{TRANSACTION_API_HOST}}"], "path": ["v1", "ledger", "transaction", "{{instruction_ref_id}}"]}}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["pm.collectionVariables.set(\"profile_ref_id\", \"f2bbd5f4-f5d8-405b-a2f4-efb39ad21473\");", "", "pm.collectionVariables.set(\"account_ref_id\", \"c286c0b9-58fb-4ecd-ad1f-58f98f9ad308\");", ""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}]}, {"name": "Service Account G", "item": [{"name": "Scenario 01 -- CREDIT", "item": [{"name": "Initiate Instruction (CREDIT only)", "event": [{"listen": "prerequest", "script": {"exec": ["var uuid = require('uuid'); var pg_uuid = uuid.v4();", "pm.collectionVariables.set(\"instruction_ref_id\", pg_uuid);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});\r", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{$isoTimestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}, {"key": "x-pg-account-id", "value": "{{account_ref_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"instruction_ref_id\": \"{{instruction_ref_id}}\",\n    \"payment_rail\": \"ETRANSFER\",\n    \"transactions\": [\n        {\n            \"transaction_ref_id\": \"{{instruction_ref_id}}\",\n            \"payment_category\": \"COMPLETE_PAYMENT\",\n            \"amount\": {{$randomInt}}.00,\n            \"monetary_unit\": \"CAD\",\n            \"acceptance_date_time\": \"{{$isoTimestamp}}\"\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{TRANSACTION_API_HOST}}/v1/ledger/transaction", "host": ["{{TRANSACTION_API_HOST}}"], "path": ["v1", "ledger", "transaction"]}}, "response": []}, {"name": "Commit Transaction", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 200\", function () {", "    pm.response.to.have.status(200);", "});", ""], "type": "text/javascript"}}], "request": {"method": "PATCH", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{$isoTimestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}, {"key": "x-pg-account-id", "value": "{{account_ref_id}}", "type": "text"}], "url": {"raw": "{{TRANSACTION_API_HOST}}/v1/ledger/transaction/{{instruction_ref_id}}", "host": ["{{TRANSACTION_API_HOST}}"], "path": ["v1", "ledger", "transaction", "{{instruction_ref_id}}"]}}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["pm.collectionVariables.set(\"profile_ref_id\", \"dece2da4-0f2a-4d08-beaa-bd91abc90648\");", "", "pm.collectionVariables.set(\"account_ref_id\", \"30d6b083-6a68-4016-bbc3-bdd4d98a2d75\");", ""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}]}, {"name": "Service Account H", "item": [{"name": "Scenario 01 -- CREDIT", "item": [{"name": "Initiate Instruction (CREDIT only)", "event": [{"listen": "prerequest", "script": {"exec": ["var uuid = require('uuid'); var pg_uuid = uuid.v4();", "pm.collectionVariables.set(\"instruction_ref_id\", pg_uuid);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});\r", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{$isoTimestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}, {"key": "x-pg-account-id", "value": "{{account_ref_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"instruction_ref_id\": \"{{instruction_ref_id}}\",\n    \"payment_rail\": \"ETRANSFER\",\n    \"transactions\": [\n        {\n            \"transaction_ref_id\": \"{{instruction_ref_id}}\",\n            \"payment_category\": \"COMPLETE_PAYMENT\",\n            \"amount\": {{$randomInt}}.00,\n            \"monetary_unit\": \"CAD\",\n            \"acceptance_date_time\": \"{{$isoTimestamp}}\"\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{TRANSACTION_API_HOST}}/v1/ledger/transaction", "host": ["{{TRANSACTION_API_HOST}}"], "path": ["v1", "ledger", "transaction"]}}, "response": []}, {"name": "Commit Transaction", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"HTTP Status Code == 200\", function () {", "    pm.response.to.have.status(200);", "});", ""], "type": "text/javascript"}}], "request": {"method": "PATCH", "header": [{"key": "x-pg-interaction-id", "value": "{{$guid}}", "type": "text"}, {"key": "x-pg-interaction-timestamp", "value": "{{$isoTimestamp}}", "type": "text"}, {"key": "x-pg-profile-id", "value": "{{profile_ref_id}}", "type": "text"}, {"key": "x-pg-account-id", "value": "{{account_ref_id}}", "type": "text"}], "url": {"raw": "{{TRANSACTION_API_HOST}}/v1/ledger/transaction/{{instruction_ref_id}}", "host": ["{{TRANSACTION_API_HOST}}"], "path": ["v1", "ledger", "transaction", "{{instruction_ref_id}}"]}}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["pm.collectionVariables.set(\"profile_ref_id\", \"5577e73a-04f0-48bb-94ab-d9dcc40f3546\");", "", "pm.collectionVariables.set(\"account_ref_id\", \"3d7a6283-0518-4df3-9a56-50792a9e2423\");", ""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "PROFILE_API_HOST", "value": "https://ledger-qas-api.peoplescloud.io/cfsb-test"}, {"key": "ACCOUNT_API_HOST", "value": "https://ledger-qas-api.peoplescloud.io/cfsb-test"}, {"key": "TRANSACTION_API_HOST", "value": "https://ledger-qas-api.peoplescloud.io/cfsb-test"}, {"key": "profile_ref_id", "value": ""}, {"key": "account_ref_id", "value": ""}, {"key": "instruction_ref_id", "value": ""}]}