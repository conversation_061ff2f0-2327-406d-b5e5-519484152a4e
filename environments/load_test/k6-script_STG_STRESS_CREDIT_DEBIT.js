// Auto-generated by the postman-to-k6 converter

import "./libs/shim/core.js";
import "./libs/shim/urijs.js";
import { group } from "k6";
import { uuidv4 } from 'https://jslib.k6.io/k6-utils/1.4.0/index.js';

export const options = {
  scenarios: {
    //
    // DEBIT
    //
    PROF_A_ACCT_1_credit: {
      executor: 'constant-vus',
      vus: 1,
      duration: '5m',
      gracefulStop: '15s', // do not wait for iterations to finish in the end

      tags: { test_type: 'credit_transfer' }, // extra tags for the metrics generated by this scenario
      exec: 'credit_transfer', // the function this scenario will execute
      env: { 
        PROFILE_REF_ID: '8310c3b3-2cb8-4a82-8234-0a442c84d058',
        ACCOUNT_REF_ID: 'de9d2dcf-bbd6-4e2d-a34c-f77102eb56c2'
      },
    },
    PROF_A_ACCT_2_credit: {
      executor: 'constant-vus',
      vus: 1,
      duration: '5m',
      gracefulStop: '15s', // do not wait for iterations to finish in the end

      tags: { test_type: 'credit_transfer' }, // extra tags for the metrics generated by this scenario
      exec: 'credit_transfer', // the function this scenario will execute
      env: { 
        PROFILE_REF_ID: '8310c3b3-2cb8-4a82-8234-0a442c84d058',
        ACCOUNT_REF_ID: 'e0ce76ee-92e7-4f1a-b11c-a9b0c849fab7'
      },
    },
    PROF_A_ACCT_3_credit: {
      executor: 'constant-vus',
      vus: 1,
      duration: '5m',
      gracefulStop: '15s', // do not wait for iterations to finish in the end

      tags: { test_type: 'credit_transfer' }, // extra tags for the metrics generated by this scenario
      exec: 'credit_transfer', // the function this scenario will execute
      env: { 
        PROFILE_REF_ID: '8310c3b3-2cb8-4a82-8234-0a442c84d058',
        ACCOUNT_REF_ID: 'b7d3ebe0-9b56-45a1-8d29-7f7dacc23421'
      },
    },
    PROF_A_ACCT_4_credit: {
      executor: 'constant-vus',
      vus: 1,
      duration: '5m',
      gracefulStop: '15s', // do not wait for iterations to finish in the end

      tags: { test_type: 'credit_transfer' }, // extra tags for the metrics generated by this scenario
      exec: 'credit_transfer', // the function this scenario will execute
      env: { 
        PROFILE_REF_ID: '8310c3b3-2cb8-4a82-8234-0a442c84d058',
        ACCOUNT_REF_ID: '71a35f7c-fc4b-42f6-9401-ffe66b049c52'
      },
    },
    PROF_A_ACCT_5_credit: {
      executor: 'constant-vus',
      vus: 1,
      duration: '5m',
      gracefulStop: '15s', // do not wait for iterations to finish in the end

      tags: { test_type: 'credit_transfer' }, // extra tags for the metrics generated by this scenario
      exec: 'credit_transfer', // the function this scenario will execute
      env: { 
        PROFILE_REF_ID: '8310c3b3-2cb8-4a82-8234-0a442c84d058',
        ACCOUNT_REF_ID: '4e8f8b60-f058-4a19-b2f9-b521d043666b'
      },
    },
    PROF_A_ACCT_3_debit: {
      executor: 'constant-vus',
      vus: 1,
      duration: '5m',
      gracefulStop: '15s', // do not wait for iterations to finish in the end

      tags: { test_type: 'debit_transfer' }, // extra tags for the metrics generated by this scenario
      exec: 'debit_transfer', // the function this scenario will execute
      env: { 
        PROFILE_REF_ID: '8310c3b3-2cb8-4a82-8234-0a442c84d058',
        ACCOUNT_REF_ID: 'b7d3ebe0-9b56-45a1-8d29-7f7dacc23421'
      },
    },
  },
  discardResponseBodies: true,
  thresholds: {
    // we can set different thresholds for the different scenarios because of the extra metric tags we set! 
    'http_req_duration{test_type:credit_transfer}': ['p(95)<150', 'p(99)<250'],
    'http_req_duration{test_type:debit_transfer}': ['p(95)<250', 'p(99)<500'],
  },
};

const Pre = Symbol.for("pre");
const Request = Symbol.for("request");
postman[Symbol.for("initial")]({
  options,
  collection: {
    //API_HOST: "https://ledger-qas-api.peoplescloud.io/cfsb-test",
    API_HOST: "http://localhost:8080",
  }
});

export function credit_transfer() {
  group("Service Account", function() {
    postman[Pre].push(() => {
      postman[Pre].push(() => {
        var pg_uuid = uuidv4();
        pm.collectionVariables.set("instruction_ref_id", pg_uuid);
      });
    });

    group("Scenario 01 -- CREDIT", function() {
      postman[Request]({
        name: "Initiate Instruction (CREDIT only)",
        id: "49420b7d-034b-45e6-b982-3cc037d319dd",
        method: "POST",
        address: "{{API_HOST}}/v1/ledger/transaction",
        data:
          '{\n    "instruction_ref_id": "{{instruction_ref_id}}",\n    "payment_rail": "ETRANSFER",\n    "transactions": [\n        {\n            "transaction_ref_id": "{{instruction_ref_id}}",\n            "payment_category": "COMPLETE_PAYMENT",\n            "amount": {{$randomInt}}.00,\n            "monetary_unit": "CAD",\n            "acceptance_date_time": "{{$isoTimestamp}}"\n        }\n    ]\n}',
        headers: {
          "x-pg-interaction-id": "{{$guid}}",
          "x-pg-interaction-timestamp": "{{$isoTimestamp}}",
          "x-pg-profile-id": __ENV.PROFILE_REF_ID,
          "x-pg-account-id": __ENV.ACCOUNT_REF_ID,
          "Content-Type": 'application/json'
        },
        post(response) {
          pm.test("HTTP Status Code == 201", function() {
            pm.response.to.have.status(201);
          });
        }
      });

      postman[Request]({
        name: "Commit Transaction",
        id: "d5db4e42-c774-4dfb-a53a-4d1768ee5377",
        method: "PATCH",
        address:
          "{{API_HOST}}/v1/ledger/transaction/{{instruction_ref_id}}",
        headers: {
          "x-pg-interaction-id": "{{$guid}}",
          "x-pg-interaction-timestamp": "{{$isoTimestamp}}",
          "x-pg-profile-id": __ENV.PROFILE_REF_ID,
          "x-pg-account-id": __ENV.ACCOUNT_REF_ID,
          "Content-Type": 'application/json'
        },
        post(response) {
          pm.test("HTTP Status Code == 200", function() {
            pm.response.to.have.status(200);
          });
        }
      });
    });

    postman[Pre].pop();
  });
}


export function debit_transfer() {
  group("Service Account", function() {
    postman[Pre].push(() => {
      var pg_uuid = uuidv4();
      pm.collectionVariables.set("instruction_ref_id", pg_uuid);
    });

    group("Scenario 02 -- DEBIT", function() {
      postman[Request]({
        name: "Initiate Instruction (DEBIT only)",
        id: "d6a4c2ce-a042-44cb-bbf9-ea4f8c71eb90",
        method: "POST",
        address: "{{API_HOST}}/v1/ledger/transaction",
        data:
          '{\n    "instruction_ref_id": "{{instruction_ref_id}}",\n    "payment_rail": "ETRANSFER",\n    "transactions": [\n        {\n            "transaction_ref_id": "{{instruction_ref_id}}",\n            "payment_category": "SEND_PAYMENT",\n            "amount": 1007.5,\n            "monetary_unit": "CAD",\n            "acceptance_date_time": "{{$isoTimestamp}}",\n            "due_date_time": "{{$isoTimestamp}}"\n        }\n    ]\n}',
        headers: {
          "x-pg-interaction-id": "{{$guid}}",
          "x-pg-interaction-timestamp": "{{$isoTimestamp}}",
          "x-pg-profile-id": __ENV.PROFILE_REF_ID,
          "x-pg-account-id": __ENV.ACCOUNT_REF_ID,
          "Content-Type": 'application/json'
        },
        post(response) {
          pm.test("HTTP Status Code == 201", function() {
            pm.response.to.have.status(201);
          });
        }
      });

      postman[Request]({
        name: "Commit Transaction",
        id: "57b330c8-02b8-41a7-888a-aa5d4aaa6394",
        method: "PATCH",
        address:
          "{{API_HOST}}/v1/ledger/transaction/{{instruction_ref_id}}",
        headers: {
          "x-pg-interaction-id": "{{$guid}}",
          "x-pg-interaction-timestamp": "{{$isoTimestamp}}",
          "x-pg-profile-id": __ENV.PROFILE_REF_ID,
          "x-pg-account-id": __ENV.ACCOUNT_REF_ID,
          "Content-Type": 'application/json'
        },
        post(response) {
          pm.test("HTTP Status Code == 200", function() {
            pm.response.to.have.status(200);
          });
        }
      });
    });

    postman[Pre].pop();
  });
}
