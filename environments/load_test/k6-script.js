//
// <PERSON><PERSON>
// 2023-05-02
// 
// Peoples Group
//


import "./libs/shim/core.js";
import "./libs/shim/urijs.js";
import "./libs/shim/expect.js" // to use pm.expect

import { group } from "k6";
import { uuidv4 } from 'https://jslib.k6.io/k6-utils/1.4.0/index.js';

import { htmlReport } from "https://raw.githubusercontent.com/benc-uk/k6-reporter/main/dist/bundle.js";

export const options = {
  scenarios: {
    PROF_A_ACCT_1_credit: {
      executor: 'ramping-arrival-rate',
      startRate: 10,
      timeUnit: '1m',
      preAllocatedVUs: 2,
      maxVUs: 15,
      gracefulStop: '15s', // do not wait for iterations to finish in the end
      tags: { test_type: 'credit_transfer' }, // extra tags for the metrics generated by this scenario
      exec: 'credit_transfer', // the function this scenario will execute
      stages: [
        { target: 5, duration: '150s' },  // First 2.5 minutes
        { target: 20, duration: '90s' },   // next 1.5 minutes
        { target: 200, duration: '90s' },   // next 1.5 minutes
        { target: 10, duration: '270s' },  // next 4.5 minutes
      ],
      env: { 
        PROFILE_REF_ID: '534c68e4-f706-4e21-b33f-49845e6d50b0',
        ACCOUNT_REF_ID: 'd882edfc-7016-42b4-bd33-1d6cbbb8db60'
      },
    },
    PROF_B_ACCT_1_credit: {
      executor: 'ramping-arrival-rate',
      startRate: 0,
      timeUnit: '1m',
      preAllocatedVUs: 2,
      maxVUs: 10,
      gracefulStop: '15s', // do not wait for iterations to finish in the end
      tags: { test_type: 'credit_transfer' }, // extra tags for the metrics generated by this scenario
      exec: 'credit_transfer', // the function this scenario will execute
      stages: [
        { target: 5, duration: '150s' },  // First 2.5 minutes
        { target: 10, duration: '90s' },   // next 1.5 minutes
        { target: 50, duration: '90s' },   // next 1.5 minutes
        { target: 5, duration: '270s' },  // next 4.5 minutes
      ],
      env: { 
        PROFILE_REF_ID: '0e6806af-d127-4196-864b-ac2fb383d77b',
        ACCOUNT_REF_ID: '85eb3436-f439-4bcc-8c29-65de1194d96d'
      },
    },
    PROF_C_ACCT_1_credit: {
      executor: 'ramping-arrival-rate',
      startRate: 5,
      timeUnit: '1m',
      preAllocatedVUs: 2,
      maxVUs: 5,
      gracefulStop: '15s', // do not wait for iterations to finish in the end
      tags: { test_type: 'credit_transfer' }, // extra tags for the metrics generated by this scenario
      exec: 'credit_transfer', // the function this scenario will execute
      stages: [
        { target: 5, duration: '150s' },  // First 2.5 minutes
        { target: 10, duration: '90s' },   // next 1.5 minutes
        { target: 50, duration: '90s' },   // next 1.5 minutes
        { target: 5, duration: '270s' },  // next 4.5 minutes
      ],
      env: { 
        PROFILE_REF_ID: '0367b04c-03e1-4754-bbf3-c2010a56415f',
        ACCOUNT_REF_ID: '********-eddf-449e-b9a1-2a91a2c3d7fb'
      },
    },
    PROF_C_ACCT_2_credit: {
      executor: 'ramping-arrival-rate',
      startRate: 1,
      timeUnit: '1m',
      preAllocatedVUs: 2,
      maxVUs: 15,
      gracefulStop: '15s', // do not wait for iterations to finish in the end
      tags: { test_type: 'credit_transfer' }, // extra tags for the metrics generated by this scenario
      exec: 'credit_transfer', // the function this scenario will execute
      stages: [
        { target: 5, duration: '150s' },  // First 2.5 minutes
        { target: 20, duration: '90s' },   // next 1.5 minutes
        { target: 100, duration: '90s' },   // next 1.5 minutes
        { target: 20, duration: '270s' },  // next 4.5 minutes
      ],
      env: { 
        PROFILE_REF_ID: '0367b04c-03e1-4754-bbf3-c2010a56415f',
        ACCOUNT_REF_ID: '9354c50d-a196-419c-817c-60e9aecb8289'
      },
    },
    PROF_D_ACCT_1_credit: {
      executor: 'ramping-arrival-rate',
      startRate: 0,
      timeUnit: '1m',
      preAllocatedVUs: 2,
      maxVUs: 5,
      gracefulStop: '15s', // do not wait for iterations to finish in the end
      tags: { test_type: 'credit_transfer' }, // extra tags for the metrics generated by this scenario
      exec: 'credit_transfer', // the function this scenario will execute
      stages: [
        { target: 2, duration: '150s' },  // First 2.5 minutes
        { target: 10, duration: '90s' },   // next 1.5 minutes
        { target: 0, duration: '90s' },   // next 1.5 minutes
        { target: 10, duration: '270s' },  // next 4.5 minutes
      ],
      env: { 
        PROFILE_REF_ID: 'f588ce0e-21bc-473d-8e32-cd378a3156fe',
        ACCOUNT_REF_ID: 'ccedc394-f310-42d5-a2ab-ae7cfbe9f67d'
      },
    },
    PROF_E_ACCT_1_credit: {
      executor: 'ramping-arrival-rate',
      startRate: 0,
      timeUnit: '1m',
      preAllocatedVUs: 2,
      maxVUs: 5,
      gracefulStop: '15s', // do not wait for iterations to finish in the end
      tags: { test_type: 'credit_transfer' }, // extra tags for the metrics generated by this scenario
      exec: 'credit_transfer', // the function this scenario will execute
      stages: [
        { target: 5, duration: '150s' },  // First 2.5 minutes
        { target: 10, duration: '90s' },   // next 1.5 minutes
        { target: 25, duration: '90s' },   // next 1.5 minutes
        { target: 5, duration: '270s' },  // next 4.5 minutes
      ],
      env: { 
        PROFILE_REF_ID: 'f2bbd5f4-f5d8-405b-a2f4-efb39ad21473',
        ACCOUNT_REF_ID: 'c286c0b9-58fb-4ecd-ad1f-58f98f9ad308'
      },
    },
    PROF_F_ACCT_1_credit: {
      executor: 'ramping-arrival-rate',
      startRate: 0,
      timeUnit: '1m',
      preAllocatedVUs: 2,
      maxVUs: 5,
      gracefulStop: '15s', // do not wait for iterations to finish in the end
      tags: { test_type: 'credit_transfer' }, // extra tags for the metrics generated by this scenario
      exec: 'credit_transfer', // the function this scenario will execute
      stages: [
        { target: 5, duration: '150s' },  // First 2.5 minutes
        { target: 10, duration: '90s' },   // next 1.5 minutes
        { target: 25, duration: '90s' },   // next 1.5 minutes
        { target: 5, duration: '270s' },  // next 4.5 minutes
      ],
      env: { 
        PROFILE_REF_ID: 'dece2da4-0f2a-4d08-beaa-bd91abc90648',
        ACCOUNT_REF_ID: '30d6b083-6a68-4016-bbc3-bdd4d98a2d75'
      },
    },
    PROF_G_ACCT_1_credit: {
      executor: 'ramping-arrival-rate',
      startRate: 0,
      timeUnit: '1m',
      preAllocatedVUs: 2,
      maxVUs: 10,
      gracefulStop: '15s', // do not wait for iterations to finish in the end
      tags: { test_type: 'credit_transfer' }, // extra tags for the metrics generated by this scenario
      exec: 'credit_transfer', // the function this scenario will execute
      stages: [
        { target: 5, duration: '150s' },  // First 2.5 minutes
        { target: 10, duration: '90s' },   // next 1.5 minutes
        { target: 50, duration: '90s' },   // next 1.5 minutes
        { target: 50, duration: '270s' },  // next 4.5 minutes
      ],
      env: { 
        PROFILE_REF_ID: '5577e73a-04f0-48bb-94ab-d9dcc40f3546',
        ACCOUNT_REF_ID: '3d7a6283-0518-4df3-9a56-50792a9e2423'
      },
    },
    
    //
    // DEBIT
    //
    PROF_A_ACCT_1_debit: {
      executor: 'ramping-arrival-rate',
      startRate: 1,
      timeUnit: '1m',
      preAllocatedVUs: 2,
      maxVUs: 15,
      gracefulStop: '15s', // do not wait for iterations to finish in the end
      tags: { test_type: 'debit_transfer' }, // extra tags for the metrics generated by this scenario
      exec: 'debit_transfer', // the function this scenario will execute
      stages: [
        { target: 5, duration: '150s' },  // First 2.5 minutes
        { target: 20, duration: '90s' },   // next 1.5 minutes
        { target: 60, duration: '90s' },   // next 1.5 minutes
        { target: 120, duration: '270s' },  // next 4.5 minutes
      ],
      env: { 
        PROFILE_REF_ID: '534c68e4-f706-4e21-b33f-49845e6d50b0',
        ACCOUNT_REF_ID: 'd882edfc-7016-42b4-bd33-1d6cbbb8db60'
      },
    },
    PROF_B_ACCT_1_debit: {
      executor: 'ramping-arrival-rate',
      startRate: 2,
      timeUnit: '1m',
      preAllocatedVUs: 2,
      maxVUs: 15,
      gracefulStop: '15s', // do not wait for iterations to finish in the end
      tags: { test_type: 'debit_transfer' }, // extra tags for the metrics generated by this scenario
      exec: 'debit_transfer', // the function this scenario will execute
      stages: [
        { target: 5, duration: '150s' },  // First 2.5 minutes
        { target: 20, duration: '90s' },   // next 1.5 minutes
        { target: 60, duration: '90s' },   // next 1.5 minutes
        { target: 120, duration: '270s' },  // next 4.5 minutes
      ],
      env: { 
        PROFILE_REF_ID: '0e6806af-d127-4196-864b-ac2fb383d77b',
        ACCOUNT_REF_ID: '85eb3436-f439-4bcc-8c29-65de1194d96d'
      },
    },
    PROF_C_ACCT_1_debit: {
      executor: 'ramping-arrival-rate',
      startRate: 1,
      timeUnit: '1m',
      preAllocatedVUs: 2,
      maxVUs: 15,
      gracefulStop: '15s', // do not wait for iterations to finish in the end
      tags: { test_type: 'debit_transfer' }, // extra tags for the metrics generated by this scenario
      exec: 'debit_transfer', // the function this scenario will execute
      stages: [
        { target: 5, duration: '150s' },  // First 2.5 minutes
        { target: 20, duration: '90s' },   // next 1.5 minutes
        { target: 60, duration: '90s' },   // next 1.5 minutes
        { target: 120, duration: '270s' },  // next 4.5 minutes
      ],
      env: { 
        PROFILE_REF_ID: '0367b04c-03e1-4754-bbf3-c2010a56415f',
        ACCOUNT_REF_ID: '********-eddf-449e-b9a1-2a91a2c3d7fb'
      },
    },
  },
  discardResponseBodies: false,
  thresholds: {
    // we can set different thresholds for the different scenarios because of the extra metric tags we set! 
    'http_req_duration{test_type:credit_transfer}': ['p(95)<150', 'p(99)<250'],
    'http_req_duration{test_type:debit_transfer}': ['p(95)<250', 'p(99)<500'],
  },
};

const Pre = Symbol.for("pre");
const Request = Symbol.for("request");
postman[Symbol.for("initial")]({
  options,
  collection: {
    API_HOST: "https://ledger-qas-api.peoplescloud.io/cfsb-test",
  }
});

// TEMPLATE CREDIT
export function credit_transfer() {
  group("Service Account", function() {
    postman[Pre].push(() => {
      postman[Pre].push(() => {
        // empty
      });
    });

    group("Scenario 01 -- CREDIT", function() {
      var pg_uuid = uuidv4();

      postman[Request]({
        name: "Initiate Instruction (CREDIT only)",
        id: "49420b7d-034b-45e6-b982-3cc037d319dd",
        method: "POST",
        address: "{{API_HOST}}/v1/ledger/transaction",
        data:
          '{\n    "instruction_ref_id": "' + pg_uuid + '",\n    "payment_rail": "ETRANSFER",\n    "transactions": [\n        {\n            "transaction_ref_id": "' + pg_uuid + '",\n            "payment_category": "COMPLETE_PAYMENT",\n            "amount": {{$randomInt}}.00,\n            "monetary_unit": "CAD",\n            "acceptance_date_time": "{{$isoTimestamp}}"\n        }\n    ]\n}',
        headers: {
          "x-pg-interaction-id": "{{$guid}}",
          "x-pg-interaction-timestamp": "{{$isoTimestamp}}",
          "x-pg-profile-id": __ENV.PROFILE_REF_ID,
          "x-pg-account-id": __ENV.ACCOUNT_REF_ID,
          "Content-Type": 'application/json'
        },
        pre() {
          // empty
        },
        post(response) {
          pm.test("HTTP Status Code == 201", function() {
            pm.response.to.have.status(201);
          });

          var json_body = pm.response.json();
          pm.test("Transaction initiated successfully", function() {
            pm.expect(json_body.instruction_ref_id).not.equal(null);
            pm.expect(json_body.status).equal('PENDING');
          });
        }
      });

      postman[Request]({
        name: "Commit Transaction",
        id: "d5db4e42-c774-4dfb-a53a-4d1768ee5377",
        method: "PATCH",
        address:
          "{{API_HOST}}/v1/ledger/transaction/" + pg_uuid,
        headers: {
          "x-pg-interaction-id": "{{$guid}}",
          "x-pg-interaction-timestamp": "{{$isoTimestamp}}",
          "x-pg-profile-id": __ENV.PROFILE_REF_ID,
          "x-pg-account-id": __ENV.ACCOUNT_REF_ID,
          "Content-Type": 'application/json'
        },
        pre() {
          // empty
        },
        post(response) {
          console.log
          pm.test("HTTP Status Code == 200", function() {
            pm.response.to.have.status(200);
          });

          var json_body = pm.response.json();
          pm.test("Transaction completed successfully", function() {
            pm.expect(json_body.instruction_ref_id).not.equal(null);
            pm.expect(json_body.status).equal('POSTED');
          });
        }
      });
    });

    postman[Pre].pop();
  });
}

// TEMPLATE DEBIT
export function debit_transfer() {
  group("Service Account", function() {
    postman[Pre].push(() => {
      var pg_uuid = uuidv4();
      pm.collectionVariables.set("instruction_ref_id", pg_uuid);
    });

    group("Scenario 02 -- DEBIT", function() {
      postman[Request]({
        name: "Initiate Instruction (DEBIT only)",
        id: "d6a4c2ce-a042-44cb-bbf9-ea4f8c71eb90",
        method: "POST",
        address: "{{API_HOST}}/v1/ledger/transaction",
        data:
          '{\n    "instruction_ref_id": "{{instruction_ref_id}}",\n    "payment_rail": "ETRANSFER",\n    "transactions": [\n        {\n            "transaction_ref_id": "{{instruction_ref_id}}",\n            "payment_category": "SEND_PAYMENT",\n            "amount": 1007.5,\n            "monetary_unit": "CAD",\n            "acceptance_date_time": "{{$isoTimestamp}}",\n            "due_date_time": "{{$isoTimestamp}}"\n        }\n    ]\n}',
        headers: {
          "x-pg-interaction-id": "{{$guid}}",
          "x-pg-interaction-timestamp": "{{$isoTimestamp}}",
          "x-pg-profile-id": __ENV.PROFILE_REF_ID,
          "x-pg-account-id": __ENV.ACCOUNT_REF_ID
        },
        post(response) {
          pm.test("HTTP Status Code == 201", function() {
            pm.response.to.have.status(201);
          });
        }
      });

      postman[Request]({
        name: "Commit Transaction",
        id: "57b330c8-02b8-41a7-888a-aa5d4aaa6394",
        method: "PATCH",
        address:
          "{{API_HOST}}/v1/ledger/transaction/{{instruction_ref_id}}",
        headers: {
          "x-pg-interaction-id": "{{$guid}}",
          "x-pg-interaction-timestamp": "{{$isoTimestamp}}",
          "x-pg-profile-id": __ENV.PROFILE_REF_ID,
          "x-pg-account-id": __ENV.ACCOUNT_REF_ID
        },
        post(response) {
          pm.test("HTTP Status Code == 200", function() {
            pm.response.to.have.status(200);
          });
        }
      });
    });

    postman[Pre].pop();
  });
}

// GENERATE HTML REPORT
// export function handleSummary(data) {
//   return {
//     "summary.html": htmlReport(data),
//   };
// }
