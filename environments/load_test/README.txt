# See https://www.baeldung.com/postman-load-testing for high level details


# Create POSTMAN collection (see attached)
> Environment variables for profile & account are set at the Service Account X  level.
> Export the postman collection

# Install
## Install load test util
brew install k6

## install PostMan collection conversion util
npm install -D @apideck/postman-to-k6

#### update the k6-script.js file and add 
import { uuidv4 } from 'https://jslib.k6.io/k6-utils/1.4.0/index.js';

and then 

REPLACE
          var uuid = require("uuid");
          var pg_uuid = uuid.v4();


WITH
          var pg_uuid = uuidv4();


# Setup
## convert postman collection to k6 script
npx @apideck/postman-to-k6 'General Ledger v1 - Load Test ********.postman_collection.json' -o k6-script.js

# Execute
## Next, let's do a live run for three seconds with two virtual users:
k6 run --duration 3s --vus 2 k6-script.js



## Now, update k6-script.js as needed to tweak the options, scenarios, etc.