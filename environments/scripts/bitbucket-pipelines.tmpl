#!yaml
image: atlassian/default-image:4
clone:
  depth: 1
options:
  size: 4x
definitions:
  scripts:
    setup-global-env-vars: &setup-global-env-vars |
      export AWS_DEFAULT_REGION=ca-central-1
      export AWS_OIDC_ROLE_ARN=arn:aws:iam::891377081616:role/tol_oidc_iam_role
    version-image: &version-image
      image:
        name: 891377081616.dkr.ecr.ca-central-1.amazonaws.com/staging/cicd-pipelines/version-runner:0.2.3
        aws:
          oidc-role: arn:aws:iam::891377081616:role/tol_oidc_iam_role
    build-jar: &build-jar |
      mvn -T 1C -am -pl ${APPLICATION_PATH} -Dmaven.test.skip=true -B clean package
    mvn-test: &mvn-test |
      mvn -T 1C -fae -B test
    mvn-install: &mvn-install |
      mvn -T 1C dependency:go-offline
    npm-install: &npm-install |
      npm install --force
    npm-test: &npm-test |
      npm test -- --watchAll=false
    build-docker: &build-docker |
      export TAG=$(cat tag.txt)
      export REPO_NAME="dev/tol/${APPLICATION_PATH}"
      docker build --label app.version="$TAG" --label app.build_date="$(date +%Y%m%d)" -t $REPO_NAME:$TAG -f ${APPLICATION_PATH}${EXTRA_PATH:-}/Dockerfile ${APPLICATION_PATH}/.
    veracode-install: &veracode-install |
      export VERACODE_VERSION="**********"
      curl -O https://repo1.maven.org/maven2/com/veracode/vosp/api/wrappers/vosp-api-wrappers-java/$VERACODE_VERSION/vosp-api-wrappers-java-$VERACODE_VERSION.jar
    zip-mvn-application: &zip-mvn-application |
      yum install -y zip
      zip ${APPLICATION_PATH}.zip ${APPLICATION_PATH}/target/${APPLICATION_PATH}-1.0-SNAPSHOT.jar
      export ZIPPED_ARTIFACT="${APPLICATION_PATH}.zip"
    zip-npm-application: &zip-npm-application |
      yum install -y zip
      zip -r ${APPLICATION_PATH}.zip ${APPLICATION_PATH} -x "./${APPLICATION_PATH}/node_modules/*"
      export ZIPPED_ARTIFACT="${APPLICATION_PATH}.zip"
    veracode-upload-scan: &veracode-upload-scan |
      java -jar vosp-api-wrappers-java-$VERACODE_VERSION.jar \
        -action UploadAndScan \
        -vid "${VERACODE_API_ID}" \
        -vkey "${VERACODE_API_SECRET}" \
        -appname tol-${APPLICATION_PATH} \
        -createprofile true \
        -version $(cat tag.txt) \
        -filepath "$ZIPPED_ARTIFACT" \
        -scantype "STATIC" \
        -autoscan true \
        -deleteincompletescan 1 \
        -scanpollinginterval 120 \
        -teams "Product Development Team"
    veracode-sandbox-scan: &veracode-sandbox-scan |
      java -jar vosp-api-wrappers-java-**********.jar \
        -action UploadAndScan \
        -vid "${VERACODE_API_ID}" \
        -vkey "${VERACODE_API_SECRET}" \
        -appname tol-${APPLICATION_PATH} \
        -createprofile true \
        -version $(cat tag.txt) \
        -filepath "$ZIPPED_ARTIFACT" \
        -sandboxname "develop" \
        -createsandbox true \
        -scantype "STATIC" \
        -lifecyclestage "InDevelopmentPreAlpha" \
        -autoscan true \
        -deleteincompletescan 1 \
        -scanpollinginterval 120 \
        -teams "Product Development Team"
    {{- range (datasource "input").mvn_services }}
    ### Begin {{ . }} setup definitions ###
    setup-{{ . }}: &setup-{{ . }} |
      export APPLICATION_PATH={{ . }}
    ### End {{ . }} setup definitions ###
    {{- end }}
    {{- range (datasource "input").npm_services }}
    ### Begin {{ . }} setup definitions ###
    setup-{{ . }}: &setup-{{ . }} |
      export APPLICATION_PATH={{ . }}
    ### End {{ . }} setup definitions ###
    {{- end }}
  steps:
    {{- range (datasource "input").mvn_services }}
    ### Begin {{ . }} step definitions ###
    - step: &veracode-upload-scan-{{ . }}
        name: Veracode Upload Scan {{ . }}
        image: maven:3.9-amazoncorretto-17-al2023
        caches:
          - maven
        script:
          - *setup-{{ . }}
          - *build-jar
          - *zip-mvn-application
          - *veracode-install
          - *veracode-upload-scan
    - step: &veracode-sandbox-scan-{{ . }}
        name: Veracode Sandbox Scan {{ . }}
        image: maven:3.9-amazoncorretto-17-al2023
        caches:
          - maven
        script:
          - *setup-{{ . }}
          - *build-jar
          - *zip-mvn-application
          - *veracode-install
          - *veracode-sandbox-scan
    - step: &docker-build-{{ . }}
        name: Docker build {{ . }}
        image: maven:3.9-amazoncorretto-17-al2023
        oidc: true
        services:
          - docker
        caches:
          - maven
        script:
          - *setup-{{ . }}
          - *setup-global-env-vars
          - *build-jar
          - *build-docker
          - pipe: atlassian/aws-ecr-push-image:2.4.2
            variables:
              AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION
              AWS_OIDC_ROLE_ARN: $AWS_OIDC_ROLE_ARN
              IMAGE_NAME: $REPO_NAME
              TAGS: $TAG
    ### End {{ . }} step definitions ###
    {{- end }}
    {{- range (datasource "input").npm_services }}
    ### Begin {{ . }} npm step definitions ###
    - step: &npm-test-{{ . }}
        name: Test {{ . }} npm project
        image: node:18-slim
        caches:
          - node
        script:
          - *setup-{{ . }}
          - cd $APPLICATION_PATH
          - *npm-install
          - *npm-test
    - step: &veracode-upload-scan-{{ . }}
        name: Veracode Upload Scan {{ . }}
        image: maven:3.9-amazoncorretto-17-al2023
        script:
          - *setup-{{ . }}
          - *zip-npm-application
          - *veracode-install
          - *veracode-upload-scan
    - step: &veracode-sandbox-scan-{{ . }}
        name: Veracode Sandbox Scan {{ . }}
        image: maven:3.9-amazoncorretto-17-al2023
        script:
          - *setup-{{ . }}
          - *zip-npm-application
          - *veracode-install
          - *veracode-sandbox-scan
    ### End {{ . }} npm step definitions ###
    {{- end }}
    - step: &test-mvn
        name: Test all Java projects
        image: maven:3.9-amazoncorretto-17-al2023
        caches:
          - maven
        script:
          - *mvn-test
    - step: &generate-tag
        name: "Generate tag"
        oidc: true
        script:
          - |
            echo "$BITBUCKET_BUILD_NUMBER-alpha" > tag.txt
            echo "Created tag: $BITBUCKET_BUILD_NUMBER"
        artifacts:
          - tag.txt
    - step: &tag-repo
        name: "Tag Repo"
        <<: *version-image
        oidc: true
        script:
          - |
            versionTag=$(cat tag.txt)
            git tag "$versionTag"
            git push origin tag "$versionTag"
    - step: &mvn-install-deps
        name: "Install dependencies"
        image: maven:3.9-amazoncorretto-17-al2023
        caches:
          - maven
        script:
          - *mvn-install

pipelines:
  pull-requests:
    "**":
      - parallel:
          fail-fast: true
          steps:
            - step:
                <<: *generate-tag
            - step:
                <<: *mvn-install-deps
      - parallel:
          steps:
            # TODO - uncomment when unit tests are separated from integration
            # - step:
            #    <<: *test-mvn
            {{- range (datasource "input").mvn_services }}
            ### Begin {{ . }} step generation ###
            - step:
                <<: *docker-build-{{ . }}
            ### End {{ . }} step generation ###
            {{- end }}
            {{- range (datasource "input").npm_services }}
            ### Begin {{ . }} step generation ###
            - step:
                <<: *npm-test-{{ . }}
            ### End {{ . }} step generation ###
            {{- end }}
  branches:
    develop:
      - parallel:
          fail-fast: true
          steps:
            - step:
                <<: *generate-tag
            - step:
                <<: *mvn-install-deps
      - parallel:
          steps:
            # TODO - uncomment when unit tests are separated from integration
            # - step:
            #    <<: *test-mvn
            {{- range (datasource "input").mvn_services }}
            ### Begin {{ . }} step generation ###
            - step:
                <<: *docker-build-{{ . }}
            - step:
                <<: *veracode-sandbox-scan-{{ . }}
            ### End {{ . }} step generation ###
            {{- end }}
            {{- range (datasource "input").npm_services }}
            ### Begin {{ . }} step generation ###
            - step:
                <<: *npm-test-{{ . }}
            - step:
                <<: *veracode-sandbox-scan-{{ . }}
            ### End {{ . }} step generation ###
            {{- end }}
    master:
      - parallel:
          fail-fast: true
          steps:
            - step:
                <<: *generate-tag
            - step:
                <<: *mvn-install-deps
      - parallel:
          steps:
            # TODO - uncomment when unit tests are separated from integration
            # - step:
            #    <<: *test-mvn
            {{- range (datasource "input").mvn_services }}
            ### Begin {{ . }} step generation ###
            - step:
                <<: *docker-build-{{ . }}
            - step:
                <<: *veracode-upload-scan-{{ . }}
            ### End {{ . }} step generation ###
            {{- end }}
            {{- range (datasource "input").npm_services }}
            ### Begin {{ . }} step generation ###
            - step:
                <<: *npm-test-{{ . }}
            - step:
                <<: *veracode-upload-scan-{{ . }}
            ### End {{ . }} step generation ###
            {{- end }}
