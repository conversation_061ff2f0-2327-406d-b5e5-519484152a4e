package com.peoplestrust.account.external.api.v1.controller;

import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.peoplestrust.account.external.api.v1.AccountExternalApplication;
import java.io.IOException;

import jakarta.servlet.ServletContext;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

@SpringBootTest
@ContextConfiguration(classes = {AccountExternalApplication.class})
@WebAppConfiguration(value = "")
@ActiveProfiles("test")
public abstract class DefaultControllerTest {

  @Autowired WebApplicationContext applicationContext;
  @Autowired ObjectMapper mapper;
  MockMvc mockMvc;

  public void setup() {
    this.mockMvc = MockMvcBuilders.webAppContextSetup(applicationContext).build();
  }

  @Test
  public void shouldLoadWebContext() {
    ServletContext servletContext = applicationContext.getServletContext();
    assertNotNull(servletContext);
  }

  @AfterEach
  public void cleanAfterEach() {
    doCleanUpAfterTest();
  }

  public abstract void doCleanUpAfterTest();

}
