<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-parent</artifactId>
    <version>3.1.12</version>
    <!-- if updating version, also update build.plugin versions in ledger-parent -->
    <relativePath/>
  </parent>
  <groupId>com.peoplestrust</groupId>
  <artifactId>ledger-parent-api</artifactId>
  <version>1.0-SNAPSHOT</version>
  <name>Parent::API</name>
  <packaging>pom</packaging>
  <properties>
    <!-- Redefined some of these as this module does not inherit from ledger-parent-->
    <!-- Generic -->
    <java.version>17</java.version>
    <maven.compiler.source>${java.version}</maven.compiler.source>
    <maven.compiler.target>${java.version}</maven.compiler.target>

    <!--Spring Boot/Framework-->
    <spring.boot.version>3.1.12</spring.boot.version>
    <spring.framework.version>6.1.14</spring.framework.version>

    <!-- Logging -->
    <slf4jj.version>2.0.13</slf4jj.version>
    <logback.version>1.5.13</logback.version>


    <!-- Template annotations -->
    <lombok.version>1.18.22</lombok.version>

    <!-- Data type conversions -->
    <fasterxml.jackson.version>2.17.1</fasterxml.jackson.version>

    <!-- Validations -->
    <hibernate.validator.version>8.0.1.Final</hibernate.validator.version>

    <!-- Mapping -->
    <mapstruct.version>1.4.1.Final</mapstruct.version>

    <!-- Disruptor - enhanced asynchronous logging -->
    <disruptor.version>3.4.2</disruptor.version>

    <!-- Testing -->
    <junit.version>5.9.3</junit.version>

    <kafka-clients.version>3.5.2</kafka-clients.version>

    <spring-kafka.version>3.2.2</spring-kafka.version>
    <hikaricp.version>5.0.1</hikaricp.version>

    <!-- Data type conversions -->
    <maven-failsafe-plugin>2.22.2</maven-failsafe-plugin>
    <maven-compiler-plugin>3.8.1</maven-compiler-plugin>

  </properties>
  <dependencies>

    <dependency>
      <groupId>ch.qos.logback</groupId>
      <artifactId>logback-core</artifactId>
      <version>${logback.version}</version>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <version>${slf4jj.version}</version>
    </dependency>
    <dependency>
      <groupId>com.zaxxer</groupId>
      <artifactId>HikariCP</artifactId>
      <version>${hikaricp.version}</version>
    </dependency>
    <dependency>
      <groupId>org.apache.kafka</groupId>
      <artifactId>kafka-clients</artifactId>
      <version>${kafka-clients.version}</version>
    </dependency>
    <dependency>
      <groupId>org.springframework.kafka</groupId>
      <artifactId>spring-kafka</artifactId>
      <version>${spring-kafka.version}</version>
    </dependency>

    <!-- Starter for building web, including RESTful, applications using Spring
      MVC.Uses Tomcat as the default embedded container -->
    <dependency>
      <groupId>org.apache.tomcat.embed</groupId>
      <artifactId>tomcat-embed-core</artifactId>
      <version>10.1.34</version>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
      <exclusions>
        <exclusion>
          <groupId>org.apache.logging.log4j</groupId>
          <artifactId>log4j-to-slf4j</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.logging.log4j</groupId>
          <artifactId>log4j-api</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <!-- All Spring::Boot::Actuator related dependencies-->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-actuator</artifactId>
      <version>${spring.boot.version}</version>
      <exclusions>
        <exclusion>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-starter-logging</artifactId>
        </exclusion>
      </exclusions>
    </dependency>


    <!-- Automatic Resource Management, automatic generation of getters, setters, equals,
    hashCode and toString, and more  -->
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <version>${lombok.version}</version>
      <scope>provided</scope>
    </dependency>


    <!-- Hibernates Jakarta Bean Validation reference implementation. -->
    <dependency>
      <groupId>org.hibernate.validator</groupId>
      <artifactId>hibernate-validator</artifactId>
      <version>${hibernate.validator.version}</version>
    </dependency>
    <dependency>
      <groupId>org.hibernate.validator</groupId>
      <artifactId>hibernate-validator-annotation-processor</artifactId>
      <version>${hibernate.validator.version}</version>
    </dependency>

    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-databind</artifactId>
    </dependency>

    <!-- Starter for testing Spring Boot applications with libraries including JUnit,
Hamcrest and Mockito -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <groupId>org.junit.vintage</groupId>
          <artifactId>junit-vintage-engine</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-starter-logging</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
  </dependencies>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>ch.qos.logback</groupId>
        <artifactId>logback-core</artifactId>
        <version>${logback.version}</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-api</artifactId>
        <version>${slf4jj.version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka-clients</artifactId>
        <version>${kafka-clients.version}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.kafka</groupId>
        <artifactId>spring-kafka</artifactId>
        <version>${spring-kafka.version}</version>
      </dependency>


      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-validation</artifactId>
      </dependency>
      <!-- All Spring::Boot::Actuator related dependencies-->
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-actuator</artifactId>
        <version>${spring.boot.version}</version>
        <exclusions>
          <exclusion>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-logging</artifactId>
          </exclusion>
        </exclusions>
      </dependency>


      <!-- Automatic Resource Management, automatic generation of getters, setters, equals,
      hashCode and toString, and more  -->
      <dependency>
        <groupId>org.projectlombok</groupId>
        <artifactId>lombok</artifactId>
        <version>${lombok.version}</version>
        <scope>provided</scope>
      </dependency>

      <!-- Hibernate's Jakarta Bean Validation reference implementation. -->
      <dependency>
        <groupId>org.hibernate.validator</groupId>
        <artifactId>hibernate-validator</artifactId>
        <version>${hibernate.validator.version}</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate.validator</groupId>
        <artifactId>hibernate-validator-annotation-processor</artifactId>
        <version>${hibernate.validator.version}</version>
      </dependency>

      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-actuator</artifactId>
      </dependency>
      <dependency>
        <groupId>org.yaml</groupId>
        <artifactId>snakeyaml</artifactId>
        <version>2.0</version>
      </dependency>

    </dependencies>
  </dependencyManagement>

  <!-- Build Settings -->
  <build>
    <plugins>
      <!-- Apache Maven Compiler Plugin - The Compiler Plugin is used to compile the sources of
  your project. -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <source>${java.version}</source>
          <target>${java.version}</target>
          <encoding>UTF-8</encoding>
        </configuration>
      </plugin>
      <!-- The Spring Boot Maven Plugin provides Spring Boot support in Apache Maven. It allows you
      to package executable jar or war archives, run Spring Boot applications, generate build
      information and start your Spring Boot application.-->
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <!-- If you are using spring-boot-starter-parent, such execution is already pre-configured
        with a repackage execution ID so that only the plugin definition should be added. -->
        <configuration>
          <layers>
            <!--in spring 2.4 it should be enabled by default, so should be removed after migration  -->
            <enabled>true</enabled>
          </layers>
        </configuration>
      </plugin>
      <!-- Maven Failsafe MOJO in maven-failsafe-plugin. -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-failsafe-plugin</artifactId>
        <version>${maven-failsafe-plugin}</version>
      </plugin>
    </plugins>
  </build>

</project>