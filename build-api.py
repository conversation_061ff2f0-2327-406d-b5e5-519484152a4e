import subprocess
import sys
import json
import yaml
import glob
from pathlib import Path
import docker
import re

allChangeK8sFile = []

class yamlFile:
    yamlFile = ""
    dockerImageLink = ""
    valuesYaml = any
    kind = ""


def scanAll_K8sFile(deploy_env):
    yamlfiles = []

    # use the glob file filter we only pickup batch + listener yaml file to process
    for file in glob.glob("environments/k8s_" + deploy_env + "/k8s-*-deployment.yaml"):
        try:
            with open(file, 'r') as f:
                oneYamlFile = yamlFile()
                oneYamlFile.valuesYaml = yaml.load(f, Loader=yaml.FullLoader)
                if oneYamlFile.valuesYaml['kind'] == "Deployment":
                    oneYamlFile.kind = "Deployment"
                    oneYamlFile.yamlFile = file
                    oneYamlFile.dockerImageLink = oneYamlFile.valuesYaml['spec']['template']['spec']['containers'][0][
                        'image']
                    yamlfiles.append(oneYamlFile)
        except (FileNotFoundError, IOError, PermissionError) as e:
            print(f"Error reading file {file}: {str(e)}")
            raise e
        except yaml.YAMLError as e:
            print(f"Error in YAML file {file}: {str(e)}")
            raise e

    print("total match yaml file count:" + str(len(yamlfiles)))
    return yamlfiles


def matchK8sFile(all_k8sFiles, ecrFile, timeStamp):
    """
    Update the image tag (& optional Datadog version tag) in *all* matching
    Deployment YAMLs and return the number of files touched.
    """
    matchFiles = []

    try:
        for oneYamlFile in all_k8sFiles:
            # ── identify the repository part of the image ───────────────────────
            dockerImageLinks = oneYamlFile.dockerImageLink.split("/")
            if len(dockerImageLinks) <= 1:
                continue

            repo_and_tag = dockerImageLinks[1]          # e.g. ledger-transaction-async-listener:20250422-1230
            repo = repo_and_tag.split(":")[0]

            if repo != ecrFile:                         # not the project we’re processing
                continue

            # ── build new image reference ───────────────────────────────────────
            newImage = f"{oneYamlFile.dockerImageLink.split(':')[0]}:{timeStamp}"

            # ── load, mutate, write back ────────────────────────────────────────
            yaml_path = Path(oneYamlFile.yamlFile)
            yaml_text = yaml_path.read_text()

            # update container image
            yaml_text = yaml_text.replace(
                f"image: {oneYamlFile.dockerImageLink}",
                f"image: {newImage}",
            )

            # update any Datadog “version” tag
            yaml_text = re.sub(
                r'tags\.datadoghq\.com/version:\s*["\'].*?["\']',
                f'tags.datadoghq.com/version: \"{timeStamp}\"',
                yaml_text,
            )

            yaml_path.write_text(yaml_text)

            # book-keeping
            matchFiles.append(str(yaml_path))
            allChangeK8sFile.append(str(yaml_path))

    except Exception as e:
        print(f"Error occurred while updating YAML files: {e}")
        raise

    return len(matchFiles)


def buildProject(project, timeStamp, all_k8sFiles):
    shortImageShaValue = findDockerImageSha256(project["ecr"])
    #     print("Image Sha256 value:"+str(shortImageShaValue))
    project["imageSha"] = shortImageShaValue

    #     print("inside build project for project:"+project["ecr"])
    # scan find the k8s yaml file and update it
    project["yamlFileCount"] = matchK8sFile(all_k8sFiles, project["ecr"], timeStamp)


def findDockerImageSha256(imageName):
    cli = docker.from_env()
    dockerImages = cli.images.list()
    sha256ShortImageId = None
    for dockerImage in dockerImages:
        imageTags = dockerImage.tags
        if len(imageTags) > 1:
            imageTag0 = imageTags[1].split(":")[0]
            if imageTag0 == imageName:
                sha256ShortImageId = dockerImage.id.replace("sha256:", "")[0:12]
                break

    if sha256ShortImageId:
        print(imageName + " sha256:" + sha256ShortImageId)
        return sha256ShortImageId
    else:
        print(f"No SHA256 ID found for {imageName}")
        return None


def findDockerImageTag(imageName):
    cli = docker.from_env()
    dockerImages = cli.images.list()
    imageTag = None
    for dockerImage in dockerImages:
        imageTags = dockerImage.tags
        if len(imageTags) > 1:
            imageTag0 = imageTags[1].split(":")[0]
            if imageTag0 == imageName:
                imageTag = imageTags[1].split(":")[1]
                print(imageName + " Tag:" + imageTag)
                break

    if imageTag:
        return imageTag
    else:
        print(f"No tag found for {imageName}")
        return None


def loadConfigJson(configFile):
    # Opening JSON config file
    f = open(configFile)

    # returns JSON object as a dictionary
    projectJsonData = json.load(f)

    return projectJsonData


def autoK8sApply(apply):
    # section for auto k8s apply
    print("")
    for k8sFile in allChangeK8sFile:
        # change the file path format to be match macos
        k8sApplyCmd = "kubectl apply -f " + k8sFile.replace("\\", "/")
        print("{:<100}".format(k8sApplyCmd))
        if apply:
            subprocess.call(k8sApplyCmd, shell=True)


def is_docker_running():
    try:
        subprocess.check_output(['docker', 'version'])
        return True
    except subprocess.CalledProcessError:
        return False


if __name__ == "__main__":

    if not is_docker_running():
        print("Docker is not running!")
        sys.exit(1)

    # load json config file
    projectJsonData = loadConfigJson(sys.argv[1])

    # scan disk find all match files
    all_k8sFiles = scanAll_K8sFile(sys.argv[2])

    for project in projectJsonData['projects']:
        # find timestamp from util build result
        timeStamp = findDockerImageTag(project["ecr"])
        if not timeStamp:
            print(f"Skipping project {project['ecr']} due to missing timestamp.")
            continue
        buildProject(project, timeStamp, all_k8sFiles)

    print("")
    print("Final Build Result")
    for project in projectJsonData['projects']:
        print("{:<40} {:<15} {:<5} {:<100}".format(project["folder"], project["imageSha"], project["yamlFileCount"],
                                                   project["imageUrl"]))

    # https://stackoverflow.com/questions/31684375/automatically-create-requirements-txt
    if (len(sys.argv) == 4 and sys.argv[3] == "applyk8s"):
        autoK8sApply(True)
    else:
        autoK8sApply(False)
