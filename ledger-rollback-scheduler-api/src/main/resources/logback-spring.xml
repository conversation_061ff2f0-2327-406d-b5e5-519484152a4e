<?xml version="1.0" encoding="UTF-8"?>
<configuration>

	<appender class="ch.qos.logback.core.ConsoleAppender" name="Console">
		<layout class="ch.qos.logback.classic.PatternLayout">
			<Pattern>
				<!-- %-30(%green(%d) %highlight(%-5level) [%t] - [%X{GUID:-00000000-0000-0000-0000-000000000000}] %yellow(%c{1}):) %m%n%throwable -->
				%-30(%(%d) %(%-5level) [%X{sa:-DEFAULT}] [%X{PG_GUID:-00000000-0000-0000-0000-000000000000}]-[%t] %(%logger{0}) -) %m%n%throwable
			</Pattern>
		</layout>
	</appender>

	<logger additivity="false" level="debug" name="com.peoplestrust">
		<appender-ref ref="Console"/>
	</logger>

	<!-- SQL logger -->
	<logger level="${LOG_LEVEL_HIBERNATE}" name="org.hibernate.SQL"/>

	<logger level="${LOG_LEVEL_HIBERNATE}" name="org.hibernate.type.descriptor.sql.BasicBinder"/>

	<!--	API Payload Logger -->
	<logger level="${LOG_LEVEL_API_PAYLOAD_LOGGER}" name="APIPayloadLogger"/>

	<!-- API Performance Logger -->
	<logger level="${LOG_LEVEL_PERF_LOGGER}" name="PerfLogger"/>

	<!-- API Flow Logger -->
	<logger level="${LOG_LEVEL_FLOW_LOGGER}" name="FlowLogger"/>

	<!-- API Payload Logger -->
	<root level="${LOG_LEVEL_API_PAYLOAD_LOGGER}">
		<appender-ref ref="Console"/>
	</root>
</configuration>
