    package com.peoplestrust.transaction.scheduler.service.impl;

import com.peoplestrust.transaction.persistence.entity.InstructionEntity;
import com.peoplestrust.transaction.persistence.repository.read.ReadInstructionRepository;
import com.peoplestrust.transaction.scheduler.service.PartitionedTransactionRollBackJobRunner;
import com.peoplestrust.transaction.scheduler.service.TransactionServiceAdapter;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class PartitionedTransactionRollBackJobRunnerImpl implements PartitionedTransactionRollBackJobRunner {

  private static final ZoneId EST_ZONE = ZoneId.of("America/New_York");
  private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss z");

  @Value("${scheduler.rollback.expiration.days}")
  private long rollbackExpirationDays;

  @Value("${scheduler.rollback.instructions.fetch.limit}")
  private int fetchLimit;

  @Autowired
  TransactionServiceAdapter transactionServiceAdapter;

  @Autowired
  ReadInstructionRepository readInstructionRepository;

  @Override
  public void runRollBackJob() {
    List<InstructionEntity> instructionsForRollBack;
    do {
      instructionsForRollBack = fetchExpiredTransactions();
      if (instructionsForRollBack.isEmpty()) {
        log.info("No instructions found for rollback, terminating the job.");
        return;
      }

      log.info("Found {} instructions eligible for rollback", instructionsForRollBack.size());
      try {
        transactionServiceAdapter.rollBackTransactions(instructionsForRollBack);
      } catch (Exception ex) {
        log.error("Rollback failed, aborting current run", ex);
        return;
      }
    } while (true);
  }

  List<InstructionEntity> fetchExpiredTransactions() {
    // Get current time in EST
    ZonedDateTime nowEST = ZonedDateTime.now(EST_ZONE);

    // Calculate rollback cutoff time in EST
    ZonedDateTime rollbackCutoffEST = nowEST
        .toLocalDate()
        .atStartOfDay(EST_ZONE)
        .minusDays(rollbackExpirationDays);

    // CRITICAL: Convert to UTC for database comparison
    ZonedDateTime rollbackCutoffUTC = rollbackCutoffEST.withZoneSameInstant(ZoneId.of("UTC"));
    LocalDateTime rollbackStartTime = rollbackCutoffUTC.toLocalDateTime();

    // Enhanced logging to show both timezones
    log.info("RollBack check - Current time EST: {}, Rollback cutoff EST: {}, " +
            "Rollback cutoff UTC: {}, Checking for transactions created BEFORE {} UTC ({}+ days old), fetchLimit: {}",
        nowEST.format(FORMATTER),
        rollbackCutoffEST.format(FORMATTER),
        rollbackCutoffUTC.format(FORMATTER),
        rollbackCutoffUTC.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
        rollbackExpirationDays,
        fetchLimit
    );

    return readInstructionRepository.findRollbackInstructions(
        rollbackStartTime,
        PageRequest.of(0, fetchLimit));
  }
}
