package com.peoplestrust.transaction.scheduler.service.impl;

import com.peoples.banking.util.logger.annotation.PerfLogger;
import com.peoplestrust.transaction.scheduler.service.PartitionedTransactionRollBackJobRunner;
import com.peoplestrust.transaction.scheduler.service.TransactionRollBackScheduledJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class TransactionRollBackScheduledJobImpl implements TransactionRollBackScheduledJob {

  @Autowired
  PartitionedTransactionRollBackJobRunner jobRunner;

  @Override
  @PerfLogger
  public void runTask() {
    log.info("Scheduled task for rollback transactions has started.");
    jobRunner.runRollBackJob();
    log.info("Scheduled task for rollback transactions is finished.");
  }

}
