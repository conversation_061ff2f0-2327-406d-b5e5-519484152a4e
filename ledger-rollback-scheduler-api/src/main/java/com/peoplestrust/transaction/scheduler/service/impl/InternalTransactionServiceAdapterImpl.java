package com.peoplestrust.transaction.scheduler.service.impl;

import com.peoples.banking.util.logger.annotation.PerfLogger;
import com.peoplestrust.transaction.persistence.entity.InstructionEntity;
import com.peoplestrust.transaction.scheduler.service.TransactionServiceAdapter;
import com.peoplestrust.util.api.common.config.APICommonUtilConstant;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Service
@Slf4j
public class InternalTransactionServiceAdapterImpl implements TransactionServiceAdapter {

  @Autowired
  RestTemplate restTemplate;

  @Value("${scheduler.internal.rollback.endpoint}")
  private String internalTransactionRollBackEndpointUrl;

  @Override
  public void rollBackTransactions(List<InstructionEntity> instructions) {

    int successCount = 0;
    int failureCount = 0;

    for (InstructionEntity instruction : instructions) {
      String refId = instruction.getInstructionRefId();
      int txnCount = instruction.getTransactions() == null
          ? 0
          : instruction.getTransactions().size();

      log.info("Rollback START  | instruction_ref_id={} | {} txn(s)", refId, txnCount);

      try {
        boolean ok = callInternalApi(instruction);   // <- logs its own details

        if (ok) {
          successCount++;
          log.info("Rollback SUCCESS | instruction_ref_id={}", refId);
        } else {
          failureCount++;
          log.warn("Rollback API returned FALSE | instruction_ref_id={}", refId);
        }

      } catch (Exception ex) {
        failureCount++;
        log.error("Rollback EXCEPTION | instruction_ref_id={} | {}",
            refId, ex.getMessage(), ex);
        // continue to the next instruction
      }
    }

    log.info("Rollback run finished: {} succeeded, {} failed ({} total).",
        successCount, failureCount, instructions.size());
  }

  @PerfLogger
  private boolean callInternalApi(InstructionEntity instructionEntity) {
    String interactionId = UUID.randomUUID().toString();
    log.info("Call internal rollback instruction API with interactionId={} instructionId={} profileId={} accountId={} ",
        interactionId, instructionEntity.getInstructionRefId(),
        instructionEntity.getProfileRefId(), instructionEntity.getAccountRefId().toString());

    // build API call
    HttpHeaders headers = new HttpHeaders();
    headers.setAccept(Arrays.asList(MediaType.APPLICATION_JSON));
    headers.set(APICommonUtilConstant.HEADER_INTERACTION_ID, interactionId);
    headers.set(APICommonUtilConstant.HEADER_PROFILE_ID, instructionEntity.getProfileRefId().toString());
    headers.set(APICommonUtilConstant.HEADER_ACCOUNT_ID, instructionEntity.getAccountRefId().toString());
    HttpEntity<String> entity = new HttpEntity<>(headers);

    // call API
    ResponseEntity<Boolean> response = null;
    try {
      response = restTemplate.exchange(
          internalTransactionRollBackEndpointUrl + "/" + instructionEntity.getInstructionRefId(),
          HttpMethod.DELETE, entity, Boolean.class);
    } catch (Exception e) {
      log.error("Rollback exception invoking API", e);
    }

    // parse result
    Boolean result = null;
    if (response != null && response.getBody() != null) {
      result = response.getBody().booleanValue();
    } else {
      result = false;
    }

    log.info("Rollback end instruction_ref_id = {} Result is {}", instructionEntity.getInstructionRefId(), result);
    return result;
  }
}
