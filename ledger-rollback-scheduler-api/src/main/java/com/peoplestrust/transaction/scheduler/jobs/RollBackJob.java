package com.peoplestrust.transaction.scheduler.jobs;

import com.peoplestrust.transaction.scheduler.service.TransactionRollBackScheduledJob;
import java.time.LocalDateTime;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class RollBackJob {

  /**
   * Service layer
   */
  @Autowired
  TransactionRollBackScheduledJob transactionRollBackScheduledJob;

  @Scheduled(cron = "${scheduler.rollback.transactions}")
  public void doRollback() {
    log.info("=== ROLLBACK JOB STARTED === at {}", LocalDateTime.now());

    try {
      transactionRollBackScheduledJob.runTask();
      log.info("=== ROLLBACK JOB COMPLETED === at {}", LocalDateTime.now());
    } catch (Exception e) {
      log.error("=== ROLLBACK JOB FAILED === at {}", LocalDateTime.now(), e);
    }
  }
}
