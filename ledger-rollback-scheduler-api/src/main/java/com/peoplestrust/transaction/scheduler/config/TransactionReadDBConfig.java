package com.peoplestrust.transaction.scheduler.config;

import com.peoplestrust.transaction.persistence.entity.InstructionEntity;
import com.peoplestrust.transaction.persistence.entity.TransactionEntity;
import com.zaxxer.hikari.HikariDataSource;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.autoconfigure.orm.jpa.JpaProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Configuration class for setting up the read database transaction manager.
 */
@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(entityManagerFactoryRef = "readEntityManagerFactory",
    transactionManagerRef = "readTransactionManager",
    basePackages = {"com.peoplestrust.transaction.persistence.repository.read"})
public class TransactionReadDBConfig {

  private final JpaProperties jpaProperties;

  /**
   * Constructor for TransactionReadDBConfig class.
   *
   * @param jpaProperties The JpaProperties used for configuring the read database.
   */
  public TransactionReadDBConfig(JpaProperties jpaProperties) {
    this.jpaProperties = jpaProperties;
  }

  /**
   * Creates a DataSourceProperties bean for the read data source.
   *
   * @return The DataSourceProperties bean for the read data source.
   */
  @Bean
  @ConfigurationProperties("spring.datasource.transaction-ro")
  public DataSourceProperties readDataSourceProperties() {
    return new DataSourceProperties();
  }

  /**
   * Creates a DataSource bean for the read data source.
   *
   * @return The DataSource bean for the read data source.
   */
  @Bean
  @ConfigurationProperties("spring.datasource.transaction-ro")
  public HikariDataSource readDataSource() {
    return readDataSourceProperties().initializeDataSourceBuilder()
        .type(HikariDataSource.class).build();
  }

  /**
   * Creates a LocalContainerEntityManagerFactoryBean for the read EntityManagerFactory.
   *
   * @param builder The EntityManagerFactoryBuilder used to build the EntityManagerFactory.
   * @return The LocalContainerEntityManagerFactoryBean for the read EntityManagerFactory.
   */
  @Bean(name = "readEntityManagerFactory")
  public LocalContainerEntityManagerFactoryBean entityManagerFactory(EntityManagerFactoryBuilder builder) {
    return builder
        .dataSource(readDataSource())
        .packages(InstructionEntity.class, TransactionEntity.class)
        .properties(jpaProperties.getProperties())
        .persistenceUnit("read")
        .build();
  }

  /**
   * Creates a PlatformTransactionManager for the read database transactions.
   *
   * @param entityManagerFactoryBean The LocalContainerEntityManagerFactoryBean for the read EntityManagerFactory.
   * @return The PlatformTransactionManager for the read database transactions.
   */
  @Bean(name = "readTransactionManager")
  public PlatformTransactionManager transactionManager(
      final @Qualifier("readEntityManagerFactory") LocalContainerEntityManagerFactoryBean entityManagerFactoryBean) {
    return new JpaTransactionManager(entityManagerFactoryBean.getObject());
  }
}
