package com.peoplestrust.transaction.scheduler;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication
@EnableScheduling
@ComponentScan(
    basePackages = {
        "com.peoplestrust.transaction.scheduler",
    })
@EntityScan(
    basePackages = {
        "com.peoplestrust.transaction.persistence.entity"
    })
public class RollBackTransactionSchedulerApplication {
  public static void main(String[] args) {
    SpringApplication.run(RollBackTransactionSchedulerApplication.class, args);
  }
}
