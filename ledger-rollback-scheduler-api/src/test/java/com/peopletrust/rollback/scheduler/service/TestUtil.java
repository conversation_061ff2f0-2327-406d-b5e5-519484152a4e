package com.peopletrust.rollback.scheduler.service;


import com.peoplestrust.transaction.persistence.entity.InstructionEntity;
import com.peoplestrust.transaction.persistence.entity.MonetaryUnit;
import com.peoplestrust.transaction.persistence.entity.PaymentCategoryType;
import com.peoplestrust.transaction.persistence.entity.TransactionEntity;
import com.peoplestrust.transaction.persistence.entity.TransactionFlowType;
import com.peoplestrust.transaction.persistence.entity.TransactionHoldType;
import com.peoplestrust.transaction.persistence.entity.TransactionStatus;
import com.peoplestrust.transaction.scheduler.model.Instruction;
import com.peoplestrust.transaction.scheduler.model.Transaction;
import com.peoplestrust.util.api.common.util.DateUtils;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

public class TestUtil {

  public static List<Transaction> createTransactions(String accountId, String profileId) {
    List<Transaction> list = new ArrayList<>();

    Transaction t1 =
        Transaction.builder()
            .transactionRefId(UUID.randomUUID().toString())
            .transactionFlow(TransactionFlowType.CREDIT)
            .paymentCategory(PaymentCategoryType.DEBIT_PULL)
            .amount(new BigDecimal(100))
            .monetaryUnit("CAD")
            .acceptanceDateTime(DateUtils.offsetDateTime())
            .dueDateTime(DateUtils.offsetDateTime().plusDays(2))
            .status(TransactionStatus.PENDING)
            .build();
    t1.setAccountRefId(accountId);
    t1.setProfileRefId(profileId);
    Transaction t2 =
        Transaction.builder()
            .transactionRefId(UUID.randomUUID().toString())
            .transactionFlow(TransactionFlowType.DEBIT)
            .paymentCategory(PaymentCategoryType.CREDIT_PUSH)
            .amount(new BigDecimal(100))
            .monetaryUnit("CAD")
            .acceptanceDateTime(DateUtils.offsetDateTime())
            .status(TransactionStatus.PENDING)
            .build();
    t2.setAccountRefId(accountId);
    t2.setProfileRefId(profileId);
    Transaction t3 =
        Transaction.builder()
            .transactionRefId(UUID.randomUUID().toString())
            .transactionFlow(TransactionFlowType.CREDIT)
            .paymentCategory(PaymentCategoryType.DEBIT_PULL)
            .amount(new BigDecimal(100))
            .monetaryUnit("CAD")
            .acceptanceDateTime(DateUtils.offsetDateTime())
            .status(TransactionStatus.PENDING)
            .build();
    t3.setAccountRefId(accountId);
    t3.setProfileRefId(profileId);
    list.add(t1);
    list.add(t2);
    list.add(t3);
    return list;
  }

  public static InstructionEntity mapToInstructionEntity(Instruction instruction) {
    InstructionEntity instructionEntity = new InstructionEntity();

    instructionEntity.setInstructionRefId(instruction.getInstructionRefId());
    instructionEntity.setStatus(instruction.getStatus());
    instructionEntity.setAccountRefId(UUID.fromString(instruction.getAccountRefId()));
    instructionEntity.setPaymentRail(instruction.getPaymentRail());
    instructionEntity.setProfileRefId(UUID.fromString(instruction.getProfileRefId()));
    instructionEntity.setCreatedDateTime(instruction.getCreatedDateTime());
    return instructionEntity;
  }

  public static List<TransactionEntity> getTransactionEntities(Instruction instruction) {
    List<TransactionEntity> ts = new ArrayList<>();
    instruction
        .getTransactions()
        .forEach(
            t -> {
              TransactionEntity te =
                  TransactionEntity.builder()
                      .transactionRefId(t.getTransactionRefId())
                      .acceptanceDateTime(t.getAcceptanceDateTime().toLocalDateTime())
                      .transactionFlow(t.getTransactionFlow())
                      .paymentCategory(t.getPaymentCategory())
                      .transactionHold(TransactionHoldType.HOLD)
                      .monetaryUnit(MonetaryUnit.valueOf(t.getMonetaryUnit()))
                      .status(t.getStatus())
                      .effectiveDateTime(LocalDateTime.now())
                      .amount(t.getAmount())
                      .accountRefId(UUID.fromString(t.getAccountRefId()))
                      .profileRefId(UUID.fromString(t.getProfileRefId()))
                      .build();
              ts.add(te);
            });
    return ts;
  }
}
