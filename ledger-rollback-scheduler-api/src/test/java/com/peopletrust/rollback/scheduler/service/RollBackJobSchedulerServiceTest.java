package com.peopletrust.rollback.scheduler.service;

import static com.peopletrust.rollback.scheduler.service.TestUtil.createTransactions;
import static com.peopletrust.rollback.scheduler.service.TestUtil.getTransactionEntities;
import static com.peopletrust.rollback.scheduler.service.TestUtil.mapToInstructionEntity;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.peoplestrust.transaction.persistence.entity.InstructionEntity;
import com.peoplestrust.transaction.persistence.entity.InstructionStatus;
import com.peoplestrust.transaction.persistence.entity.PaymentRailType;
import com.peoplestrust.transaction.persistence.entity.TransactionEntity;
import com.peoplestrust.transaction.persistence.repository.read.ReadInstructionRepository;
import com.peoplestrust.transaction.persistence.repository.write.InstructionRepository;
import com.peoplestrust.transaction.scheduler.RollBackTransactionSchedulerApplication;
import com.peoplestrust.transaction.scheduler.model.Instruction;
import com.peoplestrust.transaction.scheduler.model.Transaction;
import com.peoplestrust.transaction.scheduler.service.PartitionedTransactionRollBackJobRunner;
import com.peoplestrust.transaction.scheduler.service.TransactionServiceAdapter;
import com.peoplestrust.util.api.common.util.DateUtils;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.stubbing.Answer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.web.client.RestTemplate;

@ExtendWith(MockitoExtension.class)
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
@ContextConfiguration(classes = {RollBackTransactionSchedulerApplication.class})
@WebAppConfiguration(value = "")
public class RollBackJobSchedulerServiceTest {

  private static final String profileRefId = UUID.randomUUID().toString();
  private static final String accountRefId = UUID.randomUUID().toString();

  private static final int dataFetchLimit = 25;

  @Autowired
  PartitionedTransactionRollBackJobRunner jobRunner;

  @MockBean
  ReadInstructionRepository readInstructionRepository;

  @MockBean
  InstructionRepository instructionRepository;

  @MockBean
  RestTemplate restTemplate;

  @MockBean
  TransactionServiceAdapter transactionServiceAdapter;

  @BeforeEach
  public void setUp() {
    instructionRepository.findAll().forEach(e -> instructionRepository.delete(e));
  }

  @Test
  public void serviceHappyPathTest() {
    InstructionEntity expiredInstruction = createExpiredInstructionData();
    InstructionEntity nonExpiredInstruction = createNonExpiredInstruction();
    when(restTemplate.exchange(anyString(), any(), any(), eq(Boolean.class))).thenReturn(new ResponseEntity<>(true, HttpStatus.OK));
    when(readInstructionRepository.findRollbackInstructions(any(), any())).thenReturn(Arrays.asList(new InstructionEntity[]{expiredInstruction}))
        .thenReturn(new ArrayList<>());

    doAnswer(new Answer() {
      @Override
      public Object answer(InvocationOnMock invocationOnMock) throws Throwable {
        expiredInstruction.setStatus(InstructionStatus.ROLLBACKED);
        return null;
      }
    }).when(transactionServiceAdapter).rollBackTransactions(any());
    jobRunner.runRollBackJob();

    assertEquals(expiredInstruction.getStatus(), InstructionStatus.ROLLBACKED);
    assertEquals(nonExpiredInstruction.getStatus(), InstructionStatus.PENDING);
  }

  @Test
  public void servicePaginationTest() {
    List<InstructionEntity> instructionList = createPaginationInstructionData();
    when(restTemplate.exchange(anyString(), any(), any(), eq(Boolean.class))).thenReturn(new ResponseEntity<>(true, HttpStatus.OK));
    when(readInstructionRepository.findRollbackInstructions(any(), any())).thenReturn(instructionList.subList(0, dataFetchLimit))
        .thenReturn(instructionList.subList(dataFetchLimit, dataFetchLimit + 10)).thenReturn(new ArrayList<>());
    doAnswer(new Answer() {
      @Override
      public Object answer(InvocationOnMock invocationOnMock) throws Throwable {
        instructionList.forEach(i -> i.setStatus(InstructionStatus.ROLLBACKED));
        return null;
      }
    }).when(transactionServiceAdapter).rollBackTransactions(any());
    jobRunner.runRollBackJob();
    for (InstructionEntity i : instructionList) {
      assertEquals(i.getStatus(), InstructionStatus.ROLLBACKED);
    }
    // verify that it is called 2 times because list has size (limit +10 ) which means two cycles.
    verify(transactionServiceAdapter, times(2)).rollBackTransactions(any());
  }

  @AfterEach
  public void reset() {
    instructionRepository.findAll().forEach(e -> instructionRepository.delete(e));
  }

  private InstructionEntity createExpiredInstructionData() {
    return createInstructionAndTransactionData(10);
  }

  private InstructionEntity createNonExpiredInstruction() {
    return createInstructionAndTransactionData(0);
  }

  private List<InstructionEntity> createPaginationInstructionData() {
    List<InstructionEntity> instructionEntityList = new ArrayList<>();
    for (int i = 0; i < dataFetchLimit + 10; i++) {
      instructionEntityList.add(createInstructionAndTransactionData(30));
    }
    return instructionEntityList;
  }

  private InstructionEntity createInstructionAndTransactionData(int expirationTimeInMinutes) {
    List<Transaction> transactions = createTransactions(accountRefId, profileRefId);
    Instruction instruction =
        Instruction.builder()
            .instructionRefId(UUID.randomUUID().toString())
            .accountRefId(accountRefId)
            .paymentRail(PaymentRailType.EFT)
            .status(InstructionStatus.PENDING)
            .profileRefId(profileRefId)
            .transactions(transactions)
            .createdDateTime(DateUtils.offset().minusMinutes(expirationTimeInMinutes))
            .updatedDateTime(DateUtils.offset())
            .build();
    InstructionEntity instructionEntity = mapToInstructionEntity(instruction);
    List<TransactionEntity> transactionEntities = getTransactionEntities(instruction);
    transactionEntities.forEach(t -> t.setInstruction(instructionEntity));
    instructionEntity.setTransactions(transactionEntities);
    instructionRepository.save(instructionEntity);
    return instructionEntity;
  }

  /**
   * Error‑path test:
   *  – The adapter throws an exception.
   *  – We expect the loop to stop after the first failure.
   */
  @Test
  public void serviceErrorPathTest() {
    // 1. Prepare one expired instruction
    InstructionEntity expired = createInstructionAndTransactionData(60); // 60 min ago

    when(readInstructionRepository.findRollbackInstructions(any(), any()))
        .thenReturn(List.of(expired))   // first call returns data
        .thenReturn(List.of());         // second call returns empty list

    // 2. Make the adapter throw an exception
    doAnswer(inv -> { throw new RuntimeException("boom"); })
        .when(transactionServiceAdapter).rollBackTransactions(any());

    // 3. Execute the job
    jobRunner.runRollBackJob();

    // 4. Adapter should be invoked exactly once
    verify(transactionServiceAdapter, times(1)).rollBackTransactions(any());
  }

  /**
   * Empty‑result path:
   *  – Repository returns an empty list on the first call.
   *  – Adapter must never be invoked.
   */
  @Test
  public void noExpiredInstructionsTest() {
    when(readInstructionRepository.findRollbackInstructions(any(), any()))
        .thenReturn(List.of());   // immediately empty

    jobRunner.runRollBackJob();

    verify(transactionServiceAdapter, times(0)).rollBackTransactions(any());
  }

  @Test
  public void expiredVsNonPendingTest() {
    // 1. Create two entities with different statuses
    InstructionEntity pendingExpired   = createInstructionAndTransactionData(60);
    InstructionEntity postedExpired    = createInstructionAndTransactionData(60);
    postedExpired.setStatus(InstructionStatus.POSTED);

    // 2. Mock repository: first call returns ONLY the PENDING item,
    //    second call returns empty list → loop exits.
    when(readInstructionRepository.findRollbackInstructions(any(), any()))
        .thenReturn(List.of(pendingExpired))  // realistic result set
        .thenReturn(List.of());               // next page empty

    // 3. Adapter should receive exactly one item (the PENDING one)
    doAnswer(inv -> {
      List<InstructionEntity> batch = inv.getArgument(0);
      assertEquals(1, batch.size());
      assertEquals(InstructionStatus.PENDING, batch.get(0).getStatus());
      // Mark as rolled back so it won’t be fetched again
      batch.get(0).setStatus(InstructionStatus.ROLLBACKED);
      return null;
    }).when(transactionServiceAdapter).rollBackTransactions(any());

    // 4. Execute the job
    jobRunner.runRollBackJob();

    // 5. The POSTED instruction should remain untouched
    assertEquals(InstructionStatus.POSTED, postedExpired.getStatus());
  }

}
