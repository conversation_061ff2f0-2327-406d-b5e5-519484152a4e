<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

  <parent>
    <artifactId>ledger-parent-api</artifactId>
    <groupId>com.peoplestrust</groupId>
    <version>1.0-SNAPSHOT</version>
    <relativePath>../ledger-parent-api/pom.xml</relativePath>
  </parent>
  <groupId>com.peoplestrust.transaction.scheduler</groupId>
  <artifactId>ledger-rollback-scheduler-api</artifactId>
  <name>Ledger::Rollback::Scheduler::API</name>

  <dependencies>
    <dependency>
      <groupId>com.peoplestrust</groupId>
      <artifactId>ledger-transaction-persistence</artifactId>
      <version>1.0-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.peoplestrust</groupId>
      <artifactId>ledger-transaction-domain</artifactId>
      <version>1.0-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.peoplestrust</groupId>
      <artifactId>common-api</artifactId>
      <version>1.0-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>
  </dependencies>

</project>