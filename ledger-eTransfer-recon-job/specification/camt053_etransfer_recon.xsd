<?xml version="1.0" encoding="UTF-8"?>
<!--
  Minimal XSD for eTransfer reconciliation, based on ISO 20022 camt.053.001.12.
  Only includes the subset of fields used in the current sample XML.
  All timestamps are in Eastern Time (ET) with correct offset; Statement IDs are UUIDs.
  This XSD uses the official namespace and type names.
-->
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns="urn:iso:std:iso:20022:tech:xsd:camt.053.001.12"
           targetNamespace="urn:iso:std:iso:20022:tech:xsd:camt.053.001.12"
           elementFormDefault="qualified">

  <!-- Root element -->
  <xs:element name="Document">
    <xs:complexType>
      <xs:sequence>
        <xs:element name="BkToCstmrStmt">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="Stmt">
                <xs:complexType>
                  <xs:sequence>
                    <xs:element name="Id" type="xs:string"/>
                    <xs:element name="CreDtTm" type="xs:dateTime"/>
                    <xs:element name="FrToDt">
                      <xs:complexType>
                        <xs:sequence>
                          <xs:element name="FrDtTm" type="xs:dateTime"/>
                          <xs:element name="ToDtTm" type="xs:dateTime"/>
                        </xs:sequence>
                      </xs:complexType>
                    </xs:element>
                    <xs:element name="Acct">
                      <xs:complexType>
                        <xs:sequence>
                          <xs:element name="Id">
                            <xs:complexType>
                              <xs:sequence>
                                <xs:element name="Othr">
                                  <xs:complexType>
                                    <xs:sequence>
                                      <xs:element name="Id" type="xs:string"/>
                                    </xs:sequence>
                                  </xs:complexType>
                                </xs:element>
                              </xs:sequence>
                            </xs:complexType>
                          </xs:element>
                          <xs:element name="Ownr">
                            <xs:complexType>
                              <xs:sequence>
                                <xs:element name="Id">
                                  <xs:complexType>
                                    <xs:sequence>
                                      <xs:element name="OrgId">
                                        <xs:complexType>
                                          <xs:sequence>
                                            <xs:element name="Othr">
                                              <xs:complexType>
                                                <xs:sequence>
                                                  <xs:element name="Id" type="xs:string"/>
                                                </xs:sequence>
                                              </xs:complexType>
                                            </xs:element>
                                          </xs:sequence>
                                        </xs:complexType>
                                      </xs:element>
                                    </xs:sequence>
                                  </xs:complexType>
                                </xs:element>
                              </xs:sequence>
                            </xs:complexType>
                          </xs:element>
                        </xs:sequence>
                      </xs:complexType>
                    </xs:element>
                    <xs:element name="Ntry" maxOccurs="unbounded">
                      <xs:complexType>
                        <xs:sequence>
                          <xs:element name="NtryRef" type="xs:string"/>
                          <xs:element name="Amt">
                            <xs:complexType>
                              <xs:simpleContent>
                                <xs:extension base="xs:decimal">
                                  <xs:attribute name="Ccy" type="xs:string" use="required"/>
                                </xs:extension>
                              </xs:simpleContent>
                            </xs:complexType>
                          </xs:element>
                          <xs:element name="CdtDbtInd" type="xs:string"/>
                          <xs:element name="Sts" type="xs:string"/>
                          <xs:element name="BookgDtTm" type="xs:dateTime"/>
                          <xs:element name="NtryDtls">
                            <xs:complexType>
                              <xs:sequence>
                                <xs:element name="TxDtls">
                                  <xs:complexType>
                                    <xs:sequence>
                                      <xs:element name="Refs">
                                        <xs:complexType>
                                          <xs:sequence>
                                            <xs:element name="EndToEndId" type="xs:string"/>
                                            <xs:element name="InitnTmstmp" type="xs:dateTime"/>
                                            <xs:element name="CompltnTmstmp" type="xs:dateTime"/>
                                          </xs:sequence>
                                        </xs:complexType>
                                      </xs:element>
                                    </xs:sequence>
                                  </xs:complexType>
                                </xs:element>
                              </xs:sequence>
                            </xs:complexType>
                          </xs:element>
                        </xs:sequence>
                      </xs:complexType>
                    </xs:element>
                  </xs:sequence>
                </xs:complexType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
</xs:schema> 