from lxml import etree
from dateutil.parser import isoparse
import logging
from .datadog import trigger_datadog_alert
logger = logging.getLogger("eTransfer_recon.validation.transaction")
def validate_transaction_fields(doc: etree._ElementTree) -> bool:
    try:
        root = doc.getroot()
        ns = {'ns': root.nsmap[None]}

        stmt = root.find('.//ns:BkToCstmrStmt/ns:Stmt', namespaces=ns)
        if stmt is None:
            logger.error("Missing <Stmt> element for transaction validation")
            trigger_datadog_alert("Missing <Stmt> element for transaction validation")
            return False

        entries = stmt.findall('ns:Ntry', namespaces=ns)
        valid = True
        for i, ntry in enumerate(entries, 1):
            amt_el = ntry.find('ns:Amt', namespaces=ns)
            ccy = amt_el.get('Ccy') if amt_el is not None else None
            amt_val = float(amt_el.text) if amt_el is not None and amt_el.text else None

            cdt_dbt_el = ntry.find('ns:CdtDbtInd', namespaces=ns)
            cdt_dbt_val = cdt_dbt_el.text.strip() if cdt_dbt_el is not None and cdt_dbt_el.text else None

            status_el = ntry.find('ns:Sts', namespaces=ns)
            status = status_el.text.strip() if status_el is not None and status_el.text else None

            bookg_dt_el = ntry.find('ns:BookgDtTm', namespaces=ns)
            bookg_dt = bookg_dt_el.text.strip() if bookg_dt_el is not None and bookg_dt_el.text else None

            ntry_ref_el = ntry.find('ns:NtryRef', namespaces=ns)
            ntry_ref = ntry_ref_el.text.strip() if ntry_ref_el is not None and ntry_ref_el.text else None

            tx_el = ntry.find('.//ns:NtryDtls/ns:TxDtls', namespaces=ns)
            tx = tx_el.text.strip() if tx_el is not None and tx_el.text else None

            if tx is None:
                logger.error(f"Transaction {i}: Missing <TxDtls>.")
                trigger_datadog_alert(f"Transaction {i}: Missing <TxDtls>.")
                valid = False
                continue

            refs_el = tx_el.find('ns:Refs', namespaces=ns)

            end_to_end_id_el = refs_el.find('ns:EndToEndId', namespaces=ns)
            end_to_end_id = end_to_end_id_el.text.strip() if end_to_end_id_el is not None and end_to_end_id_el.text else None
            logger.info(f"Transaction {i}: end_to_end_id: {end_to_end_id}")

            initn_tmstmp_el = refs_el.find('ns:InitnTmstmp', namespaces=ns)
            initn_tmstmp = initn_tmstmp_el.text.strip() if initn_tmstmp_el is not None and initn_tmstmp_el.text else None
            logger.info(f"Transaction {i}: initn_tmstmp: {initn_tmstmp}")

            compltn_tmstmp_el = refs_el.find('ns:CompltnTmstmp', namespaces=ns)
            compltn_tmstmp = compltn_tmstmp_el.text.strip() if compltn_tmstmp_el is not None and compltn_tmstmp_el.text else None
            logger.info(f"Transaction {i}: compltn_tmstmp: {compltn_tmstmp}")

            if not ntry_ref:
                logger.error(f"Transaction {i}: Missing EntryRef.")
                trigger_datadog_alert(f"Transaction {i}: Missing EntryRef.")
                valid = False
            if amt_val is None or amt_val <= 0:
                logger.error(f"Transaction {i}: Invalid or missing amount.")
                trigger_datadog_alert(f"Transaction {i}: Invalid or missing amount.")
                valid = False
            if not ccy or ccy != 'CAD':
                logger.error(f"Transaction {i}: Invalid or missing currency (must be CAD).")
                trigger_datadog_alert(f"Transaction {i}: Invalid or missing currency (must be CAD).")
                valid = False
            if cdt_dbt_val not in ('CRDT', 'DBIT'):
                logger.error(f"Transaction {i}: Invalid credit/debit indicator: {cdt_dbt}")
                trigger_datadog_alert(f"Transaction {i}: Invalid credit/debit indicator: {cdt_dbt}")
                valid = False
            if status not in ('BOOK', 'PDNG'):
                logger.error(f"Transaction {i}: Invalid status: {status}")
                trigger_datadog_alert(f"Transaction {i}: Invalid status: {status}")
                valid = False
            if not end_to_end_id:
                logger.error(f"Transaction {i}: Missing EndToEndId.")
                trigger_datadog_alert(f"Transaction {i}: Missing EndToEndId.")
                valid = False
            for ts_name, ts_val in [("Entry Timestamp", bookg_dt), ("Initiated Timestamp", initn_tmstmp), ("Completed Timestamp", compltn_tmstmp)]:
                if not ts_val:
                    logger.error(f"Transaction {i}: Missing {ts_name}.")
                    trigger_datadog_alert(f"Transaction {i}: Missing {ts_name}.")
                    valid = False
                else:
                    try:
                        ts_val = isoparse(ts_val)
                        _ = ts_val.year, ts_val.month, ts_val.day, ts_val.hour, ts_val.minute, ts_val.second
                    except Exception:
                        logger.error(f"Transaction {i}: Invalid {ts_name}: {ts_val}")
                        trigger_datadog_alert(f"Transaction {i}: Invalid {ts_name}: {ts_val}")
                        valid = False
        if valid:
            logger.info("All transaction fields validated successfully.")
        return valid
    except Exception as e:
        logger.error(f"Transaction validation error: {e}")
        trigger_datadog_alert(f"Transaction validation error: {e}")
        return False