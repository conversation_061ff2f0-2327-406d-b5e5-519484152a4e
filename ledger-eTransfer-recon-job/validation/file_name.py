import os
import re
import logging
from urllib.parse import urlparse
from typing import Optional
from .datadog import trigger_datadog_alert

logger = logging.getLogger("eTransfer_recon.validation.file_name")

def validate_file_name(file_path: str, account_id: Optional[str] = None) -> bool:
    if not file_path.startswith("s3://") and not os.path.isfile(file_path):
        logger.error(f"Invalid file_path: '{file_path}'. Expected an S3 path or local file.")
        trigger_datadog_alert(f"Invalid file_path: {file_path}")
        return False
    if account_id is None:
        account_id = os.getenv("GL_ACCOUNT_ID")
    if not account_id:
        logger.error("GL_ACCOUNT_ID environment variable not set.")
        trigger_datadog_alert("GL_ACCOUNT_ID environment variable not set.")
        return False
    if file_path.startswith("s3://"):
        parsed_url = urlparse(file_path)
        file_name = os.path.basename(parsed_url.path)
    else:
        file_name = os.path.basename(file_path)
    pattern = rf"etransfer_gl_recon_{account_id}_[0-9]{{8}}\.xml"
    if not re.fullmatch(pattern, file_name):
        logger.error(f"Invalid file name: '{file_name}' and account: '{account_id}'. Expected format: 'etransfer_gl_recon_$AccountID_YYYYMMDD.xml'")
        trigger_datadog_alert(f"Invalid file name: {file_name}")
        return False
    logger.info(f"file name: '{file_name}' validated successfully.")
    return True
