from lxml import etree
import logging
from .datadog import trigger_datadog_alert
logger = logging.getLogger("eTransfer_recon.validation.header")
def parse_and_validate_headers(doc: etree._ElementTree) -> bool:
    try:
        root = doc.getroot()
        ns = {'ns': root.nsmap[None]}

        stmt = root.find('.//ns:BkToCstmrStmt/ns:Stmt', namespaces=ns)
        if stmt is None:
            logger.error("Missing <Stmt> element")
            return False

        acct_id_el = stmt.find('ns:Acct/ns:Id/ns:Othr/ns:Id', namespaces=ns)
        ownr_id_el = stmt.find('ns:Acct/ns:Ownr/ns:Id/ns:OrgId/ns:Othr/ns:Id', namespaces=ns)
        stmt_id_el = stmt.find('ns:Id', namespaces=ns)

        acct_id = acct_id_el.text.strip() if acct_id_el is not None and acct_id_el.text else None
        ownr_id = ownr_id_el.text.strip() if ownr_id_el is not None and ownr_id_el.text else None
        stmt_id = stmt_id_el.text.strip() if stmt_id_el is not None and stmt_id_el.text else None

        if not acct_id:
            logger.error("Missing or empty Account ID in header.")
            trigger_datadog_alert("Missing or empty Account ID in header.")
            return False
        if not ownr_id:
            logger.error("Missing or empty Profile ID in header.")
            trigger_datadog_alert("Missing or empty Profile ID in header.")
            return False
        if not stmt_id:
            logger.error("Missing or empty Statement ID in header.")
            trigger_datadog_alert("Missing or empty Statement ID in header.")
            return False
        logger.info(f"Header Account ID: {acct_id}, Profile ID: {ownr_id}, Statement ID: {stmt_id}")
        logger.info("Header validation is successful")
        return True
    except Exception as e:
        logger.error(f"Header validation error: {e}")
        trigger_datadog_alert(f"Header validation error: {e}")
        return False
 