  import argparse
  import logging
  import os
  import re
  from datetime import datetime
  from urllib.parse import urlparse

  # Configure Logging
  logging.basicConfig(level=logging.INFO)
  logger = logging.getLogger("eTransfer_recon")

  def validate_file_name(file_path: str) -> bool:
    # check if the file_path is from s3
    if not file_path.startswith("s3://"):
      logger.error(f"Invalid file_path: '{file_path}'. Expected an S3 path.")
      return False

    expected_account_id = os.getenv["GL_ACCOUNT_ID"]

    # Extract filename from s3 path
    parsed_url = urlparse(file_path)
    file_name = os.path.basename(parsed_url.path)

    pattern = rf"etransfer_gl_recon_{expected_account_id}_[0-9]{{8}}\.xml"

    if not re.fullmatch(pattern, file_name):
      logger.error(f"Invalid file name: '{file_name}'. Expected format: 'etransfer_gl_recon_$AccountID_YYYYMMDD.xml'")
      return False
    logger.info(f"file name: '{file_name}' validated successfully.")
    return True

  def etransfer_recon(report_date: str, file_path: str):
    logger.info(f"Running eTransfer recon for report date: {report_date}")
    if not validate_file_name(file_path):
      return False

    # Step1: Validate file name format.
    logger.info("Step1: Validate file name format.")

    # Step2: Validate XML against XSD
    logger.info("Step2: Validate XML against the provided XSD schema.")

    # Step3: Validate headers (Profile ID, Account ID, etc.)
    logger.info("Step3: Validate presence and correctness of header fields.")

    # Step4: Validate transaction fields
    logger.info("Step4: Validate business rules (amount, status, timestamps, etc.).")

    logger.info("eTransfer Recon Completed Successfully.")
    return True

  if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Run eTransfer recon job")
    parser.add_argument("-d", "--date", required=False, help="Report date in YYYY-MM-DD format")
    parser.add_argument("-f", "--file_path", required=True, help="Path to the XML file")
    args = parser.parse_args()

    try:
      if args.date:
        # Validate date format
        report_date = datetime.strptime(args.date, "%Y-%m-%d").date()
      else:
        report_date = datetime.today().date()
      etransfer_recon(str(report_date), args.file_path)
    except ValueError as ve:
      logger.error("Invalid date format. Use YYYY-MM-DD.")
      raise ve