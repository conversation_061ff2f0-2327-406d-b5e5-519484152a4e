from pyiso20022.camt.camt_053_001_12 import (
    Document, BankToCustomerStatementV12, GroupHeader116, AccountStatement13,
    AccountIdentification4Choice, GenericAccountIdentification1,
    BalanceType10Choice, BalanceType13, ActiveOrHistoricCurrencyAndAmount,
    DateAndDateTime2Choice, CashBalance8, ReportEntry14,
    TransactionReferences6,
    DateTimePeriod1, EntryStatus1Choice, BankTransactionCodeStructure4,
    EntryDetails13, EntryTransaction14, CashAccount43
)
from xsdata.formats.dataclass.serializers import XmlSerializer
from xsdata.formats.dataclass.serializers.config import SerializerConfig
from xsdata.models.datatype import XmlDateTime, XmlDate
from datetime import datetime, timedelta
import random
import uuid
from decimal import Decimal
import pytz
import psutil
import os
import time

def get_process_memory():
    """Get memory usage of current process in MB"""
    process = psutil.Process(os.getpid())
    return process.memory_info().rss / 1024 / 1024  # Convert to MB

def get_cpu_percent():
    """Get CPU usage percentage of current process"""
    process = psutil.Process(os.getpid())
    return process.cpu_percent(interval=0.1)

def generate_transaction_time(base_date, et_tz):
    """Generate a random time between 00:00 and 23:59:59.999 on the given date in ET"""
    hour = random.randint(0, 23)
    minute = random.randint(0, 59)
    second = random.randint(0, 59)
    microsecond = random.randint(0, 999999)
    
    # Create datetime in ET
    et_time = datetime.combine(base_date, datetime.min.time().replace(
        hour=hour, minute=minute, second=second, microsecond=microsecond
    ))
    return et_tz.localize(et_time)

def create_camt053_xml(num_transactions=100000, process_date=None):
    # Start timing and resource monitoring
    start_time = time.time()
    initial_memory = get_process_memory()
    print(f"\nInitial Memory Usage: {initial_memory:.2f} MB")
    
    # Set up Eastern Time zone
    et_tz = pytz.timezone('America/New_York')
    
    # If no date provided, use yesterday's date
    if process_date is None:
        process_date = (datetime.now(et_tz) - timedelta(days=1)).date()
    
    # Create the document structure
    doc = Document()
    
    # Create the statement
    stmt = BankToCustomerStatementV12()
    doc.bk_to_cstmr_stmt = stmt
    
    # Create group header
    grp_hdr = GroupHeader116()
    grp_hdr.msg_id = str(uuid.uuid4())
    now = datetime.now(et_tz)
    grp_hdr.cre_dt_tm = XmlDateTime(now.year, now.month, now.day, now.hour, now.minute, now.second)
    stmt.grphdr = grp_hdr
    
    # Create statement
    statement = AccountStatement13()
    statement.id = f'STMT{process_date.strftime("%Y%m%d")}'
    statement.cre_dt_tm = XmlDateTime(now.year, now.month, now.day, now.hour, now.minute, now.second)
    
    # Set from-to date period
    start_dt = datetime.combine(process_date, datetime.min.time(), tzinfo=et_tz)
    end_dt = datetime.combine(process_date, datetime.max.time().replace(microsecond=999999), tzinfo=et_tz)
    statement.fr_to_dt = DateTimePeriod1(
        fr_dt_tm=XmlDateTime(start_dt.year, start_dt.month, start_dt.day, start_dt.hour, start_dt.minute, start_dt.second),
        to_dt_tm=XmlDateTime(end_dt.year, end_dt.month, end_dt.day, end_dt.hour, end_dt.minute, end_dt.second)
    )
    
    # Set account information
    statement.acct = CashAccount43()
    statement.acct.id = AccountIdentification4Choice()
    statement.acct.id.othr = GenericAccountIdentification1()
    statement.acct.id.othr.id = str(uuid.uuid4())  # AcctId from E-Transfer
    statement.acct.id.othr.schme_nm = 'CACC'  # Changed from BBAN to CACC
    
    # Set opening balance
    opng_bal = CashBalance8()
    opng_bal.tp = BalanceType13()
    opng_bal.tp.cd = "OPBD"
    opng_bal.amt = ActiveOrHistoricCurrencyAndAmount()
    opng_bal.amt.ccy = 'CAD'
    opng_bal.amt.value = Decimal('1000000.00')
    opng_bal.dt = DateAndDateTime2Choice()
    opng_bal.dt.dt = XmlDate(process_date.year, process_date.month, process_date.day)
    statement.bal = [opng_bal]
    
    # Generate transactions
    statement.ntry = []
    
    print(f"Generating transactions for {process_date.strftime('%Y-%m-%d')}...")
    transaction_start_time = time.time()
    for i in range(num_transactions):
        if i % 10000 == 0:
            current_memory = get_process_memory()
            current_cpu = get_cpu_percent()
            elapsed_time = time.time() - transaction_start_time
            transactions_per_second = i / elapsed_time if elapsed_time > 0 else 0
            print(f"Generated {i:,} transactions - Memory: {current_memory:.2f} MB, CPU: {current_cpu:.1f}%, Speed: {transactions_per_second:.0f} tx/s")
            
        # Create entry
        entry = ReportEntry14()
        
        # Set amount (TxAmt from E-Transfer)
        entry.amt = ActiveOrHistoricCurrencyAndAmount()
        entry.amt.ccy = 'CAD'
        entry.amt.value = Decimal(str(round(random.uniform(10.00, 1000.00), 2)))
        
        # Set credit/debit indicator (TxSts from E-Transfer)
        entry.cdt_dbt_ind = random.choice(['CRDT', 'DBIT'])
        
        # Set entry status (required)
        entry.sts = EntryStatus1Choice()
        entry.sts.cd = random.choice(['BOOK', 'PDNG'])  # Randomly choose between Booked and Pending
        
        # Set bank transaction code (required)
        entry.bk_tx_cd = BankTransactionCodeStructure4()
        entry.bk_tx_cd.domn = None  # Optional
        entry.bk_tx_cd.prtry = None  # Optional
        
        # Set dates (EntryTimestamp from E-Transfer) in ET
        transaction_time = generate_transaction_time(process_date, et_tz)
        
        # Only set booking date for booked transactions
        if entry.sts.cd == 'BOOK':
            entry.bookg_dt = DateAndDateTime2Choice()
            entry.bookg_dt.dt_tm = XmlDateTime(
                transaction_time.year, transaction_time.month, transaction_time.day,
                transaction_time.hour, transaction_time.minute, transaction_time.second
            )
        
        # Set value date (required) - this represents when the transaction will be effective
        entry.val_dt = DateAndDateTime2Choice()
        entry.val_dt.dt_tm = XmlDateTime(
            transaction_time.year, transaction_time.month, transaction_time.day,
            transaction_time.hour, transaction_time.minute, transaction_time.second
        )
        
        # Set transaction details
        entry.ntry_dtls = []
        
        # Create transaction references
        tx_refs = TransactionReferences6()
        tx_refs.acct_svcr_ref = str(uuid.uuid4())  # ProfId from E-Transfer
        tx_refs.end_to_end_id = str(uuid.uuid4())  # EndToEndId from E-Transfer as UUID
        
        # Create entry transaction
        tx = EntryTransaction14()
        tx.refs = tx_refs
        
        # Create entry details
        entry_detail = EntryDetails13()
        entry_detail.tx_dtls = [tx]  # List of EntryTransaction14
        entry.ntry_dtls.append(entry_detail)
        
        # Add entry to statement
        statement.ntry.append(entry)
    
    transaction_end_time = time.time()
    transaction_duration = transaction_end_time - transaction_start_time
    
    # Add statement to document
    stmt.stmt = [statement]
    
    # Configure serializer
    config = SerializerConfig(pretty_print=True, xml_declaration=True, encoding='UTF-8')
    serializer = XmlSerializer(config=config)
    
    # Define namespace map
    ns_map = {
        None: "urn:iso:std:iso:20022:tech:xsd:camt.053.001.12"
    }
    
    # Serialize to XML
    print("\nSerializing to XML...")
    serialize_start_time = time.time()
    serialize_start_memory = get_process_memory()
    xml_content = serializer.render(doc, ns_map=ns_map)
    serialize_end_time = time.time()
    serialize_end_memory = get_process_memory()
    serialize_duration = serialize_end_time - serialize_start_time
    
    # Generate filename based on date
    filename = f'etransfer_gl_recon_{process_date.strftime("%Y%m%d")}.xml'
    
    # Get the directory of the current script
    script_dir = os.path.dirname(os.path.abspath(__file__))
    file_path = os.path.join(script_dir, filename)
    
    # Write to file
    print(f"Writing to file {file_path}...")
    write_start_time = time.time()
    write_start_memory = get_process_memory()
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(xml_content)
    write_end_time = time.time()
    write_end_memory = get_process_memory()
    write_duration = write_end_time - write_start_time
    
    # Calculate final metrics
    end_time = time.time()
    final_memory = get_process_memory()
    total_duration = end_time - start_time
    
    print("\nGeneration Performance Metrics:")
    print("-" * 50)
    print(f"Transaction Generation:")
    print(f"  Time: {transaction_duration:.2f} seconds")
    print(f"  Speed: {num_transactions / transaction_duration:.0f} transactions/second")
    print(f"  Memory: {get_process_memory() - initial_memory:.2f} MB")
    
    print(f"\nXML Serialization:")
    print(f"  Time: {serialize_duration:.2f} seconds")
    print(f"  Memory: {serialize_end_memory - serialize_start_memory:.2f} MB")
    
    print(f"\nFile Writing:")
    print(f"  Time: {write_duration:.2f} seconds")
    print(f"  Memory: {write_end_memory - write_start_memory:.2f} MB")
    
    print(f"\nOverall Performance:")
    print(f"  Total time: {total_duration:.2f} seconds")
    print(f"  Average speed: {num_transactions / total_duration:.0f} transactions/second")
    print(f"  Final memory: {final_memory:.2f} MB")
    print(f"  Total memory used: {final_memory - initial_memory:.2f} MB")
    
    print(f"\nGenerated {filename} with {num_transactions:,} transactions in camT.053.001.12 format")
    print(f"Transactions cover period: {process_date.strftime('%Y-%m-%d')} 00:00:00.000 to 23:59:59.999 ET")

if __name__ == '__main__':
    # Generate file for yesterday's transactions
    yesterday = (datetime.now(pytz.timezone('America/New_York')) - timedelta(days=1)).date()
    create_camt053_xml(process_date=yesterday)  # Use default num_transactions=100000