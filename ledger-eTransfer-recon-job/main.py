import argparse
import logging
import os
import shutil
from validation.file_name import validate_file_name
from validation.header import parse_and_validate_headers
from validation.transaction import validate_transaction_fields
from utils.s3 import download_s3_file
from lxml import etree

logger = logging.getLogger("eTransfer_recon.main")
logging.basicConfig(level=logging.INFO)

XSD_PATH = os.path.join(os.path.dirname(__file__), 'specification/camt053_etransfer_recon.xsd')

def parse_and_validate_xml(xml_path: str, xsd_path: str) -> etree._ElementTree:
    try:
        with open(xsd_path, 'rb') as schema_file:
            schema_doc = etree.parse(schema_file)
            schema = etree.XMLSchema(schema_doc)

        parser = etree.XMLParser(schema=schema)
        with open(xml_path, 'rb') as xml_file:
            doc = etree.parse(xml_file, parser)

        logger.info("XML validated successfully against the XSD")
        return doc
    except etree.XMLSchemaError as e:
        logger.error("XSD validation error: {e}")
        raise
    except etree.XMLSyntaxError as e:
        logger.error("XML syntax error: {e}")
        raise

def etransfer_recon(file_path: str) -> bool:
    logger.info(f"Running eTransfer recon for file: {file_path}")
    account_id = os.getenv("GL_ACCOUNT_ID")
    if not validate_file_name(file_path, account_id):
        return False
    try:
        doc = parse_and_validate_xml(file_path, XSD_PATH)
    except Exception as e:
        logger.error(f"Failed to parse XML: {e}")
        return False
    if not parse_and_validate_headers(doc):
        return False
    if not validate_transaction_fields(doc):
        return False
    logger.info("eTransfer Recon Completed Successfully.")
    return True


def main():
    logger.info("Starting main recon job entrypoint.")

    parser = argparse.ArgumentParser(description="Run eTransfer recon job (single file per invocation)")
    logger.info("Argument parser initialized with job description.")

    group = parser.add_mutually_exclusive_group(required=True)
    logger.info("Added mutually exclusive argument group for S3 vs local file.")

    group.add_argument("--s3-bucket", help="S3 bucket name (production mode)")
    logger.info("🪣 Added argument --s3-bucket to parser.")

    parser.add_argument("--s3-key", help="S3 object key (production mode)")
    logger.info("Added argument s3-key to parser.")

    group.add_argument("--local-file", help="Local XML file path (dev/test mode)")
    logger.info("Added argument local-file to parser.")

    args = parser.parse_args()
    logger.info("Parsed command-line arguments:args")



    file_path = None
    if args.s3_bucket and args.s3_key:
        file_path = download_s3_file(args.s3_bucket, args.s3_key)
    elif args.local_file:
        file_path = args.local_file
    else:
        logger.error("You must provide either --s3-bucket and --s3-key, or --local-file.")
        return

    try:
        etransfer_recon(file_path)
    finally:
        # Clean up temp file if S3 was used
        if args.s3_bucket and args.s3_key and file_path:
            try:
                shutil.rmtree(os.path.dirname(file_path))
            except Exception:
                pass

if __name__ == "__main__":
    main()