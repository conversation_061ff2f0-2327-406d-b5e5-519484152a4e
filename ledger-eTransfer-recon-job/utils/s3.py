import boto3
import tempfile
import os
import logging
logger = logging.getLogger("eTransfer_recon.utils.s3")
AWS_REGION = os.getenv('AWS_REGION')
AWS_ACCESS_KEY_ID = os.getenv("ACCESS_KEY_ID")
AWS_SECRET_ACCESS_KEY = os.getenv("SECRET_ACCESS_KEY")
s3 = boto3.client('s3', region_name=AWS_REGION,
                  aws_access_key_id=AWS_ACCESS_KEY_ID,
                  aws_secret_access_key=AWS_SECRET_ACCESS_KEY)

def download_s3_file(bucket: str, key: str) -> str:
    logger.info(f"AWS_ACCESS_KEY_ID: {AWS_ACCESS_KEY_ID}")
    logger.info(f"AWS_REGION: {AWS_REGION}")

    tmp_dir = tempfile.mkdtemp()
    local_path = os.path.join(tmp_dir, os.path.basename(key))
    try:
        s3.get_object(Bucket=bucket, Key=key)
        logger.info("Confirmed S3 object")
        s3.download_file(bucket, key, local_path)
        logger.info(f"Downloaded S3 file s3://{bucket}/{key} to {local_path}")
        return local_path
    except Exception as e:
        logger.error(f"Failed to download S3 file: {e}")
        raise