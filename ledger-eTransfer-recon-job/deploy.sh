IMAGE_TAG="$(date -u +"%Y%m%d%H%M%S")"

echo "IMAGE_TAG is ${IMAGE_TAG}"

aws configure sso --profile NON_LEDGER
aws ecr get-login-password --region ca-central-1 --profile=NON_LEDGER | docker login --username AWS --password-stdin 799455639446.dkr.ecr.ca-central-1.amazonaws.com

docker buildx build --platform linux/amd64 -t 799455639446.dkr.ecr.ca-central-1.amazonaws.com/etransfer-recon-job:${IMAGE_TAG} --load .
docker push 799455639446.dkr.ecr.ca-central-1.amazonaws.com/etransfer-recon-job:${IMAGE_TAG}
