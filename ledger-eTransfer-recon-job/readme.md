## eTransfer Recon Job

### Build and Push Docker Image

cd gl-eTransfer-recon-job
sh deploy.sh

### Local Development & Testing

#### 1. Install dependencies
```bash
pip install -r requirements.txt
```

#### 2. Run the job
```bash
python eTransfer_recon_job.py -f <S3 or local XML path> [--local_xml <local XML path>] [-d YYYY-MM-DD]
```

#### 3. Run unit tests
```bash
pytest tests/
```

- Set the environment variable `GL_ACCOUNT_ID` before running the job for file name validation.
- The job validates file name, XML schema, header fields, and transaction business rules.