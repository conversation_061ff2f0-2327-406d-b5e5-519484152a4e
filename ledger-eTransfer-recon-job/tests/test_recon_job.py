import os
import sys
import pytest
from lxml import etree

# Add the job directory to sys.path for import
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from validation.file_name import validate_file_name
from validation.header import parse_and_validate_headers
from validation.transaction import validate_transaction_fields

TEST_DATA_DIR = os.path.join(os.path.dirname(__file__), 'data')
VALID_XML = 'etransfer_gl_recon_a602be21-1770-41ff-9788-a0ddd2bdeaad_20250714.xml'

@pytest.fixture(scope="module", autouse=True)
def setup_env():
    os.environ['GL_ACCOUNT_ID'] = 'a602be21-1770-41ff-9788-a0ddd2bdeaad'

def parse_xml(xml_path):
    with open(xml_path, 'rb') as f:
        return etree.parse(f)

def test_validate_file_name_valid():
    file_path = os.path.join(TEST_DATA_DIR, VALID_XML)
    assert validate_file_name(file_path) is True

def test_validate_file_name_invalid():
    file_path = os.path.join(TEST_DATA_DIR, 'invalid_file_name.xml')
    with open(file_path, 'w') as f:
        f.write('dummy')
    assert validate_file_name(file_path) is False
    os.remove(file_path)

def test_parse_and_validate_headers_valid():
    xml_path = os.path.join(TEST_DATA_DIR, VALID_XML)
    doc = parse_xml(xml_path)
    assert parse_and_validate_headers(doc) is True

def test_parse_and_validate_headers_invalid():
    xml_path = os.path.join(TEST_DATA_DIR, 'invalid_header.xml')
    doc = parse_xml(xml_path)
    assert parse_and_validate_headers(doc) is False

def test_validate_transaction_fields_valid():
    xml_path = os.path.join(TEST_DATA_DIR, VALID_XML)
    doc = parse_xml(xml_path)
    assert validate_transaction_fields(doc) is True

def test_validate_transaction_fields_invalid():
    xml_path = os.path.join(TEST_DATA_DIR, 'invalid_transaction.xml')
    doc = parse_xml(xml_path)
    assert validate_transaction_fields(doc) is False