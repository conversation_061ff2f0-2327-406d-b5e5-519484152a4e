package com.peoplestrust.profile.api.v1.service;

import com.peoplestrust.profile.api.v1.ProfileApplication;
import com.peoplestrust.profile.api.v1.ProfileTestUtil;
import com.peoplestrust.profile.api.v1.model.Profile;
import com.peoplestrust.profile.persistence.entity.ProfileEntity;
import com.peoplestrust.profile.persistence.repository.write.ProfileRepository;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@SpringBootTest
@ActiveProfiles("test")
@ContextConfiguration(classes = ProfileApplication.class)
@ExtendWith(SpringExtension.class)
@Slf4j
public class UpdateProfileServiceTestIT {

  @Autowired
  ProfileServiceImpl profileService;

  @MockBean
  private ProfileRepository profileRepository;

  UUID uuid = UUID.randomUUID();

  @Test
  public void putTest() throws Exception {

    ProfileEntity profileEntity = ProfileTestUtil.createProfileEntity();
    when(profileRepository.save(any())).thenAnswer(invocation -> invocation.getArgument(0));
    when(profileRepository.findByRefId(any())).thenReturn(profileEntity);

    String profileID=UUID.randomUUID().toString();

    Profile putProfile = ProfileTestUtil.putProfile(profileID);
    Profile putReturnProfile = profileService.updateProfile(putProfile, profileID);

    // then
    assertEquals(putProfile.getLegalName(), putReturnProfile.getLegalName());
    assertEquals(putProfile.getDisplayName(), putReturnProfile.getDisplayName());

  }

}
