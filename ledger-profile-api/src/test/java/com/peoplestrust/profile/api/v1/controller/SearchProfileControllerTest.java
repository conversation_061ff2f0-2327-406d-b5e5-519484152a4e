package com.peoplestrust.profile.api.v1.controller;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.peoplestrust.profile.api.v1.ProfileTestUtil;
import com.peoplestrust.profile.domain.model.CreateLedgerProfileRequest;
import com.peoplestrust.profile.domain.model.CreateLedgerProfileResponse;
import com.peoplestrust.profile.persistence.entity.ProfileEntity;
import com.peoplestrust.profile.persistence.repository.read.ReadProfileRepository;
import com.peoplestrust.profile.persistence.repository.write.ProfileRepository;
import com.peoplestrust.util.api.common.config.APICommonUtilConstant;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

@Slf4j
@ActiveProfiles("test")
@ExtendWith(MockitoExtension.class)
@SpringBootTest
@AutoConfigureMockMvc
public class SearchProfileControllerTest {

    private static final String URL = "/v1/ledger/profile";
    private static final String requestId = UUID.randomUUID().toString();
    private static final String interactionId = UUID.randomUUID().toString();

    @Autowired
    private MockMvc mockMvc;

    private ObjectMapper objectMapper;
    private HttpHeaders headers;
    private CreateLedgerProfileRequest request;

    @MockBean
    private ProfileRepository profileRepository;

    @MockBean
    private ReadProfileRepository readProfileRepository;

    @BeforeEach
    public void setupBeforeTest() {
        objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.registerModule(new JavaTimeModule())
                .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
                .disable(DeserializationFeature.ADJUST_DATES_TO_CONTEXT_TIME_ZONE)
                .enable(SerializationFeature.WRITE_DATES_WITH_ZONE_ID);

        headers = new HttpHeaders();
        headers.add(APICommonUtilConstant.AUTHORIZATION, ProfileTestUtil.JWT_TOKEN);
        headers.add(APICommonUtilConstant.HEADER_INTERACTION_ID, interactionId);
        headers.add(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString());
        headers.add(APICommonUtilConstant.HEADER_AMAZON_REQUEST_ID, requestId);

        request = ProfileTestUtil.createProfilePostRequest();
    }

    @Test
    void searchProfileWithName_success() throws Exception {
        ProfileEntity profileEntity = ProfileTestUtil.createProfileEntity();
        List<ProfileEntity> profileEntityList = ProfileTestUtil.createProfileEntityLst(5);
        when(profileRepository.findByRefId(any())).thenReturn(profileEntity);
        when(readProfileRepository.findByLegalNameContainingIgnoreCase(any())).thenReturn(profileEntityList);
        when(readProfileRepository.findByLegalNameContainingIgnoreCaseAndStatus(any(), any())).thenReturn(profileEntityList);
        when(profileRepository.save(any())).thenAnswer(invocation -> invocation.getArgument(0));


        MvcResult result = this.mockMvc.perform(
                MockMvcRequestBuilders.post(URL).headers(headers).content(objectMapper.writeValueAsString(request)).contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON)).andExpect(status().isCreated()).andReturn();
        CreateLedgerProfileResponse response = objectMapper.readValue(result.getResponse().getContentAsString(), CreateLedgerProfileResponse.class);

        String apiUrl = URL + "/search" + "?profile-name=" + response.getLegalName();
        MvcResult result2 = this.mockMvc.perform(
                        MockMvcRequestBuilders.get(apiUrl).headers(headers).contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk()).andReturn();
        Assertions.assertNotNull(result2.getResponse().getContentAsString());
    }

    @Test
    void searchProfileWithPartialName_success() throws Exception {
        ProfileEntity profileEntity = ProfileTestUtil.createProfileEntity();
        List<ProfileEntity> profileEntityList = ProfileTestUtil.createProfileEntityLst(5);
        when(profileRepository.findByRefId(any())).thenReturn(profileEntity);
        when(readProfileRepository.findByLegalNameContainingIgnoreCase(any())).thenReturn(profileEntityList);
        when(readProfileRepository.findByLegalNameContainingIgnoreCaseAndStatus(any(), any())).thenReturn(profileEntityList);
        when(profileRepository.save(any())).thenAnswer(invocation -> invocation.getArgument(0));


        MvcResult result = this.mockMvc.perform(
                MockMvcRequestBuilders.post(URL).headers(headers).content(objectMapper.writeValueAsString(request)).contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON)).andExpect(status().isCreated()).andReturn();
        CreateLedgerProfileResponse response = objectMapper.readValue(result.getResponse().getContentAsString(), CreateLedgerProfileResponse.class);

        String apiUrl = URL + "/search" + "?profile-name=" + response.getLegalName().substring(0, 4);
        MvcResult result2 = this.mockMvc.perform(
                        MockMvcRequestBuilders.get(apiUrl).headers(headers).contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk()).andReturn();
        Assertions.assertNotNull(result2.getResponse().getContentAsString());
    }
}
