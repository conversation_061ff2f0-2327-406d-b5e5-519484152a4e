package com.peoplestrust.account.api.v1.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.peoplestrust.account.api.v1.common.TestUtil;
import com.peoplestrust.account.api.v1.model.Account;
import com.peoplestrust.account.domain.model.UpdateLedgerAccountRequest;
import com.peoplestrust.account.persistence.entity.AccountEntity;
import com.peoplestrust.account.persistence.repository.write.AccountRepository;
import com.peoplestrust.util.api.common.config.APICommonUtilConstant;
import com.peoplestrust.util.api.common.util.JsonUtil;
import java.time.Instant;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

@Slf4j
@ActiveProfiles("test")
@ExtendWith(MockitoExtension.class)
@SpringBootTest
@AutoConfigureMockMvc
public class UpdateAccountControllerTest {

  private static final String URL = "/v1/ledger/account";
  private static final String profileId = "28dfaa4d-5889-4cbd-bd46-e6201e560b6c";
  private static final String accountRefId = UUID.randomUUID().toString();
  @Autowired
  private MockMvc mockMvc;
  @MockBean
  private AccountRepository accountRepository;
  private UpdateLedgerAccountRequest putRequest;
  private HttpHeaders headers;
  private ObjectMapper objectMapper;

  @BeforeEach
  public void setupBeforeTest() {
    putRequest = TestUtil.createAccountPutRequest();
    objectMapper = new ObjectMapper();
    objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    UUID interactionId = UUID.randomUUID();
    headers = new HttpHeaders();
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_ID, interactionId.toString());
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString());
    headers.add(APICommonUtilConstant.HEADER_PROFILE_ID, profileId);
    headers.add(APICommonUtilConstant.AUTHORIZATION, TestUtil.JWT_TOKEN);
  }

  @Test
  void UpdateAccount_success() throws Exception {
    Account account = TestUtil.createAccount(profileId);
    AccountEntity accountEntity = TestUtil.getAccountData(account, profileId);

    when(accountRepository.findByRefId(any())).thenReturn(Optional.of(accountEntity));
    when(accountRepository.save(any())).thenAnswer(invocation -> invocation.getArgument(0));

    this.mockMvc
        .perform(
            MockMvcRequestBuilders.put(URL + "/" + accountRefId)
                .headers(headers)
                .content(JsonUtil.toString(putRequest))
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andReturn();
  }

  @Test
  void UpdateAccount_MissingHeader() throws Exception {
    Account account = TestUtil.createAccount(profileId);
    AccountEntity accountEntity = TestUtil.getAccountData(account, profileId);

    when(accountRepository.findByRefId(any())).thenReturn(Optional.of(accountEntity));
    when(accountRepository.save(any())).thenAnswer(invocation -> invocation.getArgument(0));
    headers.remove(APICommonUtilConstant.AUTHORIZATION);
    MvcResult result = this.mockMvc
        .perform(
            MockMvcRequestBuilders.put(URL + "/" + accountRefId)
                .headers(headers)
                .content(JsonUtil.toString(putRequest))
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();

    objectMapper = new ObjectMapper();
    JsonNode jsonNode = objectMapper.readTree(Objects.requireNonNull(result.getResponse().getContentAsString()));
    assertEquals("MISSING_HEADER", jsonNode.get("error").get("code").asText());
    assertEquals("Authorization", jsonNode.get("error").get("additional_information").asText());
  }

  @Test
  void UpdateAccount_BadToken() throws Exception {
    Account account = TestUtil.createAccount(profileId);
    AccountEntity accountEntity = TestUtil.getAccountData(account, profileId);

    when(accountRepository.findByRefId(any())).thenReturn(Optional.of(accountEntity));
    when(accountRepository.save(any())).thenAnswer(invocation -> invocation.getArgument(0));
    headers.set(APICommonUtilConstant.AUTHORIZATION, TestUtil.BAD_JWT_TOKEN);
    MvcResult result = this.mockMvc
        .perform(
            MockMvcRequestBuilders.put(URL + "/" + accountRefId)
                .headers(headers)
                .content(JsonUtil.toString(putRequest))
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isUnauthorized())
        .andReturn();

    objectMapper = new ObjectMapper();
    JsonNode jsonNode = objectMapper.readTree(Objects.requireNonNull(result.getResponse().getContentAsString()));
    assertEquals("UNAUTHORIZED", jsonNode.get("error").get("code").asText());
    assertEquals("UNAUTHORIZED", jsonNode.get("error").get("additional_information").asText());
  }
}
