package com.peoplestrust.account.api.v1.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.peoplestrust.account.api.v1.common.TestUtil;
import com.peoplestrust.account.api.v1.model.Account;
import com.peoplestrust.account.api.v1.service.ValidationService;
import com.peoplestrust.account.persistence.entity.AccountEntity;
import com.peoplestrust.account.persistence.repository.read.ReadAccountRepository;
import com.peoplestrust.util.api.common.config.APICommonUtilConstant;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

@Slf4j
@ActiveProfiles("test")
@ExtendWith(MockitoExtension.class)
@SpringBootTest
@AutoConfigureMockMvc
public class GetAccountControllerTest {

  private static final String URL = "/v1/ledger/account";
  private static final String profileId = UUID.randomUUID().toString();
  @Autowired
  private MockMvc mockMvc;
  @MockBean
  private ValidationService validationService;
  @MockBean
  private ReadAccountRepository readAccountRepository;
  private HttpHeaders headers;
  private ObjectMapper objectMapper;
  private Account account;

  @BeforeEach
  public void setupBeforeTest() throws Exception {
    objectMapper = new ObjectMapper();
    objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    UUID interactionId = UUID.randomUUID();
    headers = new HttpHeaders();
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_ID, interactionId.toString());
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString());
    headers.add(APICommonUtilConstant.HEADER_PROFILE_ID, profileId);
    headers.add(APICommonUtilConstant.AUTHORIZATION, TestUtil.JWT_TOKEN);
  }

  @Test
  void getAccount_success() throws Exception {
    account = TestUtil.createAccount(profileId);

    Account account = TestUtil.createAccount(profileId);
    AccountEntity accountEntity = TestUtil.getAccountData(account, profileId);
    List<AccountEntity> accountEntityList = new ArrayList<>();
    accountEntityList.add(accountEntity);

    when(validationService.isProfile(any(), any())).thenReturn(true);
    when(readAccountRepository.findByProfileId(any())).thenReturn(accountEntityList);
    this.mockMvc
        .perform(
            MockMvcRequestBuilders.get(URL)
                .headers(headers)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andReturn();
  }

  @Test
  void getAccount_MissingHeader() throws Exception {
    account = TestUtil.createAccount(profileId);

    Account account = TestUtil.createAccount(profileId);
    AccountEntity accountEntity = TestUtil.getAccountData(account, profileId);
    List<AccountEntity> accountEntityList = new ArrayList<>();
    accountEntityList.add(accountEntity);
    headers.remove(APICommonUtilConstant.AUTHORIZATION);
    when(validationService.isProfile(any(), any())).thenReturn(true);
    when(readAccountRepository.findByProfileId(any())).thenReturn(accountEntityList);
    MvcResult result = this.mockMvc
        .perform(
            MockMvcRequestBuilders.get(URL)
                .headers(headers)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();

    objectMapper = new ObjectMapper();
    JsonNode jsonNode = objectMapper.readTree(Objects.requireNonNull(result.getResponse().getContentAsString()));
    assertEquals("MISSING_HEADER", jsonNode.get("error").get("code").asText());
    assertEquals("Authorization", jsonNode.get("error").get("additional_information").asText());
  }

  @Test
  void getAccount_BadToken() throws Exception {
    account = TestUtil.createAccount(profileId);

    Account account = TestUtil.createAccount(profileId);
    AccountEntity accountEntity = TestUtil.getAccountData(account, profileId);
    List<AccountEntity> accountEntityList = new ArrayList<>();
    accountEntityList.add(accountEntity);
    headers.set(APICommonUtilConstant.AUTHORIZATION, TestUtil.BAD_JWT_TOKEN);
    when(validationService.isProfile(any(), any())).thenReturn(true);
    when(readAccountRepository.findByProfileId(any())).thenReturn(accountEntityList);
    MvcResult result = this.mockMvc
        .perform(
            MockMvcRequestBuilders.get(URL)
                .headers(headers)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isUnauthorized())
        .andReturn();

    objectMapper = new ObjectMapper();
    JsonNode jsonNode = objectMapper.readTree(Objects.requireNonNull(result.getResponse().getContentAsString()));
    assertEquals("UNAUTHORIZED", jsonNode.get("error").get("code").asText());
    assertEquals("UNAUTHORIZED", jsonNode.get("error").get("additional_information").asText());
  }
}
