package com.peoplestrust.account.api.v1.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.peoplestrust.account.api.v1.common.TestUtil;
import com.peoplestrust.account.api.v1.model.Account;
import com.peoplestrust.account.api.v1.service.AccountService;
import com.peoplestrust.account.api.v1.service.ValidationService;
import com.peoplestrust.transaction.domain.model.BalanceSnapshotResponse;
import com.peoplestrust.util.api.common.config.APICommonUtilConstant;
import com.peoplestrust.util.api.common.util.DateUtils;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

@Slf4j
@ActiveProfiles("test")
@ExtendWith(MockitoExtension.class)
@SpringBootTest
@AutoConfigureMockMvc
public class SearchAccountBalancesSnapshotsControllerTest {

  private static final String URL = "/v1/ledger/account/balance_search/";
  private static final String profileId = UUID.randomUUID().toString();
  private static final String accountRefId = UUID.randomUUID().toString();

  @Autowired
  private MockMvc mockMvc;

  @MockBean
  private ValidationService validationService;

  @MockBean
  private AccountService accountService;

  private HttpHeaders headers;
  private ObjectMapper objectMapper;
  private Account account;
  private List<BalanceSnapshotResponse> balanceSnapshotResponseList;

  @BeforeEach
  public void setupBeforeTest() throws Exception {
    objectMapper = new ObjectMapper();
    objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    UUID interactionId = UUID.randomUUID();
    headers = new HttpHeaders();
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_ID, interactionId.toString());
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString());
    headers.add(APICommonUtilConstant.HEADER_PROFILE_ID, profileId);
    headers.add(APICommonUtilConstant.AUTHORIZATION, TestUtil.JWT_TOKEN);

    account = TestUtil.createAccount(profileId);
    balanceSnapshotResponseList = getBalanceSnapshotResponseList();
  }

  private List<BalanceSnapshotResponse> getBalanceSnapshotResponseList() {
    BalanceSnapshotResponse b1 = new BalanceSnapshotResponse();
    BigDecimal credit = BigDecimal.valueOf(0.00);
    BigDecimal debit = BigDecimal.valueOf(0.00);
    BigDecimal reserve = BigDecimal.valueOf(13333.00);
    BigDecimal totalAmount = BigDecimal.valueOf(********.60);
    b1.setTotalAmountDebit(debit);
    b1.setTotalReserveAmount(reserve);
    b1.setTotalAmountCredit(credit);
    b1.setTotalAmount(totalAmount);
    b1.setTotalPendingAmount(BigDecimal.TEN);
    b1.setCreatedDateTime(OffsetDateTime.now());
    b1.setEffectiveFromDateTime(OffsetDateTime.parse("2025-07-20T21:43:00.000239Z"));
    b1.setEffectiveToDateTime(DateUtils.startOfDay(0).atOffset(ZoneOffset.UTC));

    List<BalanceSnapshotResponse> balanceList = new ArrayList<>();
    balanceList.add(b1);
    return balanceList;
  }

  @Test
  public void searchBalanceSnapshotTest() throws Exception {
    // Arrange
    when(accountService.searchBalanceSnapshots(
        any(), any(), any(), any(), any(), any(), any())).thenReturn(balanceSnapshotResponseList);

    // Act
    MvcResult result = this.mockMvc.perform(
            MockMvcRequestBuilders.get(URL + accountRefId)
                .headers(headers)
                .param("startTime", "2025-01-01T00:00:00Z")
                .param("endTime", "2025-12-31T23:59:59Z")
                .param("offset", "0")
                .param("maxResponseItems", "25")
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$.length()").value(1)) // Assert response array size
        .andExpect(jsonPath("$[0].total_credit_amount").value(0.0)) // Assert total_credit_amount
        .andExpect(jsonPath("$[0].total_debit_amount").value(0.0)) // Assert total_debit_amount
        .andExpect(jsonPath("$[0].total_reserve_amount").value(13333.0)) // Assert total_reserve_amount
        .andExpect(jsonPath("$[0].total_amount").value(********.60)) // Assert total_amount
        .andExpect(jsonPath("$[0].total_pending_amount").value(10.00)) // Assert total_pending_amount
        .andReturn();

    // Additional Assertions
    String responseContent = result.getResponse().getContentAsString();
    JsonNode responseJson = objectMapper.readTree(responseContent);
    assertEquals(1, responseJson.size(), "Response should contain one balance snapshot");
  }
}