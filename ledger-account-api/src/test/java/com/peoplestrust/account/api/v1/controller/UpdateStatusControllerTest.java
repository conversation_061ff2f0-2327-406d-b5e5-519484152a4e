package com.peoplestrust.account.api.v1.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.peoplestrust.account.api.v1.common.TestUtil;
import com.peoplestrust.account.domain.model.UpdateLedgerAccountStatusRequest;
import com.peoplestrust.account.persistence.repository.write.AccountRepository;
import com.peoplestrust.util.api.common.config.APICommonUtilConstant;
import com.peoplestrust.util.api.common.util.JsonUtil;
import java.time.Instant;
import java.util.Objects;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

@Slf4j
@ActiveProfiles("test")
@ExtendWith(MockitoExtension.class)
@SpringBootTest
@AutoConfigureMockMvc
public class UpdateStatusControllerTest {

  private static final String URL = "/v1/ledger/account";
  private static final String profileId = UUID.randomUUID().toString();
  @Autowired
  private MockMvc mockMvc;
  @MockBean
  private AccountRepository accountRepository;
  private HttpHeaders headers;
  private ObjectMapper objectMapper;

  @BeforeEach
  public void setupBeforeTest() throws Exception {
    objectMapper = new ObjectMapper();
    objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    UUID interactionId = UUID.randomUUID();
    headers = new HttpHeaders();
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_ID, interactionId.toString());
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString());
    headers.add(APICommonUtilConstant.HEADER_PROFILE_ID, profileId);
    headers.add(APICommonUtilConstant.AUTHORIZATION, TestUtil.JWT_TOKEN);
  }

  @Test
  void updateStatusInactiveProfileNotExists() throws Exception {
    String refId = "28dfaa4d-5889-4cbd-bd46-e6201e560b6c";
    assertFalse(accountRepository.findByRefId(UUID.fromString(refId)).isPresent());
    UpdateLedgerAccountStatusRequest body = TestUtil.createUpdateLedgerAccountStatusRequest();
    MvcResult result =
        this.mockMvc
            .perform(
                MockMvcRequestBuilders.patch(URL + "/" + refId + "/status")
                    .headers(headers)
                    .content(JsonUtil.toString(body))
                    .contentType(MediaType.APPLICATION_JSON)
                    .accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNotFound())
            .andReturn();
  }

  @Test
  void updateStatusInactiveProfileBadField() throws Exception {
    String refId = "28dfaa4d-5889-4cbd-bd46-e6201e560b6c";
    assertFalse(accountRepository.findByRefId(UUID.fromString(refId)).isPresent());
    String body = "{\"status\":\"VALID\",\"reason\":\"TEST\"}";

    MvcResult result =
        this.mockMvc
            .perform(
                MockMvcRequestBuilders.patch(URL + "/" + refId + "/status")
                    .headers(headers)
                    .content(body)
                    .contentType(MediaType.APPLICATION_JSON)
                    .accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isBadRequest())
            .andReturn();
  }

  @Test
  void updateStatus_MissingHeader() throws Exception {
    String refId = "28dfaa4d-5889-4cbd-bd46-e6201e560b6c";
    assertFalse(accountRepository.findByRefId(UUID.fromString(refId)).isPresent());
    String body = "{\"status\":\"ACTIVE\",\"reason\":\"TEST\"}";
    headers.remove(APICommonUtilConstant.AUTHORIZATION);
    MvcResult result =
        this.mockMvc
            .perform(
                MockMvcRequestBuilders.patch(URL + "/" + refId + "/status")
                    .headers(headers)
                    .content(body)
                    .contentType(MediaType.APPLICATION_JSON)
                    .accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isBadRequest())
            .andReturn();

    objectMapper = new ObjectMapper();
    JsonNode jsonNode = objectMapper.readTree(Objects.requireNonNull(result.getResponse().getContentAsString()));
    assertEquals("MISSING_HEADER", jsonNode.get("error").get("code").asText());
    assertEquals("Authorization", jsonNode.get("error").get("additional_information").asText());
  }

  @Test
  void updateStatus_BadToken() throws Exception {
    String refId = "28dfaa4d-5889-4cbd-bd46-e6201e560b6c";
    assertFalse(accountRepository.findByRefId(UUID.fromString(refId)).isPresent());
    String body = "{\"status\":\"ACTIVE\",\"reason\":\"TEST\"}";
    headers.set(APICommonUtilConstant.AUTHORIZATION, TestUtil.BAD_JWT_TOKEN);
    MvcResult result =
        this.mockMvc
            .perform(
                MockMvcRequestBuilders.patch(URL + "/" + refId + "/status")
                    .headers(headers)
                    .content(body)
                    .contentType(MediaType.APPLICATION_JSON)
                    .accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isUnauthorized())
            .andReturn();

    objectMapper = new ObjectMapper();
    JsonNode jsonNode = objectMapper.readTree(Objects.requireNonNull(result.getResponse().getContentAsString()));
    assertEquals("UNAUTHORIZED", jsonNode.get("error").get("code").asText());
    assertEquals("UNAUTHORIZED", jsonNode.get("error").get("additional_information").asText());
  }
}
