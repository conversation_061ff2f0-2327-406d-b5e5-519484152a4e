package com.peoplestrust.account.api.v1.service;

import com.peoples.banking.util.logger.annotation.PerfLogger;
import com.peoplestrust.account.api.v1.mapper.AccountMapper;
import com.peoplestrust.account.api.v1.model.Account;
import com.peoplestrust.account.domain.model.RetrieveLedgerAccountBalanceResponse;
import com.peoplestrust.account.persistence.entity.AccountEntity;
import com.peoplestrust.account.persistence.entity.AccountStatus;
import com.peoplestrust.account.persistence.entity.MonetaryUnit;
import com.peoplestrust.account.persistence.entity.OptionsEntity;
import com.peoplestrust.account.persistence.repository.read.ReadAccountRepository;
import com.peoplestrust.account.persistence.repository.write.AccountRepository;
import com.peoplestrust.transaction.domain.model.BalanceSnapshotResponse;
import com.peoplestrust.util.api.common.exception.ExceptionUtil;
import com.peoplestrust.util.api.common.exception.InvalidFieldException;
import com.peoplestrust.util.api.common.exception.ResourceNotFoundException;
import com.peoplestrust.util.api.common.util.DateUtils;
import com.peoplestrust.util.api.common.util.Messages;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import jakarta.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionSystemException;
import org.springframework.util.StringUtils;
import org.springframework.web.client.ResourceAccessException;

/**
 * Account service implementation
 */
@Service
@Slf4j
public class AccountServiceImpl implements AccountService {

  /**
   * Account mapper
   */
  @Autowired
  private AccountMapper accountMapper;

  /**
   * Account persistence repository
   */
  @Autowired
  private AccountRepository accountRepository;

  @Autowired
  private ReadAccountRepository readAccountRepository;


  /**
   * Validation service
   */
  @Autowired
  private ValidationService validationService;

  /**
   * {@inheritDoc}
   */
  @PerfLogger
  @Override
  public Account createAccount(Account account, String profileId, String interactionId) throws Exception {
    if (account == null) {
      throw new NullPointerException();
    }
    AccountEntity entity = null;

    try {
      // Profile validation check
      if (null != profileId && validationService.isProfile(profileId, interactionId)) {
        account.setProfileId(profileId);
      } else {

        log.warn("invalid field exception on creating account", Messages.PROFILE_NOT_FOUND);

        throw new InvalidFieldException(Messages.PROFILE_NOT_FOUND);
      }

      // Account validation check

      entity = accountRepository.save(accountMapper.fromAccountToAccountEntity(account));
    } catch (ConstraintViolationException ex) {

      log.warn("constraint violation on creating account", ex);

      throw new InvalidFieldException(ExceptionUtil.message(ex));
    } catch (DataIntegrityViolationException ex) {

      log.warn("data integrity violation on creating account", ex);

      throw new InvalidFieldException(ex.getCause().getCause().getMessage());
    } catch (IllegalArgumentException ex) {

      log.warn("illegal argument exception on creating account", ex);

      throw new InvalidFieldException(ExceptionUtil.message(ex));
    } catch (org.springframework.web.client.HttpClientErrorException ex) {
      throw new com.peoplestrust.util.api.common.exception.HttpClientErrorException(ex.getMessage());
    } catch (ResourceNotFoundException ex) {
      throw new ResourceNotFoundException(ex.getMessage());
    } catch (ResourceAccessException ex) {
      throw new com.peoplestrust.util.api.common.exception.ResourceAccessException(ex.getCause().getMessage());
    } catch (Exception ex) {

      log.error("unexpected exception on creating account", ex);

      throw new Exception(ex.getMessage());
    }

    if (log.isDebugEnabled()) {
      log.debug("Account created; account = {}", entity);
    }

    // return result
    return accountMapper.fromAccountEntityToAccount(entity);
  }

  /**
   * {@inheritDoc}
   */
  @PerfLogger
  @Override
  @CacheEvict(value = "AccountDetails", key = "#profileIdAndAccountId", cacheManager = "cacheManagerAccount")
  public Account updateAccount(Account accountUpdateRequest, String refId, String profileId, String profileIdAndAccountId) throws Exception {

    // account validation check
    AccountEntity accountEntity = accountRepository.findByRefId(UUID.fromString(refId))
        .orElseThrow(() -> new ResourceNotFoundException(Messages.ACCOUNT_NOT_FOUND));

    // profile validation check
    if (null == profileId || (null != profileId && !accountEntity.getProfileId().equals(UUID.fromString(profileId)))) {

      log.warn("invalid field exception on creating account", Messages.PROFILE_NOT_FOUND);

      throw new InvalidFieldException(Messages.PROFILE_NOT_FOUND);
    }

    // update account
    accountEntity.setName(accountUpdateRequest.getName());
    accountEntity.setDescription(accountUpdateRequest.getDescription());
    MonetaryUnit monetaryUnit = MonetaryUnit.valueOf(accountUpdateRequest.getMonetaryUnit());
    accountEntity.setMonetaryUnit(monetaryUnit);

    OptionsEntity optionsEntity = accountEntity.getOptions();
    if (null != accountUpdateRequest.getOptions() && null != accountUpdateRequest.getOptions().getFundHoldDays()) {
      optionsEntity.setFundHoldDays(accountUpdateRequest.getOptions().getFundHoldDays());
    }
    if (null != accountUpdateRequest.getOptions() && null != accountUpdateRequest.getOptions().getOverdraftAmount()) {
      optionsEntity.setOverdraftAmount(accountUpdateRequest.getOptions().getOverdraftAmount());
    }

    accountEntity.setOptions(optionsEntity);
    accountEntity.setUpdatedDateTime(DateUtils.offset());
    AccountEntity newEntity = null;

    try {
      newEntity = accountRepository.save(accountEntity);
    } catch (ConstraintViolationException ex) {

      log.warn("constraint violation exception on updating account", ExceptionUtil.message(ex));

      throw new InvalidFieldException(ExceptionUtil.message(ex));
    } catch (TransactionSystemException ex) {

      log.warn("transaction system exception on updating account", ExceptionUtil.message(ex));

      throw new InvalidFieldException(ExceptionUtil.message(ex));
    } catch (IllegalArgumentException ex) {

      log.warn("illegal argument exception on updating account", ExceptionUtil.message(ex));

      throw new InvalidFieldException(ExceptionUtil.message(ex));
    } catch (Exception ex) {

      log.error("unexpected exception on creating account", ex.getMessage());

      throw new Exception(ex.getMessage());
    }

    Account updatedAccount = accountMapper.fromAccountEntityToAccount(newEntity);

    if (log.isDebugEnabled()) {
      log.debug("updated account; new account = {}", updatedAccount);
    }

    // return result
    return updatedAccount;
  }

  /**
   * {@inheritDoc}
   */
  @PerfLogger
  @Override
  public List<Account> getAccounts(String profileId, String interactionId) throws Exception {
    List<Account> accounts = new ArrayList<>();
    List<AccountEntity> entities;

    try {
      validationService.isProfile(profileId, interactionId);

      entities = readAccountRepository.findByProfileId(UUID.fromString(profileId));

      if (entities.isEmpty()) {

        log.warn("Resource not found exception on getting accounts", Messages.ACCOUNT_NOT_FOUND);

        throw new ResourceNotFoundException(Messages.ACCOUNT_NOT_FOUND);
      }

      // maps each account entity to an account
      entities.forEach(a -> {
        accounts.add(accountMapper.fromAccountEntityToAccount(a));
      });

      if (log.isDebugEnabled()) {
        log.debug("{} accounts found for profileId {}", accounts.size(), profileId);
      }
    } catch (org.springframework.web.client.HttpClientErrorException ex) {
      throw new com.peoplestrust.util.api.common.exception.HttpClientErrorException(ex.getMessage());
    } catch (ResourceNotFoundException ex) {
      throw new ResourceNotFoundException(ex.getMessage());
    } catch (ResourceAccessException ex) {
      throw new com.peoplestrust.util.api.common.exception.ResourceAccessException(ex.getCause().getMessage());
    } catch (Exception ex) {
      throw new Exception(ex.getMessage());
    }
    // return result
    return accounts;
  }

  /**
   * {@inheritDoc}
   */
  @PerfLogger
  @Override
  public List<Account> loadAccounts() throws Exception {
    List<Account> accounts = new ArrayList<>();
    List<AccountEntity> entities = null;

    entities = readAccountRepository.findAll();

    // maps each account entity to an account
    entities.forEach(a -> {
      accounts.add(accountMapper.fromAccountEntityToAccount(a));
    });

    // return result
    return accounts;
  }

  /**
   * {@inheritDoc}
   */
  @PerfLogger
  @Override
  @CacheEvict(value = "AccountDetails", key = "#profileIdAndAccountId", cacheManager = "cacheManagerAccount")
  public Optional<Account> updateAccountStatus(String refId, com.peoplestrust.account.domain.model.AccountStatus accountStatus, String reason, String profileId,
      String profileIdAndAccountId)
      throws Exception {

    if (StringUtils.isEmpty(reason) || !StringUtils.hasText(reason)) {

      log.warn("invalid field exception on updating account status", Messages.INVALID_REASON);

      throw new InvalidFieldException(Messages.INVALID_REASON);
    }

    AccountEntity accountEntity = accountRepository.findByRefId(UUID.fromString(refId))
        .orElseThrow(() -> new ResourceNotFoundException(Messages.ACCOUNT_NOT_FOUND));

    // profile validation check
    if (null == profileId || (null != profileId && !accountEntity.getProfileId().equals(UUID.fromString(profileId)))) {

      log.warn("invalid field exception on creating account", Messages.PROFILE_NOT_FOUND);

      throw new InvalidFieldException(Messages.PROFILE_NOT_FOUND);
    }

    AccountStatus status = AccountStatus.valueOf(accountStatus.name());
    accountEntity.setStatus(status);
    accountEntity.setReason(reason);

    try {
      accountEntity = accountRepository.save(accountEntity);
    } catch (ConstraintViolationException ex) {

      log.warn("constraint violation exception on updating account", ExceptionUtil.message(ex));

      throw new InvalidFieldException(ExceptionUtil.message(ex));
    } catch (TransactionSystemException ex) {

      log.warn("transaction system exception on updating account", ExceptionUtil.message(ex));

      throw new InvalidFieldException(ExceptionUtil.message(ex));
    } catch (Exception ex) {

      log.error("unexpected exception on creating account", ex.getMessage());

      throw new Exception(ex.getMessage());
    }

    Optional<Account> updatedAccount = Optional.ofNullable(accountMapper.fromAccountEntityToAccount(accountEntity));

    if (log.isDebugEnabled()) {
      log.debug("updated account; new account = {}", updatedAccount);
    }

    // return result
    return updatedAccount;
  }

  /**
   * {@inheritDoc}
   */
  @PerfLogger
  @Override
  public RetrieveLedgerAccountBalanceResponse retrieveLedgerAccountBalance(String profileRefId, String accountRefId, String interactionId)
      throws ResourceNotFoundException, InvalidFieldException {
    // account validation check
    AccountEntity accountEntity = readAccountRepository.findByRefId(UUID.fromString(accountRefId))
        .orElseThrow(() -> new ResourceNotFoundException(Messages.ACCOUNT_NOT_FOUND));

    // profile validation check
    if (null == profileRefId || (null != profileRefId && !accountEntity.getProfileId().equals(UUID.fromString(profileRefId)))) {
      throw new ResourceNotFoundException(Messages.PROFILE_NOT_FOUND);
    }

    BigDecimal overdraftAmount = accountEntity.getOptions().getOverdraftAmount();

    return validationService.retrieveBalance(accountRefId, profileRefId, interactionId, overdraftAmount);
  }

  /**
   * {@inheritDoc}
   */

  @PerfLogger
  @Override
  public List<BalanceSnapshotResponse> searchBalanceSnapshots(String profileRefId, String accountRefId, String interactionId, LocalDateTime startTime, LocalDateTime endTime, Integer offset,
      Integer maxResponseItems) throws Exception {
    // account validation check
    AccountEntity accountEntity = readAccountRepository.findByRefId(UUID.fromString(accountRefId))
        .orElseThrow(() -> new ResourceNotFoundException(Messages.ACCOUNT_NOT_FOUND));

    // profile validation check
    if (null == profileRefId || (null != profileRefId && !accountEntity.getProfileId().equals(UUID.fromString(profileRefId)))) {
      throw new ResourceNotFoundException(Messages.PROFILE_NOT_FOUND);
    }

    List<BalanceSnapshotResponse> balanceResponse = null;

    try {
      balanceResponse = validationService.searchBalanceSnapshots(accountRefId, profileRefId, interactionId, startTime, endTime, offset, maxResponseItems);
    } catch (org.springframework.web.client.HttpClientErrorException ex) {
      throw new com.peoplestrust.util.api.common.exception.HttpClientErrorException(ex.getMessage());
    } catch (ResourceNotFoundException ex) {
      throw new ResourceNotFoundException(ex.getMessage());
    } catch (ResourceAccessException ex) {
      throw new com.peoplestrust.util.api.common.exception.ResourceAccessException(ex.getCause().getMessage());
    } catch (Exception ex) {
      throw new Exception(ex.getMessage());
    }

    return balanceResponse;
  }


  /**
   * {@inheritDoc}
   */
  @PerfLogger
  @Override
  @Cacheable(key = "#profileIdAndAccountId", value = "AccountDetails", cacheManager = "cacheManagerAccount")
  public Account getAccountById(String refId, String profileId, String profileIdAndAccountId) throws Exception {
    AccountEntity accountEntity = readAccountRepository.findByRefId(UUID.fromString(refId))
        .orElseThrow(() -> new ResourceNotFoundException(Messages.ACCOUNT_NOT_FOUND));

    // profile validation check
    if (null == profileId || (null != profileId && !accountEntity.getProfileId().equals(UUID.fromString(profileId)))) {
      throw new ResourceNotFoundException(Messages.PROFILE_NOT_FOUND);
    }

    log.debug("account found for ref_id = {}", refId);

    return accountMapper.fromAccountEntityToAccount(accountEntity);
  }

}
