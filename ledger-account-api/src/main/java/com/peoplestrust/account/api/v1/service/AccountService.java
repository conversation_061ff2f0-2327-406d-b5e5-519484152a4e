package com.peoplestrust.account.api.v1.service;

import com.peoplestrust.account.api.v1.model.Account;
import com.peoplestrust.account.domain.model.AccountStatus;
import com.peoplestrust.account.domain.model.RetrieveLedgerAccountBalanceResponse;
import com.peoplestrust.transaction.domain.model.BalanceSnapshotResponse;
import com.peoplestrust.util.api.common.exception.InvalidFieldException;
import com.peoplestrust.util.api.common.exception.ResourceNotFoundException;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Transaction service interface
 */
public interface AccountService {

  /**
   * Service for Phase 1 :: Create
   *
   * @param account
   * @param profileId
   * @param interactionId
   * @return
   * @throws Exception
   */
  Account createAccount(Account account, String profileId, String interactionId) throws Exception;

  /**
   * Update Account
   *
   * @param accountUpdateRequest
   * @param refId
   * @param profileId
   * @param profileIdAndAccountId
   * @return
   * @throws Exception
   */
  Account updateAccount(Account accountUpdateRequest, String refId, String profileId, String profileIdAndAccountId)
      throws Exception;

  /**
   * Get accounts given a profile ID
   *
   * @param profileId
   * @return
   * @throws Exception
   */
  public List<Account> getAccounts(String profileId, String interactionId) throws Exception;

  /**
   * Service to retrieve all the accounts
   *
   * @return
   * @throws Exception
   */
  public List<Account> loadAccounts() throws Exception;

  /**
   * Update an account's status
   *
   * @param refId
   * @param accountStatus
   * @param reason
   * @param profileId
   * @param profileIdAndAccountId
   * @return
   * @throws Exception
   */
  Optional<Account> updateAccountStatus(String refId, AccountStatus accountStatus, String reason, String profileId, String profileIdAndAccountId)
      throws Exception;

  /**
   * Service to retrieve account balance
   *
   * @param profileRefId  Unique ID that identifies the profile
   * @param accountRefId  Unique ID that identifies the account
   * @param interactionId Unique ID
   * @return
   * @throws ResourceNotFoundException
   * @throws InvalidFieldException
   */
  RetrieveLedgerAccountBalanceResponse retrieveLedgerAccountBalance(String profileRefId, String accountRefId, String interactionId)
      throws ResourceNotFoundException, InvalidFieldException;


  /**
   * Service to retrieve previous 48 snapshots
   *
   * @param profileRefId  unique profile identifier
   * @param accountRefId  unique account identifier
   * @param interactionId unique id
   * @return
   * @throws Exception
   */

  /**
   * Service to search previous snapshots
   *
   * @param profileRefId  unique profile identifier
   * @param accountRefId  unique account identifier
   * @param interactionId unique id
   * @param startTime        from dateTime
   * @param endTime          to dateTime
   * @param offset           offset is starting point of payments request filter; if offset if not provided it would be defaulted to zero
   * @param maxResponseItems Maximum number of response items to be returned. All items are returned if this field is absent
   * @return
   * @throws Exception
   */
  List<BalanceSnapshotResponse> searchBalanceSnapshots(String profileRefId, String accountRefId, String interactionId, LocalDateTime startTime, LocalDateTime endTime, Integer offset, Integer maxResponseItems)
          throws Exception;

  /**
   * Internal API::Retrieve account by primary key.
   *
   * @param refId
   * @param profileId
   * @return
   * @throws Exception
   */
  Account getAccountById(String refId, String profileId, String profileIdAndAccountId) throws Exception;

}

