-- ###############
-- # ENVIRONMENT #
-- ###############
-- # Based on env, set search path accordingly (uncomment one of the below)

-- # LOCAL
-- CREATE SCHEMA IF NOT EXISTS ledger_account
-- SET search_path to ledger_account;

-- # DEV 
-- SET search_path to account_dev;

-- # QAS
--  SET search_path to account_qa;


-- ######
-- TABLES
-- ######
DROP TABLE IF EXISTS account;
DROP TABLE IF EXISTS options;

-- INDEXES for ACCOUNT table
DROP INDEX IF EXISTS ix_ledger_account_account;
DROP INDEX IF EXISTS ix_ledger_account_profile;


-- #####
-- TYPES
-- #####
DROP TYPE IF EXISTS account_status_type;



