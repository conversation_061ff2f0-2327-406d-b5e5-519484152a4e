-- ###############
-- # ENVIRONMENT #
-- ###############
-- # Based on env, set search path accordingly (uncomment one of the below)

-- # LOCAL
-- CREATE SCHEMA IF NOT EXISTS ledger_account
-- SET search_path to ledger_account;

-- # DEV 
-- SET search_path to account_dev;

-- # QAS
--  SET search_path to account_qa;


-- #####
-- TYPES
-- #####
DO $$ BEGIN
	-- Creating ENUM for account::status
CREATE TYPE account_status_type AS ENUM
    ('ACTIVE', 'INACTIVE', 'SUSPENDED');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;


-- ######
-- TABLES
-- ######
	-- Creating ACCOUNT table
CREATE TABLE IF NOT EXISTS account (
	id integer NOT NULL  GENERATED ALWAYS AS IDENTITY,
	profile_id UUID NOT NULL,
	ref_id UUID UNIQUE NOT NULL,
	account_name varchar(50) NOT NULL,
	description varchar(255) NOT NULL,
	status account_status_type NOT NULL DEFAULT 'ACTIVE'::account_status_type,
	monetary_unit varchar(3) NOT NULL,
	reason varchar(240),
	created_date_time timestamp NOT NULL,
	updated_date_time timestamp NULL,
	options_id int2 ,
	CONSTRAINT fk_account_options FOREIGN KEY (options_id) REFERENCES options(id)
);

-- INDEXES for ACCOUNT table
CREATE INDEX IF NOT exists ix_account_account ON account USING btree (ref_id);
CREATE INDEX IF NOT exists ix_account_profile ON account USING btree (profile_id);


	-- Creating OPTIONS table
CREATE TABLE IF NOT EXISTS options (
	id integer NOT NULL GENERATED ALWAYS AS IDENTITY,
	overdraft_amount numeric(13,2)  DEFAULT 0.0,
	prefund_reserve_amount numeric(13,2) DEFAULT 0.0,
	fund_hold_days int2 DEFAULT 0,
	CONSTRAINT options_id PRIMARY KEY (id)
);


