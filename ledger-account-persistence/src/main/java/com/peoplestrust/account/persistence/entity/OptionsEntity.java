package com.peoplestrust.account.persistence.entity;

import java.math.BigDecimal;

import jakarta.persistence.*;
import jakarta.validation.constraints.Digits;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Data
@Entity
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Table(name = "options")
public class OptionsEntity {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id", unique = true, nullable = false)
  private Integer id;

  @Column(name = "fund_hold_days")
  private Integer fundHoldDays;

  @Column(name = "overdraft_amount")
  @Digits(integer = 13, fraction = 2)
  private BigDecimal overdraftAmount;

  @OneToOne(mappedBy = "options")
  private AccountEntity accountEntity;
}

