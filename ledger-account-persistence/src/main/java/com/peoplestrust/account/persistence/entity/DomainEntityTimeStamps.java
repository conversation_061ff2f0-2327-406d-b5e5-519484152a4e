package com.peoplestrust.account.persistence.entity;

import com.peoplestrust.account.persistence.utils.DateTimeGenerator;
import java.time.LocalDateTime;

import jakarta.persistence.Column;
import jakarta.persistence.MappedSuperclass;
import jakarta.persistence.PrePersist;
import jakarta.persistence.PreUpdate;
import lombok.Data;

@MappedSuperclass
@Data
public class DomainEntityTimeStamps {

  @Column(name = "created_date_time", nullable = false, length = 29)
  private LocalDateTime createdDateTime;

  @Column(name = "updated_date_time", length = 29)
  private LocalDateTime updatedDateTime;

  @PrePersist
  protected void onCreate() {
    if (this.createdDateTime == null) {
      this.createdDateTime = DateTimeGenerator.getCurrentTime();
    }
  }

  @PreUpdate
  protected void onUpdate() {
    this.updatedDateTime = DateTimeGenerator.getCurrentTime();
  }
}
