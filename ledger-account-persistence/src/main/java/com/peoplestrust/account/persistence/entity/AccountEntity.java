package com.peoplestrust.account.persistence.entity;

import java.util.UUID;

import io.hypersistence.utils.hibernate.type.basic.PostgreSQLEnumType;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Type;

@Data
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "account")
public class AccountEntity extends DomainEntityTimeStamps {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id", unique = true, nullable = false)
  private Integer id;

  @NotNull
  @Column(name = "profile_id", columnDefinition = "uuid")
  private UUID profileId;

  @GenericGenerator(name = "uuid", strategy = "uuid2")
  @GeneratedValue(generator = "uuid")
  @Column(name = "ref_id", unique = true, nullable = false, columnDefinition = "uuid")
  private UUID refId;

  @NotEmpty
  @NotBlank
  @Column(name = "account_name", nullable = false)
  @Size(min = 3, max = 50)
  private String name;

  @NotEmpty
  @NotBlank
  @Column(name = "description", nullable = false)
  @Size(min = 3, max = 255)
  private String description;

  @Enumerated(EnumType.STRING)
  @Column(name = "monetary_unit", nullable = false)
  private MonetaryUnit monetaryUnit;

  @Enumerated(EnumType.STRING)

  @Column(name = "status", nullable = false, columnDefinition = "account.account_status_type")
  @Type(PostgreSQLEnumType.class)
  private AccountStatus status;

  @Column(name = "reason")
  private String reason;

  @OneToOne(cascade = CascadeType.ALL)
  @JoinColumn(name = "options_id", referencedColumnName = "id")
  private OptionsEntity options;
}

