package com.peoplestrust.account.persistence.repository.write;

import com.peoplestrust.account.persistence.entity.AccountEntity;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface AccountRepository extends JpaRepository<AccountEntity, Integer> {

  Optional<AccountEntity> findByRefId(UUID refId);

  List<AccountEntity> findByProfileId(UUID profileId);

}
