# General Ledger Frontend Application

This project is a React-based frontend application for the General Ledger system, bootstrapped with [Create React App](https://github.com/facebook/create-react-app).

## 📋 Table of Contents
- [Technology Stack](#technology-stack)
- [Dependencies](#dependencies)
- [Getting Started](#getting-started)
- [Available Scripts](#available-scripts)
- [Deployment](#deployment)
- [Security & Vulnerabilities](#security--vulnerabilities)

## 🛠 Technology Stack

- **React**: 19.1.0 (Latest)
- **Node.js**: 22.x
- **Material-UI (MUI)**: v7.2.0
- **TypeScript**: Configured via jsconfig.json
- **Build Tool**: Create React App 5.0.1

## 📦 Dependencies

### Core Framework & Libraries
| Package | Version | Purpose | Last Updated |
|---------|---------|---------|--------------|
| `react` | ^19.1.0 | Core React library | PR #798 (July 2025) |
| `react-dom` | ^19.1.0 | React DOM rendering | PR #798 (July 2025) |
| `react-scripts` | 5.0.1 | Build scripts and configuration | PR #798 (July 2025) |
| `react-router-dom` | ^7.7.0 | Client-side routing | PR #798 (July 2025) |

### UI Components & Styling
| Package | Version | Purpose | Last Updated |
|---------|---------|---------|--------------|
| `@mui/material` | ^7.2.0 | Material-UI core components | PR #798 (July 2025) |
| `@mui/icons-material` | ^7.2.0 | Material-UI icons | PR #798 (July 2025) |
| `@mui/lab` | 7.0.0-beta.14 | Experimental MUI components | PR #798 (July 2025) |
| `@mui/x-data-grid` | ^8.9.1 | Advanced data grid component | PR #798 (July 2025) |
| `@mui/x-date-pickers` | ^8.9.0 | Date/time picker components | PR #798 (July 2025) |
| `@emotion/react` | ^11.14.0 | CSS-in-JS library for MUI | PR #798 (July 2025) |
| `@emotion/styled` | ^11.14.1 | Styled components for Emotion | PR #798 (July 2025) |
| `styled-components` | ^6.1.19 | CSS-in-JS styling library | PR #798 (July 2025) |

### Icons & Graphics
| Package | Version | Purpose | Last Updated |
|---------|---------|---------|--------------|
| `@fortawesome/fontawesome-svg-core` | ^6.7.2 | FontAwesome core | PR #798 (July 2025) |
| `@fortawesome/free-solid-svg-icons` | ^6.7.2 | FontAwesome solid icons | PR #798 (July 2025) |
| `@fortawesome/react-fontawesome` | ^0.2.2 | FontAwesome React components | PR #798 (July 2025) |

### Forms & Validation
| Package | Version | Purpose | Last Updated |
|---------|---------|---------|--------------|
| `formik` | ^2.4.6 | Form library for React | PR #798 (July 2025) |
| `yup` | ^1.6.1 | Schema validation library | PR #798 (July 2025) |

### Date & Time Handling
| Package | Version | Purpose | Last Updated |
|---------|---------|---------|--------------|
| `date-fns` | ^4.1.0 | Modern date utility library | PR #798 (July 2025) |
| `@date-io/date-fns` | ^3.2.1 | Date-fns adapter for MUI | PR #798 (July 2025) |
| `moment` | ^2.30.1 | Date manipulation library | PR #798 (July 2025) |
| `moment-timezone` | ^0.6.0 | Timezone support for Moment.js | PR #798 (July 2025) |

### HTTP & API
| Package | Version | Purpose | Last Updated |
|---------|---------|---------|--------------|
| `axios` | ^1.10.0 | HTTP client library | PR #798 (July 2025) |
| `react-query` | ^3.39.3 | Data fetching and caching | PR #798 (July 2025) |
| `qs` | ^6.14.0 | Query string parsing | PR #798 (July 2025) |

### Authentication & Security
| Package | Version | Purpose | Last Updated |
|---------|---------|---------|--------------|
| `amazon-cognito-auth-js` | ^1.3.3 | AWS Cognito authentication | PR #798 (July 2025) |
| `jwt-decode` | ^4.0.0 | JWT token decoding | PR #798 (July 2025) |

### Testing
| Package | Version | Purpose | Last Updated |
|---------|---------|---------|--------------|
| `@testing-library/react` | ^16.3.0 | React testing utilities | PR #798 (July 2025) |
| `@testing-library/jest-dom` | ^6.6.3 | Custom Jest matchers | PR #798 (July 2025) |
| `@testing-library/dom` | ^10.4.0 | DOM testing utilities | PR #798 (July 2025) |
| `@testing-library/user-event` | ^14.6.1 | User interaction simulation | PR #798 (July 2025) |
| `@testing-library/react-hooks` | ^8.0.1 | React hooks testing | PR #798 (July 2025) |
| `msw` | ^1.2.1 | Mock Service Worker for API mocking | PR #798 (July 2025) |

### Utilities
| Package | Version | Purpose | Last Updated |
|---------|---------|---------|--------------|
| `lodash` | ^4.17.21 | Utility library | PR #798 (July 2025) |
| `uuid` | ^11.1.0 | UUID generation | PR #798 (July 2025) |
| `react-toastify` | 11.0.5 | Toast notifications | PR #798 (July 2025) |
| `use-react-router-breadcrumbs` | ^4.0.1 | Breadcrumb navigation | PR #798 (July 2025) |
| `web-vitals` | ^5.0.3 | Web performance metrics | PR #798 (July 2025) |

### Build & Environment
| Package | Version | Purpose | Last Updated |
|---------|---------|---------|--------------|
| `env-cmd` | ^10.1.0 | Environment-specific commands | PR #798 (July 2025) |
| `buffer` | ^6.0.3 | Node.js Buffer polyfill | PR #798 (July 2025) |
| `crypto-browserify` | ^3.12.1 | Crypto polyfill for browsers | PR #798 (July 2025) |
| `stream` | ^0.0.3 | Stream polyfill | PR #798 (July 2025) |
| `util` | ^0.12.5 | Node.js util polyfill | PR #798 (July 2025) |

## 🚀 Getting Started

### Prerequisites
- Node.js 22.x
- npm or yarn package manager
- AWS CLI (for deployment)

### Installation
```bash
npm install
```

### Environment Setup
The application supports multiple environments with corresponding configuration files:
- `.env` - Default environment
- `.env.dev` - Development environment
- `.env.qa` - QA environment
- `.env.staging` - Staging environment
- `.env.prod` - Production environment

#### Session Timeout Configuration
The application includes configurable session timeout settings to automatically log out inactive users:

| Environment Variable | Default | Description |
|---------------------|---------|-------------|
| `VITE_APP_INACTIVITY_TIMEOUT_MINUTES` | 45 | Minutes of inactivity before automatic logout |
| `VITE_APP_MAX_SESSION_AGE_MINUTES` | 480 | Maximum session duration (8 hours) regardless of activity |
| `VITE_APP_TOKEN_REFRESH_BUFFER_MINUTES` | 5 | Minutes before token expiry to attempt refresh |
| `VITE_APP_SESSION_DEBUG_LOGGING` | false | Enable debug logging for session management |

**Example configuration:**
```bash
# 30-minute inactivity timeout
VITE_APP_INACTIVITY_TIMEOUT_MINUTES=30

# 4-hour maximum session
VITE_APP_MAX_SESSION_AGE_MINUTES=240

# Enable debug logging
VITE_APP_SESSION_DEBUG_LOGGING=true
```


##### Using Yarn:
Prerequisite: yarn, aws cli
1. yarn build:<"env">
2. Load AWS credentials \
   export AWS_ACCESS_KEY_ID=""\
   export AWS_SECRET_ACCESS_KEY=""\
   export AWS_SESSION_TOKEN=""
3. Sync with the S3 bucket and delete old objects\
   aws --region ca-central-1 s3 sync ./build s3://<Bucket-Name> --delete

Example:\
yarn build:qa\
Load aws credentials\
aws --region ca-central-1 s3 sync ./build s3://gl-qa-799455639446 --delete
## 📜 Avail
able Scripts

### Development
```bash
npm start                 # Run in development mode (localhost:3000)
npm run start:qa         # Run with QA environment variables
```

### Building
```bash
npm run build            # Production build
npm run build:dev        # Build with DEV environment
npm run build:qa         # Build with QA environment
npm run build:staging    # Build with staging environment  
npm run build:prod       # Build with production environment
```

### Testing
```bash
npm test                 # Run tests in interactive watch mode
```

### Other
```bash
npm run eject           # Eject from Create React App (irreversible)
```

## 🚀 Deployment

### Using npm:
1. Build the application:
   ```bash
   npm run build:<env>
   ```

2. Configure AWS credentials:
   ```bash
   export AWS_ACCESS_KEY_ID="your-access-key"
   export AWS_SECRET_ACCESS_KEY="your-secret-key"
   export AWS_SESSION_TOKEN="your-session-token"
   ```

3. Deploy to S3:
   ```bash
   aws --region ca-central-1 s3 sync ./build s3://<bucket-name> --delete
   ```

### Example QA Deployment:
```bash
npm run build:qa
# Configure AWS credentials
aws --region ca-central-1 s3 sync ./build s3://gl-qa-799455639446 --delete
```

## 🔒 Security & Vulnerabilities

### Recent Security Updates (PR #798 - July 2025)
All npm packages have been updated to their latest versions to address security vulnerabilities identified by Veracode's Software Composition Analysis (SCA).

### Key Security Improvements:
- **React 19.1.0**: Latest stable version with security patches
- **Axios 1.10.0**: Updated HTTP client with security fixes
- **Material-UI 7.2.0**: Latest version addressing known vulnerabilities
- **Testing Libraries**: All testing dependencies updated to latest secure versions

### Known Issues & Considerations:
- Some Material-UI packages may have compatibility issues with React 19
- Legacy packages that are no longer maintained should be considered for replacement
- Regular dependency audits should be performed using `npm audit`

### Recommended Maintenance Schedule:
- **Monthly**: Run `npm audit` to check for new vulnerabilities
- **Quarterly**: Review and update dependencies to latest stable versions
- **Annually**: Evaluate and replace deprecated or unmaintained packages

## 🔧 Configuration Notes

### Babel Configuration
The project includes overrides for `@babel/core` to ensure compatibility:
```json
{
  "resolutions": {
    "@babel/core": "^7.22.0"
  },
  "overrides": {
    "@babel/core": "^7.22.0"
  }
}
```

### Node.js Version
- **Required**: Node.js 22.x
- **Recommended**: Use nvm or similar tool to manage Node.js versions

## 📚 Additional Resources

- [Create React App Documentation](https://facebook.github.io/create-react-app/docs/getting-started)
- [React Documentation](https://reactjs.org/)
- [Material-UI Documentation](https://mui.com/)
- [React Router Documentation](https://reactrouter.com/)

## 🤝 Contributing

When updating dependencies:
1. Test thoroughly in development environment
2. Run full test suite: `npm test`
3. Build for all environments to ensure compatibility
4. Update this README with new version information
5. Document any breaking changes or migration steps

---

**Last Updated**: July 2025 (PR #798)  
**Maintainer**: Development Team  
**Node.js Version**: 22.x  
**React Version**: 19.1.0