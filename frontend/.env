VITE_APP_LOG_LEVEL='DEBUG'
VITE_APP_API_URL='https://ledger-qas-api.peoplescloud.io/admin-ui/v1/ledger/'
VITE_APP_TRANSACTION_URL='https://ledger-qas-api.peoplescloud.io/v1/internal/ledger/'
VITE_APP_FAKE_API_URL='http://localhost:3001/'
# COGNITO
VITE_APP_ClientId='2nnbfpn3pctvaknqgtus2hiqfv'
VITE_APP_AppWebDomain='internal-user-pool-test.auth.us-east-2.amazoncognito.com'
VITE_APP_RedirectUriSignIn='http://localhost:3000'
VITE_APP_RedirectUriSignOut='http://localhost:3000'
VITE_APP_IdentityProvider=''
VITE_APP_UserPoolId='us-east-2_oH2B5QIUo'
VITE_APP_TokenScopesArray='email,openid'

VITE_APP_BYPASS_HEALTH_CALLS=true

# Session timeout configuration (values in minutes)
# Inactivity timeout: logout if user is inactive for this duration (default: 45 minutes)
VITE_APP_INACTIVITY_TIMEOUT_MINUTES=45
# Maximum session age: logout regardless of activity after this duration (default: 480 minutes = 8 hours)
VITE_APP_MAX_SESSION_AGE_MINUTES=480
# Token refresh buffer: refresh token this many minutes before expiry (default: 5 minutes)
VITE_APP_TOKEN_REFRESH_BUFFER_MINUTES=5
# Enable debug logging for session management (set to 'true' to enable)
VITE_APP_SESSION_DEBUG_LOGGING=false

SKIP_PREFLIGHT_CHECK=true