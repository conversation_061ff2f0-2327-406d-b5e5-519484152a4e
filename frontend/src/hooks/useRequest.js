import { useEffect, useState } from "react";
import _ from "lodash";

const useRequest = (defaultData) => {
  const [loading, setLoading] = useState(false);
  const [totalNumber, setTotalNumber] = useState(1);
  const [data, setData] = useState({
    page: 0,
    limit: 25,
    search: null,
    ...defaultData,
  });

  const onPaginationModel = (pagination) => {
    setData({
      ...data,
      page: pagination.page,
      limit: pagination.pageSize,
    });
  };

  const onSearch = _.debounce((e) => {
    const { value } = e.target;

    if (data?.page !== 1) {
      setData({
        ...data,
        page: 1,
      });
    }

    setData({
      ...data,
      search: value,
    });

    setTotalNumber(1);
  }, 400);

  const onFilter = (filters) => {
    setLoading(true);
    setData({
      ...data,
      page: 0,
      search: filters,
    });
  };

  const calculateTotal = (hasMore) => {
    if (hasMore) {
      setTotalNumber(data.page + 2);
    } else {
      setTotalNumber(1);
    }
  };

  const resetFilter = () => {
    setTotalNumber(1);
    setData({
      ...data,
      page: 0,
      search: null,
    });
  };

  return {
    data,
    loading,
    setLoading,
    totalNumber,
    calculateTotal,
    resetFilter,
    actions: {
      onPaginationModel,
      onFilter,
      onSearch,
    },
  };
};

export default useRequest;
