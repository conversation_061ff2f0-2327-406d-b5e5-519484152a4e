import { useQuery } from "react-query";
import { getMetadata } from "@/services/transaction";
import baseQueryOptions from "@/constants/baseQueryOptions";

const useGetMetadata = (
  profileId,
  accountId,
  instructionRefId,
  transactionRefId
) => {
  const queryKey = [
    "getMetadata",
    profileId,
    accountId,
    instructionRefId,
    transactionRefId,
  ];

  const { data, isLoading, error, refetch } = useQuery(
    queryKey,
    () =>
      getMetadata({ profileId, accountId, instructionRefId, transactionRefId }),
    {
      ...baseQueryOptions,
      enabled: !!profileId && !!instructionRefId && !!transactionRefId, // Ensure all required params are available
    },
    {
      // Ensure that refetching is enabled
      refetchOnMount: false, // We handle refetching manually
      refetchOnWindowFocus: false,
    }
  );

  return { data, isLoading, error, refetch };
};

export default useGetMetadata;
