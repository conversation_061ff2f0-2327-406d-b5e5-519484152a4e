import { useQuery } from "react-query";
import baseQueryOptions from "@/constants/baseQueryOptions";
import { getTransactionById } from "@/services/transaction";

const useGetTransactionById = (props) => {
  const data = useQuery(
    ["getTransactionById", props],
    () => getTransactionById(props),
    {
      keepPreviousData: false,
      ...baseQueryOptions,
    }
  );

  return data;
};

export default useGetTransactionById;
