import { useMutation } from 'react-query'
import queryClient from '@/services/client/queryClient'
import alertMessages from '@/constants/alertMessages'
import { toast } from 'react-toastify'
import { createProfileService } from '@/services/profile'

const useCreateProfile = () => {
    const mutation = useMutation(createProfileService, {
        onSuccess: async () => {
            toast.success(alertMessages.profileCreation)
            await queryClient.refetchQueries(['getProfiles'], { active: true })
        }
    })
    
    return mutation

}

export default useCreateProfile