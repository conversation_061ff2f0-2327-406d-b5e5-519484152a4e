import { useMutation } from 'react-query'
import queryClient from '@/services/client/queryClient'
import alertMessages from '@/constants/alertMessages'
import { toast } from 'react-toastify'
import { updateProfileService } from '@/services/profile'

const useUpdateProfile = () => {
    const mutation = useMutation(updateProfileService, {
        onSuccess: async () => {
            toast.success(alertMessages.profileUpdate)
            await queryClient.refetchQueries(['getProfileById'], { active: true })
        }
    })
    
    return mutation

}

export default useUpdateProfile