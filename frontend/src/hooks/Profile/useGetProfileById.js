import {
    useQuery
} from 'react-query'
import baseQueryOptions from '@/constants/baseQueryOptions'
import { getProfileByIdService } from '@/services/profile'

const useGetProfileById = (id) => {
    const { data } = useQuery(['getProfileById', id], () => getProfileByIdService(id), {
        keepPreviousData: true,
        enabled: !!id,
        ...baseQueryOptions
    })

    return data

}

export default useGetProfileById