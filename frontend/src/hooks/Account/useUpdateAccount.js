import { useMutation } from 'react-query'
import queryClient from '@/services/client/queryClient'
import alertMessages from '@/constants/alertMessages'
import { toast } from 'react-toastify'
import { updateAccountService } from '@/services/account'

const useUpdateAccount = () => {
    const mutation = useMutation(updateAccountService, {
        onSuccess: async () => {
            toast.success(alertMessages.accountUpdate)
            await queryClient.refetchQueries(['getAccountById'], { active: true })
        }
    })
    
    return mutation

}

export default useUpdateAccount