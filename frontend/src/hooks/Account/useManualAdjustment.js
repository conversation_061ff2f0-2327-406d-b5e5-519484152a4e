import { useMutation } from 'react-query'
import queryClient from '@/services/client/queryClient'
import { manualAdjustmentTransaction } from '@/services/transaction'
import { toast } from 'react-toastify'
import alertMessages from '@/constants/alertMessages'

const onSuccessAction = async () => {
    toast.success(alertMessages.manualCorrection)
    await queryClient.refetchQueries(['getAccountBalance'], { active: true })
}

const useManualAdjustment = (onSuccess = onSuccessAction) => {
    const mutation = useMutation(manualAdjustmentTransaction, {
        onSuccess: (data, variables) => {
            onSuccess(data, variables)
        }
    })
    return mutation

}

export default useManualAdjustment