import { useMutation } from 'react-query'
import queryClient from '@/services/client/queryClient'
import { prefundTransaction } from '@/services/transaction'
import { toast } from 'react-toastify'
import alertMessages from '@/constants/alertMessages'

const usePrefundTransaction = () => {
    const mutation = useMutation(prefundTransaction, {
        onSuccess: async () => {
            toast.success(alertMessages.adjustmentSucceeded)
            await queryClient.refetchQueries(['getAccountBalance'], { active: true })
        }
    })
    
    return mutation

}

export default usePrefundTransaction