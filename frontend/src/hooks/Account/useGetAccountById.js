import {
    useQuery
} from 'react-query'
import baseQueryOptions from '@/constants/baseQueryOptions'
import { getAccountsServiceById } from '@/services/account'

const useGetAccountById = (profileId, id) => {
    const { data } = useQuery(['getAccountById', profileId, id], () => getAccountsServiceById(profileId, id), {
        keepPreviousData: true,
        enabled: !!id || !!profileId,
        ...baseQueryOptions
    })

    return data

}

export default useGetAccountById
