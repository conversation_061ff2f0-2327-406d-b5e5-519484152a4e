import {
    useQuery
} from 'react-query'
import baseQueryOptions from '@/constants/baseQueryOptions'
import { getBalanceSnapshotHealth } from '@/services/account'

const useGetBalanceSnapshotHealth = (isSignedIn) => {
    const { data } = useQuery(['getBalanceSnapshotHealth'], () => getBalanceSnapshotHealth(), {
        ...baseQueryOptions,
        enabled: !!isSignedIn
    })

    return data
}

export default useGetBalanceSnapshotHealth
