import {
    useQuery
} from 'react-query'
import baseQueryOptions from '@/constants/baseQueryOptions'
import { getInternalHealth } from '@/services/account'

const useGetInternalHealth = (isSignedIn) => {
    const { data } = useQuery(['getInternalHealth'], () => getInternalHealth(), {
        ...baseQueryOptions,
        enabled: !!isSignedIn
    })

    if (data?.length > 0) {
        return data?.[0]?.totalInternal_ErrorCount
    }

    return !!data && data?.length === 0
}

export default useGetInternalHealth
