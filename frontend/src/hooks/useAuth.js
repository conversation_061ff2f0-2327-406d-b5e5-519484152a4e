// hooks/useAuth.js or useAuth.ts
import { useEffect, useState } from 'react'
import { Auth } from '@/services/client/cognito'
import { jwtDecode } from 'jwt-decode'
import SESSION_CONFIG from '@/constants/sessionConfig'


export const logout = async () => {
  try {
    await Auth.signOut({ global: true })
    window.history.replaceState('', document.title, '/')
    window.location.reload()
  } catch (err) {
    console.error('Error signing out:', err)
  }
}

const isAllowed = (userGroup, userGroups = []) => {
  if (!userGroup) return true
  if (!userGroups.length) return false

  if (typeof userGroup === 'string') {
    return userGroups.includes(userGroup)
  }

  return userGroup.some(group => userGroups.includes(group))
}

const login = async () => {
   try {
    await Auth.signInWithRedirect({
      provider: "Cognito",
    })
  } catch (error) {
    console.error("Error signing in:", error)
  }
}

const parseUserSession = async () => {
  try {
    // const user = await Auth.currentAuthenticatedUser()
    const session = await Auth.fetchAuthSession()
    const isExpired = session.tokens?.accessToken?.isExpired
    if (!session.tokens) {
      console.warn('No valid session found')
      return null
    }
    if (isExpired) {
      logout()
      return null
    }
    
    // Additional check: prevent session from lasting too long (only for very old sessions)
    const accessToken = session.tokens?.accessToken?.toString()
    if (accessToken) {
      const decoded = jwtDecode(accessToken)
      const tokenIssuedAt = decoded.iat * 1000 // Convert to milliseconds
      const maxSessionAge = SESSION_CONFIG.MAX_SESSION_AGE_MS
      const isSessionTooOld = (Date.now() - tokenIssuedAt) > maxSessionAge

      if (isSessionTooOld) {
        if (SESSION_CONFIG.ENABLE_DEBUG_LOGGING) {
          console.warn(`Session too old (${SESSION_CONFIG.MAX_SESSION_AGE_MINUTES} minutes), logging out user`)
        }
        logout()
        return null
      }
    }
    
    const jwtIdToken = session.tokens?.idToken?.toString()
    const decoded = jwtDecode(jwtIdToken)


    const {
      'cognito:username': username = '',
      'cognito:groups': cognitoGroups = [],
      given_name,
      family_name,
      email,
    } = decoded

    return {
      username,
      firstName: given_name,
      lastName: family_name,
      email,
      cognitoGroups,
    }
  } catch (err) {
    console.error('User not authenticated:', err)
    return null
  }
}


const useAuth = () => {
  const [isSignedIn, setIsSignedIn] = useState(false)
  const [user, setUser] = useState(null)

  const userSessionUpdate = async () => {
    const session = await parseUserSession()
    setUser(session)
    setIsSignedIn(!!session)
  }

  useEffect(() => {
    userSessionUpdate()
  }, [])

  return {
    isSignedIn,
    user,
    isAllowed: (group) => isAllowed(group, user?.cognitoGroups || []),
    login,
    logout,
    userSessionUpdate,
  }
}

export default useAuth
