import useGetProfiles from "@/hooks/Profile/useGetProfiles";
import useGetAccount from "./Account/useGetAccount";

const accountsRoute = (id) => {
  const data = useGetAccount(id);

  return data?.map((item) => ({
    to: `/profiles/${item?.id}/${item.ref_id}`,
    path: "/profiles/:profileId/:accountId",
    name: item?.legal_name,
  }));
};
const profilesRoute = () => {
  const data = useGetProfiles();

  return data?.map((item) => ({
    to: `/profiles/${item?.ref_id}`,
    path: "/profiles/:profileId",
    name: item?.legal_name,
    submenu: accountsRoute(ref_id),
  }));
};

const useRoutes = () => {
  reutn[
    {
      to: "/profiles",
      path: "/profiles",
      name: "Profiles",
      submenu: profiles?.map((item) => ({
        to: `/profiles/${item?.ref_id}`,
        path: "/profiles/:profileId",
        name: item?.legal_name,
        submenu: item.accounts
          ? item.accounts.map((acc) => ({
              to: `/profiles/${item.path}/${acc.path}`,
              path: "/profiles/:profileId/:accountId",
              name: acc.name,
            }))
          : null,
      })),
    }
  ];
};

export default useRoutes;
