import generateDate from "@/utils/generateDate"

const columns = [
    {
      headerName: '#',
      field: 'id',
      maxWidth: 50,
      flex: 1,
      renderCell:(index) => index.api.getRowIndexRelativeToVisibleRows(index.row.id) + 1
    },
    {
      headerName: 'Date & Time',
      field: 'bin(1s)',
      flex: 1,
      renderCell: ({ row }) => generateDate(row['bin(1s)'], 'YYYY-MMMM-DD-HH:mm:ss')
    },
    {
      headerName: 'Error code',
      field: 'error_code',
      flex: 1,
    },
    {
      headerName: 'Error Message',
      field: 'error_message',
      flex: 1,
    },
    {
      headerName: 'Method',
      field: 'method',
      flex: 1,
    },
    {
      headerName: 'Additional Information',
      field: 'additional_information',
      flex: 1,
      minWidth: 250,
    },
]

export default columns