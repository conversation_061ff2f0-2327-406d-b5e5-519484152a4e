import moment from "moment-timezone";
import amountFormat from "@/utils/amountFormat";

const columns = [
  {
    headerName: "#",
    field: "id",
    flex: 1,
    maxWidth: 50,
    sortable: false,
    filterable: false,
    renderCell: (index) =>
      index.api.getRowIndexRelativeToVisibleRows(index.row.transaction_ref_id) + 1,
  },
  {
    headerName: "Transaction Reference ID",
    field: "transaction_ref_id",
    minWidth: 300,
    flex: 1,
  },
  {
    headerName: "Acceptance Date",
    field: "acceptance_date_time",
    flex: 1,
    renderCell: ({ row }) => {
      if (!row.acceptance_date_time) return "N/A";
      const formatted = moment(row.acceptance_date_time)
        .tz("America/New_York")
        .format("DD MMMM YYYY");
      return formatted;
    },
  },
  {
    headerName: "Effective Date",
    field: "effective_date_time",
    flex: 1,
    renderCell: ({ row }) => {
      if (!row.effective_date_time) return "N/A";
      const formatted = moment(row.effective_date_time)
        .tz("America/New_York")
        .format("DD MMMM YYYY");
      return formatted;
    },
  },
  {
    headerName: "Transaction Flow",
    field: "transaction_flow",
    flex: 1,
  },
  {
    headerName: "Payment Category",
    field: "payment_category",
    flex: 1,
  },
  {
    headerName: "Amount",
    field: "amount",
    flex: 1,
    renderCell: ({ row }) =>
      `${amountFormat(row.amount)} ${row.monetary_unit}`,
  },
  {
    headerName: "Status",
    field: "status",
    flex: 1,
  },
];

export default columns;
