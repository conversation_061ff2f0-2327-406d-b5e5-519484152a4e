import generateDate from "@/utils/generateDate"

const columns = [
    {
      headerName: '#',
      field: 'id',
      maxWidth: 50,
      flex: 1,
      renderCell:(index) => index.api.getRowIndexRelativeToVisibleRows(index.row.id) + 1
    },
    {
      headerName: 'Date & Time',
      field: 'bin(60s)',
      flex: 1,
      renderCell: ({ row }) => generateDate(row['bin(60s)'], 'YYYY-MMMM-DD-HH:mm:ss')
    },
    {
        headerName: 'Average response time (ms)',
        field: 'average_response_time',
        flex: 1,
    },
    {
        headerName: 'Minimum response time (ms)',
        field: 'minimum_response_time',
        flex: 1,
    },
    {
        headerName: 'Maximum response time (ms)',
        field: 'maximum_response_time',
        flex: 1,
    }
]

export default columns