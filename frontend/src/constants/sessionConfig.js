// Session timeout configuration
// All values are in minutes and will be converted to milliseconds internally

const sessionConfig = {
  // Inactivity timeout: logout if user is inactive for this duration
  // Default: 45 minutes
  inactivityTimeoutMinutes: parseInt(import.meta.env.VITE_APP_INACTIVITY_TIMEOUT_MINUTES) || 45,
  
  // Maximum session age: logout regardless of activity after this duration
  // Default: 8 hours (480 minutes)
  maxSessionAgeMinutes: parseInt(import.meta.env.VITE_APP_MAX_SESSION_AGE_MINUTES) || 480,
  
  // Token refresh buffer: refresh token this many minutes before expiry
  // Default: 5 minutes
  tokenRefreshBufferMinutes: parseInt(import.meta.env.VITE_APP_TOKEN_REFRESH_BUFFER_MINUTES) || 5,
  
  // Enable debug logging for session management
  // Default: false (set to 'true' string to enable)
  enableDebugLogging: import.meta.env.VITE_APP_SESSION_DEBUG_LOGGING === 'true'
}

// Convert minutes to milliseconds for internal use
export const SESSION_CONFIG = {
  INACTIVITY_TIMEOUT_MS: sessionConfig.inactivityTimeoutMinutes * 60 * 1000,
  MAX_SESSION_AGE_MS: sessionConfig.maxSessionAgeMinutes * 60 * 1000,
  TOKEN_REFRESH_BUFFER_MS: sessionConfig.tokenRefreshBufferMinutes * 60 * 1000,
  ENABLE_DEBUG_LOGGING: sessionConfig.enableDebugLogging,
  
  // Expose original minute values for logging/debugging
  INACTIVITY_TIMEOUT_MINUTES: sessionConfig.inactivityTimeoutMinutes,
  MAX_SESSION_AGE_MINUTES: sessionConfig.maxSessionAgeMinutes,
  TOKEN_REFRESH_BUFFER_MINUTES: sessionConfig.tokenRefreshBufferMinutes
}

export default SESSION_CONFIG
