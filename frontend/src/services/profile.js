import { customInstance } from './client/axiosInstance'
import _ from 'lodash'

export const getProfilesService = async () => {
    const response = await customInstance({ 
        url: 'profile', 
        method: 'GET',
    });

    const data = response?.data?.profiles || []

    return _.take(data, 250)
}

export const searchProfilesService = async (name) => {
    const response = await customInstance({ 
        url: `profile/search?profile-name=${name}`, 
        method: 'GET',
    });

    return response.data
}

export const getProfileByIdService = async (id) => {
    const response = await customInstance({ 
        url: `profile/${id}`, 
        method: 'GET',
    });

    return response.data
}

export const createProfileService = async (data) => {
    const response = await customInstance({ 
        url: `profile`, 
        method: 'POST',
        data,
    });

    return response.data
}

export const updateProfileService = async ({ id, status, reason, ...data }) => {
    const response = await customInstance({ 
        url: `profile/${id}`,
        data,
        method: 'PUT',
    });
    await customInstance({ 
        url: `profile/${id}/status`,
        data: {
            status,
            reason
        },
        method: 'PATCH',
    });
    return response.data
}