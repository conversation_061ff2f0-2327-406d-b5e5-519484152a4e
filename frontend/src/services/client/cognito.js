import { Amplify } from 'aws-amplify'
import * as Auth from '@aws-amplify/auth'
const amplifyConfig = {
  Auth: {
    Cognito: {
      region: import.meta.env.VITE_APP_AWS_REGION,
      userPoolId: import.meta.env.VITE_APP_UserPoolId,
      userPoolClientId: import.meta.env.VITE_APP_ClientId,
      loginWith: {
        oauth: {
          domain: import.meta.env.VITE_APP_AppWebDomain,
          scopes: import.meta.env.VITE_APP_TokenScopesArray?.split(",") || ["email", "profile", "openid"],
          redirectSignIn: [import.meta.env.VITE_APP_RedirectUriSignIn || "http://localhost:5173/"],
          redirectSignOut: [import.meta.env.VITE_APP_RedirectUriSignOut || "http://localhost:5173/"],
          responseType: "code",
        }
      },
    },
  },
}

// Additional validation to ensure arrays
const config = amplifyConfig.Auth.Cognito.loginWith.oauth

// Ensure redirectSignIn is an array
if (config.redirectSignIn && !Array.isArray(config.redirectSignIn)) {
  config.redirectSignIn = [config.redirectSignIn]
}

// Ensure redirectSignOut is an array
if (config.redirectSignOut && !Array.isArray(config.redirectSignOut)) {
  config.redirectSignOut = [config.redirectSignOut]
}

// Ensure scopes is an array
if (config.scopes && !Array.isArray(config.scopes)) {
  config.scopes = [config.scopes]
}

Amplify.configure(amplifyConfig)


export { Auth }