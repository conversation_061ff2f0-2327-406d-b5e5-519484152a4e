import { v4 as uuidv4 } from 'uuid'
import { logout } from '@/hooks/useAuth'
import { Auth } from '@/services/client/cognito'
import alertMessages from '@/constants/alertMessages'
import { toast } from 'react-toastify'
import Axios from 'axios';

const AUTHORIZATION  = 'Authorization';
const APPLICATION_JSON = 'application/json';
const ACCEPT_HEADER = 'Accept';
const CONTENT_TYPE_HEADER = 'Content-Type';

// backend required headers
const X_PG_INTERACTION_ID_HEADER = 'x-pg-interaction-id';
const X_PG_INTERACTION_TIMESTAMP_HEADER = 'x-pg-interaction-timestamp';

export const AXIOS_INSTANCE = Axios.create({
	baseURL: import.meta.env.VITE_APP_API_URL,
	headers: {
		[ACCEPT_HEADER]: APPLICATION_JSON,
		[CONTENT_TYPE_HEADER]: APPLICATION_JSON,
	},
	timeout: 15000,
	withCredentials: false,
});

AXIOS_INSTANCE.interceptors.request.use(async (config) => {
  const configWithCredentialsAndHeaders = await injectRequiredHeaders(config);
  return configWithCredentialsAndHeaders;
});

AXIOS_INSTANCE.interceptors.response.use(
  response => {
    // Reset session timeout on successful API calls
    // This ensures active users don't get logged out while using the app
    if (window.resetSessionTimeouts) {
      window.resetSessionTimeouts()
    }
    return response
  },
  err => {
    const error = err.response;


    if (error && error.status === 401) {
      console.log(error.data.message)

      toast.error(error.data.message || 'Unauthorized Request detected!', {
        autoClose: 1500,
        onClose: logout,
      });
    } else if (error && error.status && error.status !== 401) {
      toast.error(error?.data?.error?.additional_information);
    } else {
      toast.error(alertMessages.error);
    }

    return Promise.reject(err); // Important!
  }
);

const injectRequiredHeaders = async (config) => {
  const session = await Auth.fetchAuthSession()
  const jwtToken = session.tokens?.accessToken?.toString()
  config.headers[AUTHORIZATION] = `Bearer ${jwtToken}`;
  config.headers[X_PG_INTERACTION_ID_HEADER] = uuidv4();
  config.headers[X_PG_INTERACTION_TIMESTAMP_HEADER] = new Date().toISOString();

  return config;
};

export const customInstance = (config, options) => {
	const controller = new AbortController();

	const customOptions = {
		...options,
		paramsSerializer: { indexes: null },
	};

	const promise = AXIOS_INSTANCE({
		...config,
		...customOptions,
		signal: controller.signal,
	}).then((data) => data);

	promise.cancel = () => {
		controller.abort('Query was cancelled');
	};

	return promise;
};