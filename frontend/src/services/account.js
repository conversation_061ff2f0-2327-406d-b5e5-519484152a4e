import { customInstance } from "./client/axiosInstance";
import moment from "moment";
import generateQueryString from "@/utils/generateQueryString";
import _ from "lodash";

export const getAccountsService = async (id) => {
  const response = await customInstance({ 
    url: 'account',
    method: 'GET',
    headers: { "x-pg-profile-id": id }
  });

  const data = response?.data?.accounts || [];
  return _.take(data, 100);
};

export const getAccountsServiceById = async (profileId, id) => {
  const response = await customInstance({ 
    url: `account/${id}`,
    method: 'GET',
    headers: { "x-pg-profile-id": profileId }
  });
  return response?.data;
};

export const getAccountBalance = async (profileId, id) => {
  const response = await customInstance({ 
    url: `account/${id}/balance`,
    method: 'GET',
    headers: { "x-pg-profile-id": profileId }
  });
  return response?.data;
};

export const getAccountBalanceSnapshots = async (data) => {
  const { profileId, accountId, from_date, to_date, ...rest } =
    data?.search || {};
  const search = generateQueryString(rest);

  // Validate required parameters
  if (!profileId || !accountId) {
    return { snapshots: [] };
  }

  // Parse ET inputs (handle YYYY-MM-DD:HH:mm)
  const start = from_date
    ? moment.tz(from_date, "YYYY-MM-DD:HH:mm", "America/New_York").isValid()
      ? moment
          .tz(from_date, "YYYY-MM-DD:HH:mm", "America/New_York")
          .utc()
          .format("YYYY-MM-DDTHH:mm:ss")
      : ""
    : "";
  const end = to_date
    ? moment.tz(to_date, "YYYY-MM-DD:HH:mm", "America/New_York").isValid()
      ? moment
          .tz(to_date, "YYYY-MM-DD:HH:mm", "America/New_York")
          .utc()
          .format("YYYY-MM-DDTHH:mm:ss")
      : ""
    : "";

  // Build query string
  const fromDateQuery = start ? `&start_time=${start}` : "";
  const toDateQuery = end ? `&end_time=${end}` : "";
  const offsetQuery = `offset=${data.limit * data.page}`;
  const query = `${search}${
    search ? "&" : ""
  }${offsetQuery}${fromDateQuery}${toDateQuery}`;


  try {
    const url = `account/balance_search/${accountId}?${query}`;
    const response = await customInstance({ 
      url,
      method: 'GET',
      headers: { "x-pg-profile-id": profileId }
    });
    return response.data || { snapshots: [] };
  } catch (error) {
    console.error("getAccountBalanceSnapshots Error:", error);
    return { snapshots: [] };
  }
};

export const createAccountService = async ({ profile_id, ...data }) => {
  const response = await customInstance({
    url: 'account',
    method: 'POST',
    data,
    headers: { "x-pg-profile-id": profile_id }
  });
  return response.data;
};

export const updateAccountService = async ({
  profile_id,
  id,
  status,
  reason,
  ...data
}) => {
  const response = await customInstance({ url: `account/${id}`, method: 'PUT', data, headers: { "x-pg-profile-id": profile_id } });
  await customInstance({ 
    url: `account/${id}/status`,
    method: 'PATCH',
    data: {
      status,
      reason,
    },
    headers: { "x-pg-profile-id": profile_id }
  });

  return response.data;
};

export const getBalanceSnapshotHealth = async () => {
  const response = await customInstance({
    url: `health/balance/snapshot`,
    method: 'GET',
  });
  return response.data;
};

export const getTimeoutHealth = async () => {
  // Check environment variable to bypass API call
  if (import.meta.env.VITE_APP_BYPASS_HEALTH_CALLS === "true") {
    return { health: [] };
  }

  const today = moment().tz("America/New_York");
  const yesterday = moment().tz("America/New_York").subtract(1, "day");
  const start = today.utc().format("YYYY-MM-DDTHH:mm:ss");
  const end = yesterday.utc().format("YYYY-MM-DDTHH:mm:ss");

  try {
    const response = await customInstance({
      url: `health/cloudwatch/timeout_error?start_time=${end}&end_time=${start}`,
      method: 'GET'
    });
    return response.data || { health: [] };
  } catch (error) {
    console.error("getTimeoutHealth Error:", error);
    return { health: [] };
  }
};

export const getInternalHealth = async () => {
  // Check environment variable to bypass API call
  if (import.meta.env.VITE_APP_BYPASS_HEALTH_CALLS === "true") {
    return { health: [] };
  }

  const today = moment().tz("America/New_York");
  const yesterday = moment().tz("America/New_York").subtract(1, "day");
  const start = today.utc().format("YYYY-MM-DDTHH:mm:ss");
  const end = yesterday.utc().format("YYYY-MM-DDTHH:mm:ss");

  try {
    const response = await customInstance({
      url: `health/cloudwatch/internal_error?start_time=${end}&end_time=${start}`,
      method: 'GET'
    });
    return response.data || { health: [] };
  } catch (error) {
    console.error("getInternalHealth Error:", error);
    return { health: [] };
  }
};
