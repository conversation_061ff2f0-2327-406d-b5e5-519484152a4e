import { render } from '@testing-library/react'
import Header from '@/components/Header'
import customFindByTestId from '@/utils/findByTestId'

jest.mock('hooks/useAuth', () => () => ({
    isSignedIn: false,
    userSessionUpdate: jest.fn(),
    login: jest.fn(),
    logout: jest.fn,
    user: null
}))

const setUp = (props, auth) => {
    render(
        <Header {...props} />
    )
}

describe('UnAuthorized Header component', () => {
    it('should render without errors', () => {
        setUp()
        const header = customFindByTestId('header')
        expect(header).toBeInTheDocument()
    })

    it('Should render burger menu', () => {
        setUp()
        const burger = customFindByTestId('burger')
        expect(burger).toBeInTheDocument()
    })

    it ('Should render login button', () => {
        setUp()
        const login = customFindByTestId('login')
        expect(login).toBeInTheDocument()
        expect(login).toHaveTextContent('Login')
    })

    it ('Should not render logout button', () => {
        setUp()
        const logout = customFindByTestId('logout', 'query')
        expect(logout).not.toBeInTheDocument()
    })
})