import { render } from '@testing-library/react'
import { BrowserRouter as Router } from 'react-router-dom'
import Sidebar from '@/components/Sidebar'
import customFindByTestId from '@/utils/findByTestId'

const routes = [
    {
        to: '/profiles',
        path: '/profiles',
        name: 'Profiles',
        submenu:[
            {
                to: `/profiles/path`,
                path: '/profiles/:profileId',
                name: 'test profile',
                submenu: [
                    {
                        to: `/profiles/path1/path2`,
                        path: '/profiles/:profileId/:accountId',
                        name: 'test account'
                    }
                ]
            }
        ]
    }
]

const setUp = (props) => {
    render(
        <Router>
            <Sidebar {...props} />
        </Router>
    )
}


describe('SIdebar component', () => {

    it('should render without errors', () => {
        setUp()
        const sidebar = customFindByTestId('sidebar')
        expect(sidebar).toBeInTheDocument()
    })

    it('should render menu', () => {
        setUp()
        const menu = customFindByTestId('menu')
        expect(menu).toBeInTheDocument()
    })

    it('should render menu Item', () => {
        setUp({
            routes
        })
        const items = customFindByTestId('menu-item', 'all')
        expect(items).toHaveLength(routes.length)
    })

    it('should render sub menu', () => {
        setUp({
            routes
        })
        const submenu = customFindByTestId('submenu', 'all')
        expect(submenu).toHaveLength(2)
    })

    it('should render sub menu items', () => {
        setUp({
            routes
        })
        const items = customFindByTestId('submenu-item', 'all')
        expect(items).toHaveLength(2)
    })

})