import Form from "@/components/Forms/Form";
import CreateMetaData from "@/constants/forms/createMetaData";
import { useModal } from "@/contexts/ModalProvider";
import * as S from "../styled";

const MetaData = (props) => {
  const { show, formik } = useModal();

  const currentForm = CreateMetaData;

  // Access the transaction ID from props
  const transactionId = props.transactionId;

  // Create the statictext field
  const staticText = {
    type: "statictext",
    title: "Transaction ID:",
    text: transactionId || "",
    row: 2,
  };

  // Always include the staticText field in the form data
  const changedMetaForm = [staticText, ...currentForm];

  const handleClose = () => {
    props.closeModal();
    formik.resetForm();
  };

  return (
    <S.MedaDataModal
      show={show}
      handleClose={handleClose} // Use the new handleClose function
      onCreate={formik.handleSubmit}
      title="Additional Details"
      createTxt={props.edit ? "Save" : "Save"}
      testId={props.testId || "create-metaData-modal"}
      saveDisabled={!formik.isValid}
    >
      <Form
        formData={changedMetaForm}
        formik={formik}
        data-testid="form"
        noButton
      />
    </S.MedaDataModal>
  );
};

export default MetaData;
