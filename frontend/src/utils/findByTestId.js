import { screen } from '@testing-library/react' 

const findOptions = {
    all: screen.getAllByTestId,
    query: screen.queryByTestId,
    queryAll: screen.queryAllByTestId,
    default: screen.getByTestId
}

 const findByTestId = (testId, type) => { // it finds component by testId - it is used in tests
    const func = findOptions[type || 'default']
    return func(testId)
}


export default findByTestId