import React, { useState, useEffect } from 'react'
import { useParams } from 'react-router-dom'
import { DataGrid } from '@mui/x-data-grid'
import TableHeader from './TableHeader'
import * as S from './styled'


const disableSorting = (column, sortableFields = []) => column?.map((c) => {
    if (sortableFields.includes(c.field)) {
        return c
    }

    return {
        ...c,
        sortable: false
    }
})


const CTable = (props) => {
    const { id } = useParams()
    const [ limit, setLimit ] = useState(props.limit || 10)
    const [paramsId, setParamsId] = useState(null)
    const [showInfoModal, setShowInfoModal] = useState(false)
    const [hasEdit, setEdit] = useState(false)
    const [showCreateModal, setShowCreateModal] = useState(false)
    const [createModalData, setCreateModalData] = useState({})
    const [modalData, setModalData] = useState({})
    const { columns, data, infoModal, createModal, total, actions, getRowId, loading, hideFooterPagination, hideTotalCount, paginationMode } = props

    const InfoModal = infoModal || null
    const CreateModal = createModal || null

    const onRowClick = (item) => {
        if (InfoModal && item.field !== 'actions') {
            setShowInfoModal(true)
            setModalData(item.row)
        }
    }

    const onCreateClick = (e, item, edit) => {
        e.preventDefault()
        e.stopPropagation()

        setShowCreateModal(true)
        setCreateModalData(item)
        setEdit(edit)
    }

    useEffect(() => {
        if (id && paramsId !== id && data) {
            const currentData = props.data?.find((i) => i.id === id)

            setShowInfoModal(currentData ? true : false)
            setModalData(currentData || {})
            setParamsId(id)
        }
    }, [id, data])

    useEffect(() => {
        if (props.limit !== limit) {
            setLimit(props.limit)
        }
    }, [props.limit])

    const updatedColumns = props.disableSorting ? disableSorting(columns) : columns

    const serverPaginationProps = {
        rowCount: total || 0,
        paginationMode: 'server',
    }

    const clientPaginationProst = {
        paginationMode: 'client',
    }

    const paginationProps = paginationMode === 'server' ? serverPaginationProps : clientPaginationProst

    return (
        <S.TableContainer className={`d-flex row justify-content-start gl-table ${hideTotalCount ? 'hide-total' : ''}`} data-testid='table-container'>
            <TableHeader {...props} onCreateClick={onCreateClick} />

            <DataGrid
                rows={data || []}
                columns={updatedColumns || []}
                autoHeight
                onCellClick={onRowClick}
                sortingOrder={['desc', 'asc']}
                sortingMode='server'
                onSortModelChange={actions?.onSort}
                disableColumnFilter
                disableRowSelectionOnClick       
                columnBuffer={columns?.length || 0}
                disableColumnMenu
                loading={loading}
                disableSelectionOnClick={true}
                disableColumnSelector={true}
                getRowId={getRowId || null}
                hideFooterPagination={!!hideFooterPagination}
                pagination
                pageSizeOptions={[5, 10, 25]}
                onPaginationModelChange={actions?.onPaginationModel}
                pageSize={limit}
                { ...paginationProps }
                componentsProps={{
                    pagination: {
                        labelRowsPerPage: 'Records per page'
                    }
                }}
                initialState={{
                    pagination: { paginationModel: { pageSize: props.limit || 10 } }
                }}
            />

            {
                InfoModal && (
                    <InfoModal
                        show={showInfoModal}
                        handleClose={() => setShowInfoModal(false)}
                        data={modalData}
                        data-testid='info-modal'
                    />
                )
            }
            {
                CreateModal && (
                    <CreateModal
                        show={showCreateModal}
                        handleClose={() => setShowCreateModal(false)}
                        data={createModalData}
                        parentId={props.modalParentId}
                        edit={hasEdit}
                        data-testid='create-modal'
                    />
                )
            }
        </S.TableContainer>
        
    )

}

export default CTable