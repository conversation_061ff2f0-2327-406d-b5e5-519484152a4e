import React from 'react'
import Input from '../Input'
import Button from '@/components/Button'
import Accordion from './Accordion'
import _ from 'lodash'
import * as S from './styled'

const blackList = [
    'touched',
    'error',
    'value',
    'onChange',
    'validation'
]

const filterDataWhoHasChildren = (data) => data.filter(input => input?.children?.length) // filter out inputs who has children

const Form = (props) => {
    const { formData, formik, noButton, customInput, withOnChange  } = props

    const { touched, errors, handleChange, values, setValues, setFieldValue } = formik || {} // formik props

    const accordionData = filterDataWhoHasChildren(formData || [])

    const InputComponent = customInput || Input

    const onChange = _.debounce(formik?.handleSubmit || (() => {}), 150)

    const handeInputChange = (e) => {
        handleChange(e)
        if (withOnChange) {
            onChange()
        }
    }

    return (
        <S.Form className='custom-form' noValidate grid={props.grid} data-testid='custom-form'>
            <S.InputContainer className='input-container' grid={props.grid}>
                {
                    formData?.map((input, key) => {
                        if (input?.children?.length) {
                            return null
                        }

                        if (input?.render) {
                            return input.render
                        }

                        const name = input.name
                        return (
                            <InputComponent
                                {..._.omit(input, blackList)}
                                touched={_.get(touched, name)}
                                error={_.get(errors, name)}
                                value={_.get(values, name)}
                                onChange={handeInputChange}
                                setFieldValue={setFieldValue}
                                setValues={setValues}
                                values={values}
                                key={`input-${key}`}
                                data-testid='input'
                            />
                        )
                    })
                }
            </S.InputContainer>
            <Accordion data={accordionData} {...{ errors, touched, handleChange, values, setValues, blackList }} InputComponent={InputComponent} />

            {
                !noButton && (
                    <Button onClick={formik?.handleSubmit} disabled={!!props.buttonDisabled} type='primary' data-testid='form-button'>
                        {props.buttonText || 'Submit'}
                    </Button>
                )
            }

        </S.Form>
    )

}


export default Form