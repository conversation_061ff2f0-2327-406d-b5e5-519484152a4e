#!yaml
image: atlassian/default-image:4
clone:
  depth: 1
options:
  size: 4x
definitions:
  scripts:
    setup-global-env-vars: &setup-global-env-vars |
      export AWS_DEFAULT_REGION=ca-central-1
      export AWS_OIDC_ROLE_ARN=arn:aws:iam::891377081616:role/tol_oidc_iam_role
    version-image: &version-image
      image:
        name: 891377081616.dkr.ecr.ca-central-1.amazonaws.com/staging/cicd-pipelines/version-runner:0.2.3
        aws:
          oidc-role: arn:aws:iam::891377081616:role/tol_oidc_iam_role
    build-jar: &build-jar |
      mvn -T 1C -am -pl ${APPLICATION_PATH} -Dmaven.test.skip=true -B clean package
    mvn-test: &mvn-test |
      mvn -T 1C -fae -B test
    mvn-install: &mvn-install |
      mvn -T 1C dependency:go-offline
    pnpm-install: &pnpm-install |
      npm install -g pnpm
      pnpm install
    pnpm-test: &pnpm-test |
      pnpm test
    build-docker: &build-docker |
      export TAG=$(cat tag.txt)
      export REPO_NAME="dev/tol/${APPLICATION_PATH}"
      docker build --label app.version="$TAG" --label app.build_date="$(date +%Y%m%d)" -t $REPO_NAME:$TAG -f ${APPLICATION_PATH}${EXTRA_PATH:-}/Dockerfile ${APPLICATION_PATH}/.
    veracode-install: &veracode-install |
      export VERACODE_VERSION="**********"
      curl -O https://repo1.maven.org/maven2/com/veracode/vosp/api/wrappers/vosp-api-wrappers-java/$VERACODE_VERSION/vosp-api-wrappers-java-$VERACODE_VERSION.jar
    zip-mvn-application: &zip-mvn-application |
      yum install -y zip
      zip ${APPLICATION_PATH}.zip ${APPLICATION_PATH}/target/${APPLICATION_PATH}-1.0-SNAPSHOT.jar
      export ZIPPED_ARTIFACT="${APPLICATION_PATH}.zip"
    zip-pnpm-application: &zip-pnpm-application |
      yum install -y zip
      zip -r ${APPLICATION_PATH}.zip ${APPLICATION_PATH} -x "./${APPLICATION_PATH}/node_modules/*"
      export ZIPPED_ARTIFACT="${APPLICATION_PATH}.zip"
    veracode-upload-scan: &veracode-upload-scan |
      java -jar vosp-api-wrappers-java-$VERACODE_VERSION.jar \
        -action UploadAndScan \
        -vid "${VERACODE_API_ID}" \
        -vkey "${VERACODE_API_SECRET}" \
        -appname tol-${APPLICATION_PATH} \
        -createprofile true \
        -version $(cat tag.txt) \
        -filepath "$ZIPPED_ARTIFACT" \
        -scantype "STATIC" \
        -autoscan true \
        -deleteincompletescan 1 \
        -scanpollinginterval 120 \
        -teams "Product Development Team"
    veracode-sandbox-scan: &veracode-sandbox-scan |
      java -jar vosp-api-wrappers-java-**********.jar \
        -action UploadAndScan \
        -vid "${VERACODE_API_ID}" \
        -vkey "${VERACODE_API_SECRET}" \
        -appname tol-${APPLICATION_PATH} \
        -createprofile true \
        -version $(cat tag.txt) \
        -filepath "$ZIPPED_ARTIFACT" \
        -sandboxname "develop" \
        -createsandbox true \
        -scantype "STATIC" \
        -lifecyclestage "InDevelopmentPreAlpha" \
        -autoscan true \
        -deleteincompletescan 1 \
        -scanpollinginterval 120 \
        -teams "Product Development Team"
    ### Begin ledger-profile-api setup definitions ###
    setup-ledger-profile-api: &setup-ledger-profile-api |
      export APPLICATION_PATH=ledger-profile-api
    ### End ledger-profile-api setup definitions ###
    ### Begin ledger-account-api setup definitions ###
    setup-ledger-account-api: &setup-ledger-account-api |
      export APPLICATION_PATH=ledger-account-api
    ### End ledger-account-api setup definitions ###
    ### Begin ledger-transaction-api setup definitions ###
    setup-ledger-transaction-api: &setup-ledger-transaction-api |
      export APPLICATION_PATH=ledger-transaction-api
    ### End ledger-transaction-api setup definitions ###
    ### Begin ledger-schedulers-api setup definitions ###
    setup-ledger-schedulers-api: &setup-ledger-schedulers-api |
      export APPLICATION_PATH=ledger-schedulers-api
    ### End ledger-schedulers-api setup definitions ###
    ### Begin ledger-account-external-api setup definitions ###
    setup-ledger-account-external-api: &setup-ledger-account-external-api |
      export APPLICATION_PATH=ledger-account-external-api
    ### End ledger-account-external-api setup definitions ###
    ### Begin ledger-rollback-scheduler-api setup definitions ###
    setup-ledger-rollback-scheduler-api: &setup-ledger-rollback-scheduler-api |
      export APPLICATION_PATH=ledger-rollback-scheduler-api
    ### End ledger-rollback-scheduler-api setup definitions ###
    ### Begin ledger-transaction-async-api setup definitions ###
    setup-ledger-transaction-async-api: &setup-ledger-transaction-async-api |
      export APPLICATION_PATH=ledger-transaction-async-api
    ### End ledger-transaction-async-api setup definitions ###
    ### Begin ledger-transaction-async-listener setup definitions ###
    setup-ledger-transaction-async-listener: &setup-ledger-transaction-async-listener |
      export APPLICATION_PATH=ledger-transaction-async-listener
    ### End ledger-transaction-async-listener setup definitions ###
    ### Begin ledger-health-api setup definitions ###
    setup-ledger-health-api: &setup-ledger-health-api |
      export APPLICATION_PATH=ledger-health-api
    ### End ledger-health-api setup definitions ###
    ### Begin frontend setup definitions ###
    setup-frontend: &setup-frontend |
      export APPLICATION_PATH=frontend
    ### End frontend setup definitions ###
  steps:
    ### Begin ledger-profile-api step definitions ###
    - step: &veracode-upload-scan-ledger-profile-api
        name: Veracode Upload Scan ledger-profile-api
        image: maven:3.9-amazoncorretto-17-al2023
        caches:
          - maven
        script:
          - *setup-ledger-profile-api
          - *build-jar
          - *zip-mvn-application
          - *veracode-install
          - *veracode-upload-scan
    - step: &veracode-sandbox-scan-ledger-profile-api
        name: Veracode Sandbox Scan ledger-profile-api
        image: maven:3.9-amazoncorretto-17-al2023
        caches:
          - maven
        script:
          - *setup-ledger-profile-api
          - *build-jar
          - *zip-mvn-application
          - *veracode-install
          - *veracode-sandbox-scan
    - step: &docker-build-ledger-profile-api
        name: Docker build ledger-profile-api
        image: maven:3.9-amazoncorretto-17-al2023
        oidc: true
        services:
          - docker
        caches:
          - maven
        script:
          - *setup-ledger-profile-api
          - *setup-global-env-vars
          - *build-jar
          - *build-docker
          - pipe: atlassian/aws-ecr-push-image:2.4.2
            variables:
              AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION
              AWS_OIDC_ROLE_ARN: $AWS_OIDC_ROLE_ARN
              IMAGE_NAME: $REPO_NAME
              TAGS: $TAG
    ### End ledger-profile-api step definitions ###
    ### Begin ledger-account-api step definitions ###
    - step: &veracode-upload-scan-ledger-account-api
        name: Veracode Upload Scan ledger-account-api
        image: maven:3.9-amazoncorretto-17-al2023
        caches:
          - maven
        script:
          - *setup-ledger-account-api
          - *build-jar
          - *zip-mvn-application
          - *veracode-install
          - *veracode-upload-scan
    - step: &veracode-sandbox-scan-ledger-account-api
        name: Veracode Sandbox Scan ledger-account-api
        image: maven:3.9-amazoncorretto-17-al2023
        caches:
          - maven
        script:
          - *setup-ledger-account-api
          - *build-jar
          - *zip-mvn-application
          - *veracode-install
          - *veracode-sandbox-scan
    - step: &docker-build-ledger-account-api
        name: Docker build ledger-account-api
        image: maven:3.9-amazoncorretto-17-al2023
        oidc: true
        services:
          - docker
        caches:
          - maven
        script:
          - *setup-ledger-account-api
          - *setup-global-env-vars
          - *build-jar
          - *build-docker
          - pipe: atlassian/aws-ecr-push-image:2.4.2
            variables:
              AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION
              AWS_OIDC_ROLE_ARN: $AWS_OIDC_ROLE_ARN
              IMAGE_NAME: $REPO_NAME
              TAGS: $TAG
    ### End ledger-account-api step definitions ###
    ### Begin ledger-transaction-api step definitions ###
    - step: &veracode-upload-scan-ledger-transaction-api
        name: Veracode Upload Scan ledger-transaction-api
        image: maven:3.9-amazoncorretto-17-al2023
        caches:
          - maven
        script:
          - *setup-ledger-transaction-api
          - *build-jar
          - *zip-mvn-application
          - *veracode-install
          - *veracode-upload-scan
    - step: &veracode-sandbox-scan-ledger-transaction-api
        name: Veracode Sandbox Scan ledger-transaction-api
        image: maven:3.9-amazoncorretto-17-al2023
        caches:
          - maven
        script:
          - *setup-ledger-transaction-api
          - *build-jar
          - *zip-mvn-application
          - *veracode-install
          - *veracode-sandbox-scan
    - step: &docker-build-ledger-transaction-api
        name: Docker build ledger-transaction-api
        image: maven:3.9-amazoncorretto-17-al2023
        oidc: true
        services:
          - docker
        caches:
          - maven
        script:
          - *setup-ledger-transaction-api
          - *setup-global-env-vars
          - *build-jar
          - *build-docker
          - pipe: atlassian/aws-ecr-push-image:2.4.2
            variables:
              AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION
              AWS_OIDC_ROLE_ARN: $AWS_OIDC_ROLE_ARN
              IMAGE_NAME: $REPO_NAME
              TAGS: $TAG
    ### End ledger-transaction-api step definitions ###
    ### Begin ledger-schedulers-api step definitions ###
    - step: &veracode-upload-scan-ledger-schedulers-api
        name: Veracode Upload Scan ledger-schedulers-api
        image: maven:3.9-amazoncorretto-17-al2023
        caches:
          - maven
        script:
          - *setup-ledger-schedulers-api
          - *build-jar
          - *zip-mvn-application
          - *veracode-install
          - *veracode-upload-scan
    - step: &veracode-sandbox-scan-ledger-schedulers-api
        name: Veracode Sandbox Scan ledger-schedulers-api
        image: maven:3.9-amazoncorretto-17-al2023
        caches:
          - maven
        script:
          - *setup-ledger-schedulers-api
          - *build-jar
          - *zip-mvn-application
          - *veracode-install
          - *veracode-sandbox-scan
    - step: &docker-build-ledger-schedulers-api
        name: Docker build ledger-schedulers-api
        image: maven:3.9-amazoncorretto-17-al2023
        oidc: true
        services:
          - docker
        caches:
          - maven
        script:
          - *setup-ledger-schedulers-api
          - *setup-global-env-vars
          - *build-jar
          - *build-docker
          - pipe: atlassian/aws-ecr-push-image:2.4.2
            variables:
              AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION
              AWS_OIDC_ROLE_ARN: $AWS_OIDC_ROLE_ARN
              IMAGE_NAME: $REPO_NAME
              TAGS: $TAG
    ### End ledger-schedulers-api step definitions ###
    ### Begin ledger-account-external-api step definitions ###
    - step: &veracode-upload-scan-ledger-account-external-api
        name: Veracode Upload Scan ledger-account-external-api
        image: maven:3.9-amazoncorretto-17-al2023
        caches:
          - maven
        script:
          - *setup-ledger-account-external-api
          - *build-jar
          - *zip-mvn-application
          - *veracode-install
          - *veracode-upload-scan
    - step: &veracode-sandbox-scan-ledger-account-external-api
        name: Veracode Sandbox Scan ledger-account-external-api
        image: maven:3.9-amazoncorretto-17-al2023
        caches:
          - maven
        script:
          - *setup-ledger-account-external-api
          - *build-jar
          - *zip-mvn-application
          - *veracode-install
          - *veracode-sandbox-scan
    - step: &docker-build-ledger-account-external-api
        name: Docker build ledger-account-external-api
        image: maven:3.9-amazoncorretto-17-al2023
        oidc: true
        services:
          - docker
        caches:
          - maven
        script:
          - *setup-ledger-account-external-api
          - *setup-global-env-vars
          - *build-jar
          - *build-docker
          - pipe: atlassian/aws-ecr-push-image:2.4.2
            variables:
              AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION
              AWS_OIDC_ROLE_ARN: $AWS_OIDC_ROLE_ARN
              IMAGE_NAME: $REPO_NAME
              TAGS: $TAG
    ### End ledger-account-external-api step definitions ###
    ### Begin ledger-rollback-scheduler-api step definitions ###
    - step: &veracode-upload-scan-ledger-rollback-scheduler-api
        name: Veracode Upload Scan ledger-rollback-scheduler-api
        image: maven:3.9-amazoncorretto-17-al2023
        caches:
          - maven
        script:
          - *setup-ledger-rollback-scheduler-api
          - *build-jar
          - *zip-mvn-application
          - *veracode-install
          - *veracode-upload-scan
    - step: &veracode-sandbox-scan-ledger-rollback-scheduler-api
        name: Veracode Sandbox Scan ledger-rollback-scheduler-api
        image: maven:3.9-amazoncorretto-17-al2023
        caches:
          - maven
        script:
          - *setup-ledger-rollback-scheduler-api
          - *build-jar
          - *zip-mvn-application
          - *veracode-install
          - *veracode-sandbox-scan
    - step: &docker-build-ledger-rollback-scheduler-api
        name: Docker build ledger-rollback-scheduler-api
        image: maven:3.9-amazoncorretto-17-al2023
        oidc: true
        services:
          - docker
        caches:
          - maven
        script:
          - *setup-ledger-rollback-scheduler-api
          - *setup-global-env-vars
          - *build-jar
          - *build-docker
          - pipe: atlassian/aws-ecr-push-image:2.4.2
            variables:
              AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION
              AWS_OIDC_ROLE_ARN: $AWS_OIDC_ROLE_ARN
              IMAGE_NAME: $REPO_NAME
              TAGS: $TAG
    ### End ledger-rollback-scheduler-api step definitions ###
    ### Begin ledger-transaction-async-api step definitions ###
    - step: &veracode-upload-scan-ledger-transaction-async-api
        name: Veracode Upload Scan ledger-transaction-async-api
        image: maven:3.9-amazoncorretto-17-al2023
        caches:
          - maven
        script:
          - *setup-ledger-transaction-async-api
          - *build-jar
          - *zip-mvn-application
          - *veracode-install
          - *veracode-upload-scan
    - step: &veracode-sandbox-scan-ledger-transaction-async-api
        name: Veracode Sandbox Scan ledger-transaction-async-api
        image: maven:3.9-amazoncorretto-17-al2023
        caches:
          - maven
        script:
          - *setup-ledger-transaction-async-api
          - *build-jar
          - *zip-mvn-application
          - *veracode-install
          - *veracode-sandbox-scan
    - step: &docker-build-ledger-transaction-async-api
        name: Docker build ledger-transaction-async-api
        image: maven:3.9-amazoncorretto-17-al2023
        oidc: true
        services:
          - docker
        caches:
          - maven
        script:
          - *setup-ledger-transaction-async-api
          - *setup-global-env-vars
          - *build-jar
          - *build-docker
          - pipe: atlassian/aws-ecr-push-image:2.4.2
            variables:
              AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION
              AWS_OIDC_ROLE_ARN: $AWS_OIDC_ROLE_ARN
              IMAGE_NAME: $REPO_NAME
              TAGS: $TAG
    ### End ledger-transaction-async-api step definitions ###
    ### Begin ledger-transaction-async-listener step definitions ###
    - step: &veracode-upload-scan-ledger-transaction-async-listener
        name: Veracode Upload Scan ledger-transaction-async-listener
        image: maven:3.9-amazoncorretto-17-al2023
        caches:
          - maven
        script:
          - *setup-ledger-transaction-async-listener
          - *build-jar
          - *zip-mvn-application
          - *veracode-install
          - *veracode-upload-scan
    - step: &veracode-sandbox-scan-ledger-transaction-async-listener
        name: Veracode Sandbox Scan ledger-transaction-async-listener
        image: maven:3.9-amazoncorretto-17-al2023
        caches:
          - maven
        script:
          - *setup-ledger-transaction-async-listener
          - *build-jar
          - *zip-mvn-application
          - *veracode-install
          - *veracode-sandbox-scan
    - step: &docker-build-ledger-transaction-async-listener
        name: Docker build ledger-transaction-async-listener
        image: maven:3.9-amazoncorretto-17-al2023
        oidc: true
        services:
          - docker
        caches:
          - maven
        script:
          - *setup-ledger-transaction-async-listener
          - *setup-global-env-vars
          - *build-jar
          - *build-docker
          - pipe: atlassian/aws-ecr-push-image:2.4.2
            variables:
              AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION
              AWS_OIDC_ROLE_ARN: $AWS_OIDC_ROLE_ARN
              IMAGE_NAME: $REPO_NAME
              TAGS: $TAG
    ### End ledger-transaction-async-listener step definitions ###
    ### Begin ledger-health-api step definitions ###
    - step: &veracode-upload-scan-ledger-health-api
        name: Veracode Upload Scan ledger-health-api
        image: maven:3.9-amazoncorretto-17-al2023
        caches:
          - maven
        script:
          - *setup-ledger-health-api
          - *build-jar
          - *zip-mvn-application
          - *veracode-install
          - *veracode-upload-scan
    - step: &veracode-sandbox-scan-ledger-health-api
        name: Veracode Sandbox Scan ledger-health-api
        image: maven:3.9-amazoncorretto-17-al2023
        caches:
          - maven
        script:
          - *setup-ledger-health-api
          - *build-jar
          - *zip-mvn-application
          - *veracode-install
          - *veracode-sandbox-scan
    - step: &docker-build-ledger-health-api
        name: Docker build ledger-health-api
        image: maven:3.9-amazoncorretto-17-al2023
        oidc: true
        services:
          - docker
        caches:
          - maven
        script:
          - *setup-ledger-health-api
          - *setup-global-env-vars
          - *build-jar
          - *build-docker
          - pipe: atlassian/aws-ecr-push-image:2.4.2
            variables:
              AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION
              AWS_OIDC_ROLE_ARN: $AWS_OIDC_ROLE_ARN
              IMAGE_NAME: $REPO_NAME
              TAGS: $TAG
    ### End ledger-health-api step definitions ###
    ### Begin frontend pnpm step definitions ###
    - step: &pnpm-test-frontend
        name: Test frontend pnpm project
        image: node:22
        caches:
          - node
        script:
          - *setup-frontend
          - cd $APPLICATION_PATH
          - *pnpm-install
          - *pnpm-test
    - step: &veracode-upload-scan-frontend
        name: Veracode Upload Scan frontend
        image: maven:3.9-amazoncorretto-17-al2023
        script:
          - *setup-frontend
          - *zip-pnpm-application
          - *veracode-install
          - *veracode-upload-scan
    - step: &veracode-sandbox-scan-frontend
        name: Veracode Sandbox Scan frontend
        image: maven:3.9-amazoncorretto-17-al2023
        script:
          - *setup-frontend
          - *zip-pnpm-application
          - *veracode-install
          - *veracode-sandbox-scan
    ### End frontend pnpm step definitions ###
    - step: &test-mvn
        name: Test all Java projects
        image: maven:3.9-amazoncorretto-17-al2023
        caches:
          - maven
        script:
          - *mvn-test
    - step: &generate-tag
        name: "Generate tag"
        oidc: true
        script:
          - |
            echo "$BITBUCKET_BUILD_NUMBER-alpha" > tag.txt
            echo "Created tag: $BITBUCKET_BUILD_NUMBER"
        artifacts:
          - tag.txt
    - step: &tag-repo
        name: "Tag Repo"
        <<: *version-image
        oidc: true
        script:
          - |
            versionTag=$(cat tag.txt)
            git tag "$versionTag"
            git push origin tag "$versionTag"
    - step: &mvn-install-deps
        name: "Install dependencies"
        image: maven:3.9-amazoncorretto-17-al2023
        caches:
          - maven
        script:
          - *mvn-install

pipelines:
  pull-requests:
    "**":
      - parallel:
          fail-fast: true
          steps:
            - step:
                <<: *generate-tag
            - step:
                <<: *mvn-install-deps
      - parallel:
          steps:
            # TODO - uncomment when unit tests are separated from integration
            # - step:
            #    <<: *test-mvn
            ### Begin ledger-profile-api step generation ###
            - step:
                <<: *docker-build-ledger-profile-api
            ### End ledger-profile-api step generation ###
            ### Begin ledger-account-api step generation ###
            - step:
                <<: *docker-build-ledger-account-api
            ### End ledger-account-api step generation ###
            ### Begin ledger-transaction-api step generation ###
            - step:
                <<: *docker-build-ledger-transaction-api
            ### End ledger-transaction-api step generation ###
            ### Begin ledger-schedulers-api step generation ###
            - step:
                <<: *docker-build-ledger-schedulers-api
            ### End ledger-schedulers-api step generation ###
            ### Begin ledger-account-external-api step generation ###
            - step:
                <<: *docker-build-ledger-account-external-api
            ### End ledger-account-external-api step generation ###
            ### Begin ledger-rollback-scheduler-api step generation ###
            - step:
                <<: *docker-build-ledger-rollback-scheduler-api
            ### End ledger-rollback-scheduler-api step generation ###
            ### Begin ledger-transaction-async-api step generation ###
            - step:
                <<: *docker-build-ledger-transaction-async-api
            ### End ledger-transaction-async-api step generation ###
            ### Begin ledger-transaction-async-listener step generation ###
            - step:
                <<: *docker-build-ledger-transaction-async-listener
            ### End ledger-transaction-async-listener step generation ###
            ### Begin ledger-health-api step generation ###
            - step:
                <<: *docker-build-ledger-health-api
            ### End ledger-health-api step generation ###
            ### Begin frontend step generation ###
            - step:
                <<: *pnpm-test-frontend
            ### End frontend step generation ###
  branches:
    develop:
      - parallel:
          fail-fast: true
          steps:
            - step:
                <<: *generate-tag
            - step:
                <<: *mvn-install-deps
      - parallel:
          steps:
            # TODO - uncomment when unit tests are separated from integration
            # - step:
            #    <<: *test-mvn
            ### Begin ledger-profile-api step generation ###
            - step:
                <<: *docker-build-ledger-profile-api
            - step:
                <<: *veracode-sandbox-scan-ledger-profile-api
            ### End ledger-profile-api step generation ###
            ### Begin ledger-account-api step generation ###
            - step:
                <<: *docker-build-ledger-account-api
            - step:
                <<: *veracode-sandbox-scan-ledger-account-api
            ### End ledger-account-api step generation ###
            ### Begin ledger-transaction-api step generation ###
            - step:
                <<: *docker-build-ledger-transaction-api
            - step:
                <<: *veracode-sandbox-scan-ledger-transaction-api
            ### End ledger-transaction-api step generation ###
            ### Begin ledger-schedulers-api step generation ###
            - step:
                <<: *docker-build-ledger-schedulers-api
            - step:
                <<: *veracode-sandbox-scan-ledger-schedulers-api
            ### End ledger-schedulers-api step generation ###
            ### Begin ledger-account-external-api step generation ###
            - step:
                <<: *docker-build-ledger-account-external-api
            - step:
                <<: *veracode-sandbox-scan-ledger-account-external-api
            ### End ledger-account-external-api step generation ###
            ### Begin ledger-rollback-scheduler-api step generation ###
            - step:
                <<: *docker-build-ledger-rollback-scheduler-api
            - step:
                <<: *veracode-sandbox-scan-ledger-rollback-scheduler-api
            ### End ledger-rollback-scheduler-api step generation ###
            ### Begin ledger-transaction-async-api step generation ###
            - step:
                <<: *docker-build-ledger-transaction-async-api
            - step:
                <<: *veracode-sandbox-scan-ledger-transaction-async-api
            ### End ledger-transaction-async-api step generation ###
            ### Begin ledger-transaction-async-listener step generation ###
            - step:
                <<: *docker-build-ledger-transaction-async-listener
            - step:
                <<: *veracode-sandbox-scan-ledger-transaction-async-listener
            ### End ledger-transaction-async-listener step generation ###
            ### Begin ledger-health-api step generation ###
            - step:
                <<: *docker-build-ledger-health-api
            - step:
                <<: *veracode-sandbox-scan-ledger-health-api
            ### End ledger-health-api step generation ###
            ### Begin frontend step generation ###
            - step:
                <<: *pnpm-test-frontend
            - step:
                <<: *veracode-sandbox-scan-frontend
            ### End frontend step generation ###
    master:
      - parallel:
          fail-fast: true
          steps:
            - step:
                <<: *generate-tag
            - step:
                <<: *mvn-install-deps
      - parallel:
          steps:
            # TODO - uncomment when unit tests are separated from integration
            # - step:
            #    <<: *test-mvn
            ### Begin ledger-profile-api step generation ###
            - step:
                <<: *docker-build-ledger-profile-api
            - step:
                <<: *veracode-upload-scan-ledger-profile-api
            ### End ledger-profile-api step generation ###
            ### Begin ledger-account-api step generation ###
            - step:
                <<: *docker-build-ledger-account-api
            - step:
                <<: *veracode-upload-scan-ledger-account-api
            ### End ledger-account-api step generation ###
            ### Begin ledger-transaction-api step generation ###
            - step:
                <<: *docker-build-ledger-transaction-api
            - step:
                <<: *veracode-upload-scan-ledger-transaction-api
            ### End ledger-transaction-api step generation ###
            ### Begin ledger-schedulers-api step generation ###
            - step:
                <<: *docker-build-ledger-schedulers-api
            - step:
                <<: *veracode-upload-scan-ledger-schedulers-api
            ### End ledger-schedulers-api step generation ###
            ### Begin ledger-account-external-api step generation ###
            - step:
                <<: *docker-build-ledger-account-external-api
            - step:
                <<: *veracode-upload-scan-ledger-account-external-api
            ### End ledger-account-external-api step generation ###
            ### Begin ledger-rollback-scheduler-api step generation ###
            - step:
                <<: *docker-build-ledger-rollback-scheduler-api
            - step:
                <<: *veracode-upload-scan-ledger-rollback-scheduler-api
            ### End ledger-rollback-scheduler-api step generation ###
            ### Begin ledger-transaction-async-api step generation ###
            - step:
                <<: *docker-build-ledger-transaction-async-api
            - step:
                <<: *veracode-upload-scan-ledger-transaction-async-api
            ### End ledger-transaction-async-api step generation ###
            ### Begin ledger-transaction-async-listener step generation ###
            - step:
                <<: *docker-build-ledger-transaction-async-listener
            - step:
                <<: *veracode-upload-scan-ledger-transaction-async-listener
            ### End ledger-transaction-async-listener step generation ###
            ### Begin ledger-health-api step generation ###
            - step:
                <<: *docker-build-ledger-health-api
            - step:
                <<: *veracode-upload-scan-ledger-health-api
            ### End ledger-health-api step generation ###
            ### Begin frontend step generation ###
            - step:
                <<: *pnpm-test-frontend
            - step:
                <<: *veracode-upload-scan-frontend
            ### End frontend step generation ###
