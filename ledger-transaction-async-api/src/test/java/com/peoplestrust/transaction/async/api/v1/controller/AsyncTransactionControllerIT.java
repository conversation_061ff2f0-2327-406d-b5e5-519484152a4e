package com.peoplestrust.transaction.async.api.v1.controller;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.peoplestrust.account.persistence.entity.AccountEntity;
import com.peoplestrust.account.persistence.entity.AccountStatus;
import com.peoplestrust.account.persistence.entity.OptionsEntity;
import com.peoplestrust.account.persistence.repository.read.ReadAccountRepository;
import com.peoplestrust.transaction.async.api.v1.config.AsyncTransactionProperty;
import com.peoplestrust.transaction.async.api.v1.config.KafkaConfig;
import com.peoplestrust.transaction.async.api.v1.mapper.AsyncTransactionMapper;
import com.peoplestrust.transaction.async.api.v1.service.AsyncTransactionServiceImpl;
import com.peoplestrust.transaction.async.api.v1.service.ValidationService;
import com.peoplestrust.transaction.async.domain.model.InitiateLedgerTransactionAsyncRequest;
import com.peoplestrust.transaction.async.domain.model.InitiateLedgerTransactionAsyncRequestTransactionsInner;
import com.peoplestrust.transaction.async.domain.model.PaymentCategory;
import com.peoplestrust.transaction.async.domain.model.PaymentRail;
import com.peoplestrust.util.api.common.config.APICommonUtilConstant;
import com.peoplestrust.util.api.common.util.DateUtils;
import com.peoplestrust.util.api.common.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import net.bytebuddy.utility.RandomString;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.kafka.core.KafkaAdmin;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.util.concurrent.SettableListenableFuture;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ExtendWith(MockitoExtension.class)
@ActiveProfiles("test")
@Slf4j
public class AsyncTransactionControllerIT extends DefaultControllerTest {
    public final static String JWT_TOKEN = "**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";
    public final static String BAD_JWT_TOKEN = "eyJraWQiOiJxeXZnRXIzV0VFT2hONlR1N2hoakg5Q3BkcXp3WGJJY0l1VFdRSCtVRWRnPSIsImFsZyI6IlJTMjU2In0.eyJzdWIiOiJjZTlhOWQ0ZC1jMWNmLTQxNWQtOTllNS1mNzg4MzZiYzFhOTIiLCJjb2duaXRvOmdyb3VwcyI6WyJHTF9BRE1JTklTVFJBVElPTiIsIkVGVF9ET0NVTUVOVEFUSU9OIiwidXMtZWFzdC0yX29IMkI1UUlVb19QZW9wbGVzVHJ1c3RBREZTIiwiRVRSQU5TRkVSX0FETUlOSVNUUkFUT1IiXSwiaXNzIjoiaHR0cHM6XC9cL2NvZ25pdG8taWRwLnVzLWVhc3QtMi5hbWF6b25hd3MuY29tXC91cy1lYXN0LTJfb0gyQjVRSVVvIiwidmVyc2lvbiI6MiwiY2xpZW50X2lkIjoiMjAxdGxhMHZhdXE3cmk2Zmx2MGpuYWIyZ2UiLCJvcmlnaW5fanRpIjoiOGUzMmFjM2YtMTQ1OS00ZmE0LWEwZWMtZjk4NGY4YzY2MmJhIiwidG9rZW5fdXNlIjoiYWNjZXNzIiwic2NvcGUiOiJvcGVuaWQgY29tLnBlb3BsZXNncm91cC5sZWRnZXIuc3RnXC9hZG1pblVJLmFsbCBlbWFpbCIsImF1dGhfdGltZSI6MTcyMjI4NDE1MywiZXhwIjoxNzIyMjg3NzUzLCJpYXQiOjE3MjIyODQxNTMsImp0aSI6IjU1YTcyNTM5LWM0MzUtNDJjMi1hODhhLWZkMTA3MTdiNDNlZiIsI.O-vUHqyUAwObCijDZx2CGUjye7czV63cYqS1tL_t_sNbVmLS2ghapd75hwAZbDnZ8h8pN1S9JkPkx5qF3r7OticHO0UAyfLUennNN1g713cYTD_pIVqXXd7WUowyvstLhaVpNwhpqTID5vuu8f3J_yHImjUBUtYGvnR4uV7WnpWEVL2LsbhCa0iB_LeDBZEws7OYBvPwqOCaf7y58EMukOUtwizMEAslEofF-5K9RXG_Q8TKApcKixnheMqDZ7IcbKlK9XLMqn-sr147BvvaPRH1DmQbrPxFSz1z6Ihw_wAI21qDKXWX-OxGv_-gYq9NF-a-HpO0qHo_GB4NDh31EA";

    private static String profileRefId = UUID.randomUUID().toString();
    private static String accountRefId = UUID.randomUUID().toString();
    private static String interactionId = UUID.randomUUID().toString();

    String URL = "/v1/ledger/transaction/async";

    @Autowired
    AsyncTransactionMapper transactionMapper;

    @Autowired
    AsyncTransactionProperty transactionProperty;

    @MockBean
    KafkaConfig kafkaConfig;

    @MockBean
    KafkaAdmin kafkaAdmin;

    @MockBean
    private KafkaTemplate<String, Object> kafkaTemplate;

    @Autowired
    AsyncTransactionServiceImpl asyncTransactionService;

    @MockBean
    ReadAccountRepository readAccountRepository;

    @Autowired
    ValidationService validationService;

    @Autowired
    AsyncTransactionProperty asyncTransactionProperty;


    HttpHeaders headers;
    private ObjectMapper objectMapper;
    InitiateLedgerTransactionAsyncRequest initiateLedgerTransactionAsyncRequest;
    String transactionRefId;


    @BeforeEach
    public void setupBeforeTest() throws Exception {
        super.setup();
        UUID uuid = UUID.randomUUID();
        transactionRefId = UUID.randomUUID().toString();
        objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        interactionId = uuid.toString();
        headers = new HttpHeaders();
        initiateLedgerTransactionAsyncRequest = buildInstruction();
        headers.add(APICommonUtilConstant.HEADER_INTERACTION_ID, interactionId);
        headers.add(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString());
        headers.add(APICommonUtilConstant.HEADER_PROFILE_ID, profileRefId);
        headers.add(APICommonUtilConstant.HEADER_ACCOUNT_ID, accountRefId);
        headers.add(APICommonUtilConstant.AUTHORIZATION, JWT_TOKEN);
    }


    @Test
    public void createTransactionTest() throws Exception {

        AccountEntity accountEntity = createAccount();
        when(readAccountRepository.findByRefId(any())).thenReturn(Optional.ofNullable(accountEntity));

        mockKafkaSend();

        this.mockMvc.perform(MockMvcRequestBuilders.post(URL).headers(headers).content(JsonUtil.toString(initiateLedgerTransactionAsyncRequest))
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON)).andExpect(status().isNoContent()).andReturn();
    }


    @Test
    public void commitTransactionTest() throws Exception {

        AccountEntity accountEntity = createAccount();
        InitiateLedgerTransactionAsyncRequest initiateLedgerTransactionAsyncRequest = buildInstruction();
        String insRefId = initiateLedgerTransactionAsyncRequest.getInstructionRefId();

        mockKafkaSend();
        when(readAccountRepository.findByRefId(any())).thenReturn(Optional.ofNullable(accountEntity));
        this.mockMvc.perform(MockMvcRequestBuilders.patch(URL + "/" + insRefId).headers(headers)
            .contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON)).andExpect(status().isNoContent()).andReturn();
    }

    @Test
    public void rollbackTransactionTest() throws Exception {

        AccountEntity accountEntity = createAccount();
        InitiateLedgerTransactionAsyncRequest initiateLedgerTransactionAsyncRequest = buildInstruction();
        String insRefId = initiateLedgerTransactionAsyncRequest.getInstructionRefId();

        mockKafkaSend();
        when(readAccountRepository.findByRefId(any())).thenReturn(Optional.ofNullable(accountEntity));
        this.mockMvc.perform(MockMvcRequestBuilders.delete(URL + "/" + insRefId).headers(headers)
            .contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON)).andExpect(status().isNoContent()).andReturn();
    }

    @Test
    public void reverseTransactionTest() throws Exception {

        AccountEntity accountEntity = createAccount();
        InitiateLedgerTransactionAsyncRequest initiateLedgerTransactionAsyncRequest = buildInstruction();
        String insRefId = initiateLedgerTransactionAsyncRequest.getInstructionRefId();
        String transRefId = initiateLedgerTransactionAsyncRequest.getTransactions().get(0).getTransactionRefId();
        mockKafkaSend();
        when(readAccountRepository.findByRefId(any())).thenReturn(Optional.ofNullable(accountEntity));
        this.mockMvc.perform(MockMvcRequestBuilders.delete(URL + "/" + insRefId + "/" + transRefId).headers(headers)
            .contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON)).andExpect(status().isNoContent()).andReturn();
    }

    @Test
    public void createTransactionTest_failure() throws Exception {

        AccountEntity accountEntity = createAccount();
        when(readAccountRepository.findByRefId(any())).thenReturn(Optional.ofNullable(accountEntity));

        mockKafkaReturnNull();

        this.mockMvc.perform(MockMvcRequestBuilders.post(URL).headers(headers).content(JsonUtil.toString(initiateLedgerTransactionAsyncRequest))
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON)).andExpect(status().isInternalServerError());
    }

    @Test
    public void createTransactionTest_BadRequest() throws Exception {

        AccountEntity accountEntity = createAccount();
        when(readAccountRepository.findByRefId(any())).thenReturn(Optional.ofNullable(accountEntity));
        headers.remove(APICommonUtilConstant.HEADER_PROFILE_ID);
        mockKafkaSend();

        this.mockMvc.perform(MockMvcRequestBuilders.post(URL).headers(headers).content(JsonUtil.toString(initiateLedgerTransactionAsyncRequest))
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON)).andExpect(status().isBadRequest());
    }

    @Test
    public void commitTransactionTest_failure() throws Exception {

        AccountEntity accountEntity = createAccount();
        InitiateLedgerTransactionAsyncRequest initiateLedgerTransactionAsyncRequest = buildInstruction();
        String insRefId = initiateLedgerTransactionAsyncRequest.getInstructionRefId();

        mockKafkaReturnNull();
        when(readAccountRepository.findByRefId(any())).thenReturn(Optional.ofNullable(accountEntity));
        this.mockMvc.perform(MockMvcRequestBuilders.patch(URL + "/" + insRefId).headers(headers)
            .contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON)).andExpect(status().isInternalServerError());
    }

    @Test
    public void commitTransactionTest_BadRequest() throws Exception {

        AccountEntity accountEntity = createAccount();
        InitiateLedgerTransactionAsyncRequest initiateLedgerTransactionAsyncRequest = buildInstruction();
        String insRefId = initiateLedgerTransactionAsyncRequest.getInstructionRefId();
        headers.remove(APICommonUtilConstant.HEADER_PROFILE_ID);
        mockKafkaSend();
        when(readAccountRepository.findByRefId(any())).thenReturn(Optional.ofNullable(accountEntity));
        this.mockMvc.perform(MockMvcRequestBuilders.patch(URL + "/" + insRefId).headers(headers)
            .contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON)).andExpect(status().isBadRequest());
    }

    @Test
    public void rollbackTransactionTest_failure() throws Exception {

        AccountEntity accountEntity = createAccount();
        InitiateLedgerTransactionAsyncRequest initiateLedgerTransactionAsyncRequest = buildInstruction();
        String insRefId = initiateLedgerTransactionAsyncRequest.getInstructionRefId();

        mockKafkaReturnNull();
        when(readAccountRepository.findByRefId(any())).thenReturn(Optional.ofNullable(accountEntity));
        this.mockMvc.perform(MockMvcRequestBuilders.delete(URL + "/" + insRefId).headers(headers)
            .contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON)).andExpect(status().isInternalServerError());
    }

    @Test
    public void rollbackTransactionTest_BadRequest() throws Exception {

        AccountEntity accountEntity = createAccount();
        InitiateLedgerTransactionAsyncRequest initiateLedgerTransactionAsyncRequest = buildInstruction();
        String insRefId = initiateLedgerTransactionAsyncRequest.getInstructionRefId();
        headers.remove(APICommonUtilConstant.HEADER_PROFILE_ID);
        mockKafkaSend();
        when(readAccountRepository.findByRefId(any())).thenReturn(Optional.ofNullable(accountEntity));
        this.mockMvc.perform(MockMvcRequestBuilders.delete(URL + "/" + insRefId).headers(headers)
            .contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON)).andExpect(status().isBadRequest());
    }

    @Test
    public void reverseTransactionTest_failure() throws Exception {

        AccountEntity accountEntity = createAccount();
        InitiateLedgerTransactionAsyncRequest initiateLedgerTransactionAsyncRequest = buildInstruction();
        String insRefId = initiateLedgerTransactionAsyncRequest.getInstructionRefId();
        String transRefId = initiateLedgerTransactionAsyncRequest.getTransactions().get(0).getTransactionRefId();
        mockKafkaReturnNull();
        when(readAccountRepository.findByRefId(any())).thenReturn(Optional.ofNullable(accountEntity));
        this.mockMvc.perform(MockMvcRequestBuilders.delete(URL + "/" + insRefId + "/" + transRefId).headers(headers)
            .contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON)).andExpect(status().isInternalServerError());
    }

    @Test
    public void reverseTransactionTest_BadRequest() throws Exception {

        AccountEntity accountEntity = createAccount();
        InitiateLedgerTransactionAsyncRequest initiateLedgerTransactionAsyncRequest = buildInstruction();
        String insRefId = initiateLedgerTransactionAsyncRequest.getInstructionRefId();
        String transRefId = initiateLedgerTransactionAsyncRequest.getTransactions().get(0).getTransactionRefId();
        headers.remove(APICommonUtilConstant.HEADER_PROFILE_ID);
        mockKafkaSend();
        when(readAccountRepository.findByRefId(any())).thenReturn(Optional.ofNullable(accountEntity));
        this.mockMvc.perform(MockMvcRequestBuilders.delete(URL + "/" + insRefId + "/" + transRefId).headers(headers)
            .contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON)).andExpect(status().isBadRequest());
    }

    private void mockKafkaSend() {
        // Create a SettableListenableFuture and complete it
        SettableListenableFuture<SendResult<String, Object>> settableFuture = new SettableListenableFuture<>();
        settableFuture.set(new SendResult<>(new ProducerRecord<>("topic", "key", "value"), new RecordMetadata(null, 0, 0, 0, null, 0, 0)));

        // Convert it to CompletableFuture
        CompletableFuture<SendResult<String, Object>> completableFuture = new CompletableFuture<>();
        settableFuture.addCallback(completableFuture::complete, completableFuture::completeExceptionally);

        // Mock the send method to return the CompletableFuture
        when(kafkaTemplate.send(any(ProducerRecord.class))).thenReturn(completableFuture);
    }

    private void mockKafkaReturnNull() {
        // Create a SettableListenableFuture and complete it
        SettableListenableFuture<SendResult<String, Object>> settableFuture = new SettableListenableFuture<>();
        settableFuture.set(new SendResult<>(new ProducerRecord<>("topic", "key", "value"), new RecordMetadata(null, 0, 0, 0, null, 0, 0)));

        // Mock the send method to return null
        when(kafkaTemplate.send(any(ProducerRecord.class))).thenReturn(null);
    }

    private InitiateLedgerTransactionAsyncRequest buildInstruction() throws Exception {
        UUID uuid = UUID.randomUUID();
        String word = "TEST_INST" + RandomString.make(7);
        List<InitiateLedgerTransactionAsyncRequestTransactionsInner> transactions = buildTransactions();

        InitiateLedgerTransactionAsyncRequest instruction = new InitiateLedgerTransactionAsyncRequest();

        instruction.setInstructionRefId(String.valueOf(word));
        instruction.setPaymentRail(PaymentRail.ETRANSFER);
        instruction.setTransactions(transactions);

        return instruction;
    }


    private List<InitiateLedgerTransactionAsyncRequestTransactionsInner> buildTransactions() {
        List<InitiateLedgerTransactionAsyncRequestTransactionsInner> list = new ArrayList<>();

        InitiateLedgerTransactionAsyncRequestTransactionsInner t1 = new InitiateLedgerTransactionAsyncRequestTransactionsInner();
        t1.setTransactionRefId(transactionRefId);
        t1.setPaymentCategory(PaymentCategory.COMPLETE_PAYMENT);
        t1.setAmount(new BigDecimal(100));
        t1.setMonetaryUnit("CAD");
        t1.setAcceptanceDateTime(DateUtils.offsetDateTime());
        t1.setDueDateTime(DateUtils.offsetDateTime().plusDays(2));

        InitiateLedgerTransactionAsyncRequestTransactionsInner t2 = new InitiateLedgerTransactionAsyncRequestTransactionsInner();
        t2.setTransactionRefId(UUID.randomUUID().toString());
        t2.setPaymentCategory(PaymentCategory.SEND_PAYMENT);
        t2.setAmount(new BigDecimal(100));
        t2.setMonetaryUnit("CAD");
        t2.setAcceptanceDateTime(DateUtils.offsetDateTime());

        list.add(t1);
        list.add(t2);
        return list;
    }

    private AccountEntity createAccount() {
        OptionsEntity options = createOptions();
        AccountEntity account = AccountEntity.builder().name("CryptoFin Peer2Peer Settlement Acct").description("For all peer to peer transfers")
            .monetaryUnit(com.peoplestrust.account.persistence.entity.MonetaryUnit.CAD).options(options).status(AccountStatus.ACTIVE)
            .refId(UUID.fromString(accountRefId)).profileId(UUID.fromString(profileRefId)).build();
        return account;
    }

    private OptionsEntity createOptions() {
        OptionsEntity options = OptionsEntity.builder().overdraftAmount(BigDecimal.valueOf(100000)).fundHoldDays(5)
            .build();
        return options;
    }

    @Override
    public void doCleanUpAfterTest() {

    }
}
