package com.peoplestrust.transaction.async.api.v1.service;

import com.peoplestrust.account.persistence.entity.AccountEntity;
import com.peoplestrust.account.persistence.entity.AccountStatus;
import com.peoplestrust.account.persistence.entity.OptionsEntity;
import com.peoplestrust.account.persistence.repository.read.ReadAccountRepository;
import com.peoplestrust.transaction.async.api.v1.AsyncTransactionApplication;
import com.peoplestrust.transaction.async.api.v1.config.AsyncTransactionProperty;
import com.peoplestrust.transaction.async.api.v1.config.KafkaConfig;
import com.peoplestrust.transaction.async.api.v1.model.AsyncPaymentCategoryType;
import com.peoplestrust.transaction.async.api.v1.model.AsyncPaymentRailType;
import com.peoplestrust.transaction.async.api.v1.model.Instruction;
import com.peoplestrust.transaction.async.api.v1.model.Transaction;
import com.peoplestrust.util.api.common.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import net.bytebuddy.utility.RandomString;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.kafka.core.KafkaAdmin;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.util.concurrent.SettableListenableFuture;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@SpringBootTest
@ActiveProfiles("test")
@ContextConfiguration(classes = AsyncTransactionApplication.class)
@ExtendWith(SpringExtension.class)
@Slf4j
public class ReverseTransactionAsyncTestIT {

  String accountId = UUID.randomUUID().toString();
  String interactionId = UUID.randomUUID().toString();
  private static String profileId = UUID.randomUUID().toString();

  @Autowired
  AsyncTransactionServiceImpl asyncTransactionService;

  @MockBean
  ReadAccountRepository readAccountRepository;

  @MockBean
  KafkaConfig kafkaConfig;

  @MockBean
  KafkaAdmin kafkaAdmin;

  @MockBean
  private KafkaTemplate<String, Object> kafkaTemplate;


  @Autowired
  AsyncTransactionProperty asyncTransactionProperty;


  @Test
  public void reverseTransactionAsync() throws Exception {
    Instruction instruction = buildInstructionData();
    AccountEntity accountEntity = createAccount();
    String transRefId = instruction.getTransactions().get(0).getTransactionRefId();
    when(readAccountRepository.findByRefId(any())).thenReturn(Optional.ofNullable(accountEntity));

    // Create a SettableListenableFuture and complete it
    SettableListenableFuture<SendResult<String, Object>> settableFuture = new SettableListenableFuture<>();
    settableFuture.set(new SendResult<>(new ProducerRecord<>("topic", "key", "value"), new RecordMetadata(null, 0, 0, 0, null, 0, 0)));

    // Convert it to CompletableFuture
    CompletableFuture<SendResult<String, Object>> completableFuture = new CompletableFuture<>();
    settableFuture.addCallback(completableFuture::complete, completableFuture::completeExceptionally);

    // Mock the send method to return the CompletableFuture
    when(kafkaTemplate.send(any(ProducerRecord.class))).thenReturn(completableFuture);

    Boolean response = asyncTransactionService.reverseTransaction(instruction.getInstructionRefId(), transRefId, profileId, accountId, interactionId);

    assertEquals(response, Boolean.TRUE);
  }

  private Instruction buildInstructionData() {
    String word = "TEST_INST" + RandomString.make(7);
    List<Transaction> transactions = buildTransactionData();
    Instruction instruction = Instruction.builder().instructionRefId(word).paymentRail(AsyncPaymentRailType.ETRANSFER).transactions(transactions).build();
    return instruction;
  }

  private List<Transaction> buildTransactionData() {
    List<Transaction> list = new ArrayList<>();
    Transaction t1 = Transaction.builder().transactionRefId(UUID.randomUUID().toString())
        .paymentCategory(AsyncPaymentCategoryType.COMPLETE_PAYMENT).amount(new BigDecimal(100)).monetaryUnit("CAD")
        .acceptanceDateTime(DateUtils.offsetDateTime())
        .dueDateTime(DateUtils.offsetDateTime().plusDays(2)).build();

    Transaction t2 = Transaction.builder().transactionRefId(UUID.randomUUID().toString())
        .paymentCategory(AsyncPaymentCategoryType.SEND_PAYMENT).amount(new BigDecimal(100)).monetaryUnit("CAD")
        .acceptanceDateTime(DateUtils.offsetDateTime())
        .build();

    list.add(t1);
    list.add(t2);
    return list;
  }

  private AccountEntity createAccount() {
    OptionsEntity options = createOptions();
    AccountEntity account = AccountEntity.builder().name("CryptoFin Peer2Peer Settlement Acct").description("For all peer to peer transfers")
        .monetaryUnit(com.peoplestrust.account.persistence.entity.MonetaryUnit.CAD).options(options).status(AccountStatus.ACTIVE)
        .refId(UUID.fromString(accountId)).profileId(UUID.fromString(profileId)).build();
    return account;
  }

  private OptionsEntity createOptions() {
    OptionsEntity options = OptionsEntity.builder().overdraftAmount(BigDecimal.valueOf(100000)).fundHoldDays(5)
        .build();
    return options;
  }
}
