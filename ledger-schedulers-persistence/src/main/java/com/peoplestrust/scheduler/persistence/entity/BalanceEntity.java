package com.peoplestrust.scheduler.persistence.entity;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Data
@Entity
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Table(name = "balance")
public class BalanceEntity extends DomainEntityTimeStamps {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id", unique = true, nullable = false)
  private Integer id;

  @NotNull
  @Column(name = "profile_ref_id",columnDefinition = "uuid")
  private UUID profileRefId;

  @NotNull
  @Column(name = "account_ref_id",columnDefinition = "uuid")
  private UUID accountRefId;

  @Column(name = "total_amount_credit")
  private BigDecimal totalAmountCredit;


  @Column(name = "total_amount_debit")
  private BigDecimal totalAmountDebit;


  @Column(name = "total_amount")
  private BigDecimal totalAmount;


  @Column(name = "total_reserve_amount")
  private BigDecimal totalReserveAmount;

  @Enumerated(EnumType.STRING)
  @Column(name = "monetary_unit")
  private MonetaryUnit monetaryUnit;

  @Column(name = "effective_to_date_time")
  private LocalDateTime effectiveToDateTime;


  @Column(name = "effective_from_date_time")
  private LocalDateTime effectiveFromDateTime;

  @Column(name = "total_pending_amount", precision = 18, scale = 2, nullable = false)
  private BigDecimal totalPendingAmount = BigDecimal.ZERO;
}

