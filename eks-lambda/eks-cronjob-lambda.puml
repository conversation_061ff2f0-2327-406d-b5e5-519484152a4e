@startuml
skinparam backgroundColor #FFFFFF

title AWS S3 → Lambda (Container) → EKS CronJob Manual Trigger

node "S3 Bucket" as s3
node "Lambda Function\n(container image)" as lambda
database "ECR Repository" as ecr

cloud "EKS Cluster\n(gl-eks-main-devqa)" as eks {
    node "CronJob\netransfer-recon-cronjob" as cronjob
    node "Job\netransfer-recon-cronjob-*" as job
}

s3 --> lambda : ObjectCreated\n(event trigger)
ecr ..> lambda : Container image\npull at deploy
lambda --> cronjob : Kubernetes API\n(create Job)
cronjob --> job : Spawn Job\nfrom template

@enduml
