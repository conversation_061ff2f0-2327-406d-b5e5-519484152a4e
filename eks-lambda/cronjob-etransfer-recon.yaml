apiVersion: batch/v1
kind: CronJob
metadata:
  name: etransfer-recon
  namespace: pg-ledger-qa
spec:
  schedule: "*/20 * * * *"  # runs every 5 minutes
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: test
              image: busybox
              args:
                - /bin/sh
                - -c
                - echo "CronJob executed successfully"; sleep 10
          restartPolicy: OnFailure
