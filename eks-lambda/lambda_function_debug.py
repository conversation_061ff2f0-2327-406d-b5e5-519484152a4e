import json
import logging
import os
import boto3

# Force logging to work
logging.basicConfig(level=logging.INFO, force=True)
logger = logging.getLogger()
logger.setLevel(logging.INFO)

def lambda_handler(event, context):
  """Minimal debug version to identify the issue."""

  print("=== LAMBDA STARTED ===")
  logger.info("=== LAMBDA STARTED ===")

  try:
    print("Step 1: Testing basic functionality")
    logger.info("Step 1: Testing basic functionality")

    # Test environment variables
    cluster_name = os.environ.get("CLUSTER_NAME", "gl-eks-main-devqa")
    region = os.environ.get("AWS_REGION", "ca-central-1")
    namespace = os.environ.get("NAMESPACE", "pg-ledger-qa")
    cronjob_name = os.environ.get("CRONJOB_NAME", "etransfer-recon")

    print(f"Environment - Cluster: {cluster_name}, Region: {region}, Namespace: {namespace}")
    logger.info(f"Environment - Cluster: {cluster_name}, Region: {region}, Namespace: {namespace}")

    print("Step 2: Testing AWS STS")
    logger.info("Step 2: Testing AWS STS")

    # Test AWS STS
    sts = boto3.client('sts')
    identity = sts.get_caller_identity()
    actual_arn = identity.get('Arn')
    expected_arn = "arn:aws:iam::799455639446:role/service-role/eks-cron-job-2-qa-role-2xbasg46"

    print(f"Actual ARN: {actual_arn}")
    print(f"Expected ARN: {expected_arn}")
    print(f"ARNs match: {actual_arn == expected_arn}")

    logger.info(f"Actual ARN: {actual_arn}")
    logger.info(f"Expected ARN: {expected_arn}")
    logger.info(f"ARNs match: {actual_arn == expected_arn}")

    print("Step 3: Testing subprocess")
    logger.info("Step 3: Testing subprocess")

    # Test subprocess
    import subprocess
    result = subprocess.run(["aws", "--version"], capture_output=True, text=True, timeout=10)
    print(f"AWS CLI version: {result.stdout.strip()}")
    logger.info(f"AWS CLI version: {result.stdout.strip()}")

    print("Step 4: Testing kubeconfig creation")
    logger.info("Step 4: Testing kubeconfig creation")

    # Test kubeconfig creation
    config_file_path = "/tmp/.kube/config"
    kube_dir = os.path.dirname(config_file_path)
    os.makedirs(kube_dir, exist_ok=True)

    os.environ['AWS_DEFAULT_REGION'] = region

    result = subprocess.run([
      "aws", "eks", "update-kubeconfig",
      "--name", cluster_name,
      "--region", region,
      "--kubeconfig", config_file_path
    ], capture_output=True, text=True, timeout=30)

    print(f"Kubeconfig creation - Return code: {result.returncode}")
    print(f"STDOUT: {result.stdout}")
    print(f"STDERR: {result.stderr}")

    logger.info(f"Kubeconfig creation - Return code: {result.returncode}")
    logger.info(f"STDOUT: {result.stdout}")
    logger.info(f"STDERR: {result.stderr}")

    if result.returncode != 0:
      raise Exception(f"Kubeconfig creation failed: {result.stderr}")

    # Check if file exists
    if os.path.exists(config_file_path):
      file_size = os.path.getsize(config_file_path)
      print(f"Kubeconfig file created, size: {file_size} bytes")
      logger.info(f"Kubeconfig file created, size: {file_size} bytes")
    else:
      raise Exception("Kubeconfig file was not created")

    print("Step 5: Testing Kubernetes client import")
    logger.info("Step 5: Testing Kubernetes client import")

    # Test Kubernetes import
    from kubernetes import client, config

    print("Step 6: Loading kubeconfig")
    logger.info("Step 6: Loading kubeconfig")

    config.load_kube_config(config_file=config_file_path)

    print("Step 7: Testing Kubernetes API")
    logger.info("Step 7: Testing Kubernetes API")

    # Test version API
    version_api = client.VersionApi()
    version_info = version_api.get_code()

    print(f"Kubernetes version: {version_info.git_version}")
    logger.info(f"Kubernetes version: {version_info.git_version}")

    print("=== ALL TESTS PASSED ===")
    logger.info("=== ALL TESTS PASSED ===")

    return {
      "statusCode": 200,
      "body": json.dumps({
        "message": "Debug tests completed successfully",
        "arn_match": actual_arn == expected_arn,
        "k8s_version": version_info.git_version
      })
    }

  except Exception as e:
    print(f"ERROR: {str(e)}")
    logger.error(f"ERROR: {str(e)}")
    logger.exception("Full exception:")

    return {
      "statusCode": 500,
      "body": json.dumps({
        "error": str(e),
        "type": type(e).__name__
      })
    }
