FROM public.ecr.aws/lambda/python:3.12

# Debug: Check what package managers are available
RUN echo "=== Checking available package managers ===" && \
    ls -la /usr/bin/ | grep -E "(yum|dnf|microdnf|apt)" || echo "Standard package managers not found" && \
    cat /etc/os-release || echo "OS release info not available"

# Install minimal dependencies - avoiding curl
RUN set -e && \
    # Try microdnf first (Amazon Linux 2023)
    if command -v microdnf >/dev/null 2>&1; then \
        echo "Using microdnf" && \
        microdnf update -y && \
        microdnf install -y wget unzip tar gzip; \
    # Try yum (Amazon Linux 2)
    elif command -v yum >/dev/null 2>&1; then \
        echo "Using yum" && \
        yum update -y && \
        yum install -y wget unzip tar gzip; \
    # Try dnf (Fedora/RHEL 8+)
    elif command -v dnf >/dev/null 2>&1; then \
        echo "Using dnf" && \
        dnf update -y && \
        dnf install -y wget unzip tar gzip; \
    else \
        echo "No supported package manager found" && exit 1; \
    fi

# Install AWS CLI v2 using wget instead of curl
RUN wget "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -O "awscliv2.zip" && \
    unzip awscliv2.zip && \
    ./aws/install && \
    rm -rf awscliv2.zip aws

# Install kubectl using wget (using specific version to avoid issues)
RUN wget "https://dl.k8s.io/release/v1.28.0/bin/linux/amd64/kubectl" -O kubectl && \
    chmod +x kubectl && \
    mv kubectl /usr/local/bin/

# Verify installations
RUN aws --version && kubectl version --client

# Copy requirements and install Python dependencies
COPY requirements.txt ${LAMBDA_TASK_ROOT}
RUN pip install -r requirements.txt

# Copy function code
COPY lambda_function.py ${LAMBDA_TASK_ROOT}

CMD ["lambda_function.lambda_handler"]
