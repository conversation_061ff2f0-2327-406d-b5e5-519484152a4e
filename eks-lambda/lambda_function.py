import json
import logging
import os
import subprocess
import random
from kubernetes import client, config
from kubernetes.client.rest import ApiException

# Configure logging
logging.basicConfig(level=logging.INFO, force=True)
logger = logging.getLogger()
logger.setLevel(logging.INFO)

def lambda_handler(event, context):
  """
  Lambda function to trigger a Kubernetes CronJob manually.
  """
  logger.info("=== Starting CronJob Trigger Lambda ===")
  try:
    # Get configuration from environment variables
    cluster_name = os.environ.get("CLUSTER_NAME", "gl-eks-main-devqa")
    region = os.environ.get("LAMBDA_REGION", "ca-central-1")
    namespace = os.environ.get("NAMESPACE", "pg-ledger-qa")
    cronjob_name = os.environ.get("JOB_NAME", "etransfer-recon-cronjob")
    logger.info(f"Configuration - Cluster: {cluster_name}, Region: {region}, Namespace: {namespace}, CronJob: {cronjob_name}")

    # Create kubeconfig
    config_file_path = "/tmp/.kube/config"
    kube_dir = os.path.dirname(config_file_path)
    os.makedirs(kube_dir, exist_ok=True)
    os.environ['AWS_DEFAULT_REGION'] = region
    logger.info("Creating kubeconfig...")
    result = subprocess.run([
      "aws", "eks", "update-kubeconfig",
      "--name", cluster_name,
      "--region", region,
      "--kubeconfig", config_file_path
    ], capture_output=True, text=True, timeout=30)
    if result.returncode != 0:
      raise Exception(f"Failed to create kubeconfig: {result.stderr}")
    logger.info("Kubeconfig created successfully")

    # Load Kubernetes configuration
    config.load_kube_config(config_file=config_file_path)

    # Create Kubernetes API clients
    batch_v1 = client.BatchV1Api()
    logger.info(f"Fetching CronJob '{cronjob_name}' from namespace '{namespace}'...")

    # Get the CronJob
    try:
      cronjob = batch_v1.read_namespaced_cron_job(name=cronjob_name, namespace=namespace)
      logger.info(f"Found CronJob: {cronjob.metadata.name}")
    except ApiException as e:
      if e.status == 404:
        raise Exception(f"CronJob '{cronjob_name}' not found in namespace '{namespace}'")
      else:
        raise Exception(f"Error fetching CronJob: {e}")

    # Create a Job from the CronJob template
    job_name = f"{cronjob_name}-{random.randint(1, 1000000000)}"
    logger.info(f"Creating one-off Job '{job_name}' from Job template...")

    # Extract the pod template spec from the CronJob
    pod_template_spec = cronjob.spec.job_template.spec.template.spec

    # Extract containers with ALL their properties
    containers = []
    for container in pod_template_spec.containers:
      container_dict = {
        "name": container.name,
        "image": container.image,
      }

      if container.command:
        container_dict["command"] = container.command

      if container.args:
        container_dict["args"] = container.args

      # IMPORTANT: Properly preserve environment variables including secretKeyRef
      if container.env:
        env_vars = []
        for env in container.env:
          env_var = {"name": env.name}

          if env.value is not None:
            env_var["value"] = env.value
          elif env.value_from:
            value_from = {}
            if env.value_from.secret_key_ref:
              value_from["secretKeyRef"] = {
                "name": env.value_from.secret_key_ref.name,
                "key": env.value_from.secret_key_ref.key
              }
              if env.value_from.secret_key_ref.optional is not None:
                value_from["secretKeyRef"]["optional"] = env.value_from.secret_key_ref.optional
            elif env.value_from.config_map_key_ref:
              value_from["configMapKeyRef"] = {
                "name": env.value_from.config_map_key_ref.name,
                "key": env.value_from.config_map_key_ref.key
              }
              if env.value_from.config_map_key_ref.optional is not None:
                value_from["configMapKeyRef"]["optional"] = env.value_from.config_map_key_ref.optional
            elif env.value_from.field_ref:
              value_from["fieldRef"] = {
                "fieldPath": env.value_from.field_ref.field_path
              }
              if env.value_from.field_ref.api_version:
                value_from["fieldRef"]["apiVersion"] = env.value_from.field_ref.api_version
            elif env.value_from.resource_field_ref:
              value_from["resourceFieldRef"] = {
                "resource": env.value_from.resource_field_ref.resource
              }
              if env.value_from.resource_field_ref.container_name:
                value_from["resourceFieldRef"]["containerName"] = env.value_from.resource_field_ref.container_name
              if env.value_from.resource_field_ref.divisor:
                value_from["resourceFieldRef"]["divisor"] = env.value_from.resource_field_ref.divisor

            if value_from:
              env_var["valueFrom"] = value_from

          env_vars.append(env_var)

        container_dict["env"] = env_vars
        logger.info(f"Preserved {len(env_vars)} environment variables for container {container.name}")

      if container.resources:
        resources_dict = {}
        if container.resources.limits:
          resources_dict["limits"] = container.resources.limits
        if container.resources.requests:
          resources_dict["requests"] = container.resources.requests
        if resources_dict:
          container_dict["resources"] = resources_dict

      containers.append(container_dict)

    # Build the pod spec, preserving important fields from the CronJob
    pod_spec = {
      "containers": containers,
      "restartPolicy": "Never"  # Required for Jobs (CronJob had "OnFailure" but Jobs need "Never")
    }

    # Preserve ServiceAccount if it exists in the CronJob
    if hasattr(pod_template_spec, 'service_account_name') and pod_template_spec.service_account_name:
      pod_spec["serviceAccountName"] = pod_template_spec.service_account_name
      logger.info(f"Using ServiceAccount: {pod_template_spec.service_account_name}")
    elif hasattr(pod_template_spec, 'service_account') and pod_template_spec.service_account:
      pod_spec["serviceAccount"] = pod_template_spec.service_account
      logger.info(f"Using ServiceAccount: {pod_template_spec.service_account}")
    else:
      logger.info("No ServiceAccount specified in CronJob, will use 'default'")

    # Build a clean job manifest
    job_manifest = {
      "apiVersion": "batch/v1",
      "kind": "Job",
      "metadata": {
        "name": job_name,
        "namespace": namespace,
        "labels": {
          "triggered-by": "lambda",
          "source-cronjob": cronjob_name,
          "app": "etransfer-recon-cronjob-v1",
          "domain": "general-ledger"
        },
        "annotations": {
          "job.kubernetes.io/instantiate": "manual",
          "lambda-request-id": context.aws_request_id
        }
      },
      "spec": {
        "template": {
          "metadata": {
            "labels": {
              "job-name": job_name,
              "app": "etransfer-recon-cronjob-v1",
              "domain": "general-ledger"
            }
          },
          "spec": pod_spec
        },
        "backoffLimit": 0  # Don't retry on failure
      }
    }

    # Create the Job
    try:
      created_job = batch_v1.create_namespaced_job(namespace=namespace, body=job_manifest)
      logger.info(f"Successfully created Job: {created_job.metadata.name}")

      return {
        "statusCode": 200,
        "body": json.dumps({
          "message": f"Successfully triggered CronJob '{cronjob_name}' as Job '{job_name}'",
          "job": job_name,
          "namespace": namespace,
          "cluster": cluster_name
        })
      }
    except ApiException as e:
      logger.error(f"Kubernetes API Error: {e}")
      raise Exception(f"Failed to create Job: {e}")
  except Exception as e:
    logger.error(f"Error: {str(e)}")
    logger.exception("Full exception details:")
    return {
      "statusCode": 500,
      "body": json.dumps({
        "error": str(e),
        "type": type(e).__name__
      })
    }
