package com.peoplestrust.transaction.async.listener.service;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.times;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.peoplestrust.transaction.async.domain.model.InitiateLedgerTransactionAsyncRequest;
import com.peoplestrust.transaction.async.listener.config.AsyncTransactionListenerProperty;
import com.peoplestrust.transaction.async.listener.mapper.AsyncTransactionListenerMapper;
import com.peoplestrust.transaction.async.listener.model.AsyncPaymentCategoryType;
import com.peoplestrust.transaction.async.listener.model.AsyncPaymentRailType;
import com.peoplestrust.transaction.async.listener.model.Instruction;
import com.peoplestrust.transaction.async.listener.model.Transaction;
import com.peoplestrust.transaction.domain.model.InitiateLedgerTransactionResponse;
import com.peoplestrust.transaction.domain.model.InstructionStatus;
import com.peoplestrust.transaction.domain.model.PaymentRail;
import com.peoplestrust.util.api.common.config.APICommonUtilConstant;
import com.peoplestrust.util.api.common.util.DateUtils;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import lombok.extern.slf4j.Slf4j;
import net.bytebuddy.utility.RandomString;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.common.header.internals.RecordHeaders;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.web.client.RestTemplate;

@ExtendWith(MockitoExtension.class)
@Slf4j
public class InitiateInstructionAsyncListenerServiceTest {

  @InjectMocks
  private AsyncTransactionListenerServiceImpl asyncTransactionService;

  @Mock
  private KafkaTemplate kafkaTemplate;

  @Mock
  RestTemplate restTemplate;

  @Mock
  AsyncTransactionListenerMapper asyncTransactionListenerMapper;

  @Mock  // Remove the duplicate - keep only one
  AsyncTransactionListenerProperty asyncTransactionListenerProperty;

  @Mock
  ObjectMapper objectMapper;

  String accountId = UUID.randomUUID().toString();
  String interactionId = UUID.randomUUID().toString();
  private static String profileId = UUID.randomUUID().toString();

  @Test
  public void initiateInstructionTest() throws Exception {
    Instruction instruction = buildInstructionData();
    ResponseEntity<InitiateLedgerTransactionResponse> initiateLedgerTransactionResponse = buildInitiateLedgerTransactionResponse(instruction);
    String topic = "pending_transaction";
    String key = "INIT|" + instruction.getInstructionRefId();
    ConsumerRecord<String, Instruction> record = new ConsumerRecord<>(topic, 0, 123L, key, instruction);
    RecordHeaders headers = new RecordHeaders();
    headers.add(APICommonUtilConstant.HEADER_PROFILE_ID, profileId.getBytes());
    headers.add(APICommonUtilConstant.HEADER_ACCOUNT_ID, accountId.getBytes());
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_ID, interactionId.getBytes());
    headers.add(APICommonUtilConstant.HEADER_OPERATION_ID, APICommonUtilConstant.INITIATE.getBytes());

    // Use lenient mocking to handle all possible configurations
    lenient().when(asyncTransactionListenerProperty.getDeadLetterTopic()).thenReturn("transaction_failed_test");
    lenient().when(asyncTransactionListenerProperty.getDeadLetterErrorTopic()).thenReturn("transaction_error_test");
    lenient().when(asyncTransactionListenerProperty.getKafkaTopic()).thenReturn("kafka_topic_test");
    lenient().when(asyncTransactionListenerProperty.getKafkaTopicV2()).thenReturn("kafka_topic_v2_test");
    lenient().when(asyncTransactionListenerProperty.getInitiateTransactionUrl()).thenReturn("http://test-initiate-url");
    lenient().when(asyncTransactionListenerProperty.getCommitTransactionUrl()).thenReturn("http://test-commit-url");
    lenient().when(asyncTransactionListenerProperty.getRollbackTransactionUrl()).thenReturn("http://test-rollback-url");
    lenient().when(asyncTransactionListenerProperty.getReverseTransactionUrl()).thenReturn("http://test-reverse-url");

    // Mock the REST call
    when(restTemplate.exchange((String) any(), (HttpMethod) any(), (HttpEntity<?>) any(), (Class<InitiateLedgerTransactionResponse>) any()))
        .thenReturn(initiateLedgerTransactionResponse);

    // Mock Kafka template
    CompletableFuture<SendResult<String, Object>> future = mock(CompletableFuture.class);
    when(kafkaTemplate.send((ProducerRecord) any())).thenReturn(future);

    // Execute the test
    asyncTransactionService.initiateTransaction(record, headers, key);

    // Verify the REST call was made
    verify(restTemplate, times(1)).exchange((String) any(), (HttpMethod) any(), (HttpEntity<?>) any(), (Class<InitiateLedgerTransactionResponse>) any());
  }

  private InitiateLedgerTransactionAsyncRequest buildInitiateLedgerTransactionAsyncRequest(Instruction instruction) {
    InitiateLedgerTransactionAsyncRequest request = new InitiateLedgerTransactionAsyncRequest();
    request.setInstructionRefId(instruction.getInstructionRefId());
    request.setPaymentRail(com.peoplestrust.transaction.async.domain.model.PaymentRail.valueOf("ETRANSFER"));
    request.setTransactions(null);
    return request;
  }

  private ResponseEntity<InitiateLedgerTransactionResponse> buildInitiateLedgerTransactionResponse(Instruction instruction) {
    InitiateLedgerTransactionResponse initiateLedgerTransactionResponse = new InitiateLedgerTransactionResponse();

    initiateLedgerTransactionResponse.setInstructionRefId(instruction.getInstructionRefId());
    initiateLedgerTransactionResponse.setStatus(InstructionStatus.FAILED);
    initiateLedgerTransactionResponse.setPaymentRail(PaymentRail.valueOf("ETRANSFER"));
    initiateLedgerTransactionResponse.setTransactions(null);

    ResponseEntity<InitiateLedgerTransactionResponse> initiateLedgerTransactionResponse1 = new ResponseEntity<InitiateLedgerTransactionResponse>(
        initiateLedgerTransactionResponse,
        HttpStatus.OK);

    return initiateLedgerTransactionResponse1;
  }

  private Instruction buildInstructionData() {
    String word = "TEST_INST" + RandomString.make(7);
    List<Transaction> transactions = buildTransactionData();
    Instruction instruction = Instruction.builder().instructionRefId(word).paymentRail(AsyncPaymentRailType.ETRANSFER).transactions(transactions).build();
    return instruction;
  }

  private List<Transaction> buildTransactionData() {
    List<Transaction> list = new ArrayList<>();
    Transaction t1 = Transaction.builder().transactionRefId(UUID.randomUUID().toString())
        .paymentCategory(AsyncPaymentCategoryType.COMPLETE_PAYMENT).amount(new BigDecimal(100)).monetaryUnit("CAD")
        .acceptanceDateTime(DateUtils.offsetDateTime())
        .dueDateTime(DateUtils.offsetDateTime().plusDays(2)).build();

    Transaction t2 = Transaction.builder().transactionRefId(UUID.randomUUID().toString())
        .paymentCategory(AsyncPaymentCategoryType.SEND_PAYMENT).amount(new BigDecimal(100)).monetaryUnit("CAD")
        .acceptanceDateTime(DateUtils.offsetDateTime())
        .build();

    list.add(t1);
    list.add(t2);
    return list;
  }
}