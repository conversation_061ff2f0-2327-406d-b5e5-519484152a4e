import boto3
import json
from datetime import datetime, timedelta
from time import sleep, mktime

import pytz

# Configuration for different environments
config_stg = {
    "profile_name": "NON_LEDGER",
    "log_group": "/non-prod/staging/general-ledger/eks/gl-eks-main-staging/application",
    "kubernetes_namespace": "pg-ledger-staging"
}

config = {
    "profile_name": "PROD_LEDGER",
    "log_group": "/production/prod/general-ledger/eks/gl-eks-main-prod/application",
    "kubernetes_namespace": "pg-ledger-prod"
}

def convert_epoch_to_us_east(epoch_ms):
    # Convert the epoch time (in milliseconds) to a timezone-aware datetime object in UTC
    dt_utc = datetime.fromtimestamp(epoch_ms / 1000.0, pytz.UTC)

    # Define the US Eastern Time Zone
    eastern = pytz.timezone('US/Eastern')

    # Convert the UTC datetime to US Eastern Time
    dt_us_east = dt_utc.astimezone(eastern)

    return dt_us_east

def date_to_epoch(date_string, end_day=False):
    # Convert the date string to a datetime object
    dt_obj = datetime.strptime(date_string, '%Y-%m-%d')
    
    # If end_day is True, adjust to the end of the day
    if end_day:
        dt_obj = dt_obj + timedelta(days=1, microseconds=-1)
    
    # Convert the datetime object to epoch (in milliseconds)
    epoch = int(mktime(dt_obj.timetuple())) * 1000
    return epoch

def run_cloudwatch_query(log_group_name, start_time, end_time, query_string):
    try:
        # Create a session using the configured profile
        session = boto3.Session(profile_name=config["profile_name"])

        # Create the CloudWatch Logs client using the session
        client = session.client('logs')

        # Modify the query to use the configured namespace
        modified_query_string = query_string.replace("{{namespace}}", config["kubernetes_namespace"])

        # Start the query
        response = client.start_query(
            logGroupName=log_group_name,
            startTime=start_time,
            endTime=end_time,
            queryString=modified_query_string
        )

        query_id = response['queryId']

        # Wait for the query to complete
        sleep(10)  # Adjust as necessary

        # Get the query results
        results = client.get_query_results(queryId=query_id)

        return results['results']
    except Exception as e:
        print("An error occurred: ", e)
        return None

def execute_query_and_save_results(query, filename):
    results = run_cloudwatch_query(config["log_group"], start_epoch_time, end_epoch_time, query)
    # Print the first few elements to inspect their structure
    # print("First few results:", results[:5])  # Adjust the number as needed
    with open(filename, 'w') as f:
        json.dump(results, f, indent=4)

# Calculate the current time
current_time = datetime.now()

# Calculate the start time, which is 12 hours before the current time
start_time = current_time - timedelta(hours=12)

# Convert start_time and current_time to epoch times
start_epoch_time = int(start_time.timestamp()) * 1000
end_epoch_time = int(current_time.timestamp()) * 1000

# print("start time="+ str(start_epoch_time)+" end time="+str(end_epoch_time))
start_us_east = convert_epoch_to_us_east(start_epoch_time)
end_us_east = convert_epoch_to_us_east(end_epoch_time)

print("start time="+ start_us_east.strftime('%Y-%m-%d %H:%M:%S') + " end time=" + end_us_east.strftime('%Y-%m-%d %H:%M:%S'))

# Log group and query
log_group = config["log_group"]

query_responseTime = '''fields @timestamp, log
        | filter kubernetes.labels.app="transaction-v1"
        | filter kubernetes.namespace_name="{{namespace}}"
        | parse log "* * * [*] [*]-[*] * - *" as pg_date, pg_time, pg_log_level, pg_sa_id, pg_interaction_id, pg_thread_id, pg_class, pg_message
        | filter pg_class="PerfLogger"
        | parse pg_message "* | * | * | * ms" as perf_method, perf_start_date, perf_end_date, perf_elapsed
        | filter perf_method like 'TransactionController'
        | stats average(perf_elapsed) as average_response_time, min(perf_elapsed) as minimum_response_time, max(perf_elapsed) as maximum_response_time by bin(60s)
        | sort maximum_response_time desc
        | limit 10000'''

# Execute and visualize
print("generate report for response time")
execute_query_and_save_results(query_responseTime, 'response.json')

# Print the first few elements to inspect their structure
# print("First few results:", results[:5])  # Adjust the number as needed

query_rps = '''fields @timestamp, @log
        | filter kubernetes.labels.app="transaction-v1"
        | filter kubernetes.namespace_name="{{namespace}}"
        | parse log "* * * [*] [*]-[*] * - *" as pg_date, pg_time, pg_log_level, pg_sa_id, pg_interaction_id, pg_thread_id, pg_class, pg_message
        | filter pg_class = 'APIPayloadLogger'
        | filter pg_message like '| REQUEST |'
        | parse pg_message "* | REQUEST | method=*, url=*, headers=[*], body=*" as pg_req_api, pg_req_method, pg_req_url, pg_req_headers, pg_req_body
        | filter pg_req_method = 'POST' OR pg_req_method = 'PATCH'
        | stats count(*) as all, sum(pg_req_method like 'POST') as initiate, sum(pg_req_method like 'PATCH') as commit by bin(1s)
        | sort by all desc
        | limit 10000'''

print("generate report for rps")
execute_query_and_save_results(query_rps, 'rps.json')