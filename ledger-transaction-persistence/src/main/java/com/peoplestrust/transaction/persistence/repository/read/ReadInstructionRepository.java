package com.peoplestrust.transaction.persistence.repository.read;

import com.peoplestrust.transaction.persistence.entity.InstructionEntity;
import com.peoplestrust.transaction.persistence.entity.PaymentRailType;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Repository
public interface ReadInstructionRepository extends JpaRepository<InstructionEntity, Integer> {

  /**
   * Finds instruction using profileRefId, accountRefId, and instructionRefId
   *
   * @param profileRefId     profile reference ID (unique) that identifies the profile
   * @param accountRefId     account reference ID (unique) that identifies the account
   * @param instructionRefId instruction reference ID (unique) that identifies the instruction
   * @return
   */
  InstructionEntity findByProfileRefIdAndAccountRefIdAndInstructionRefId(UUID profileRefId, UUID accountRefId, String instructionRefId);


  /**
   * Find instruction using profile reference id and account reference id
   *
   * @param profileRefId unique id that identifies profile
   * @param accountRefId unique id that identifies account
   * @return
   */
  List<InstructionEntity> findByProfileRefIdAndAccountRefId(UUID profileRefId, UUID accountRefId);

  Slice<InstructionEntity> findByProfileRefIdAndAccountRefId(UUID profileRefId,
      UUID accountRefId,
      Pageable pageable);

  List<InstructionEntity> findByProfileRefIdAndPaymentRail(UUID profileRefId, PaymentRailType paymentRailType, Pageable pageable);

  Slice<InstructionEntity> findByProfileRefIdAndAccountRefIdAndPaymentRail(UUID profileRefId,
      UUID accountRefId,
      PaymentRailType paymentRailType,
      Pageable pageable);

  /**
   * Finds instructions that have at least one associated transaction with a 'PENDING' status and a creation date older than the specified deadline.
   */
  @Query("select distinct i from InstructionEntity i join i.transactions t " +
      "where t.status = 'PENDING' and t.createdDateTime < :rollbackStartTime")
  List<InstructionEntity> findRollbackInstructions(
      @Param("rollbackStartTime") LocalDateTime rollbackStartTime,
      Pageable pageable
  );

  List<InstructionEntity> findTop25ByAccountRefIdAndProfileRefIdOrderByCreatedDateTimeDesc(UUID accountRefId, UUID profileRefId);

  Slice<InstructionEntity> findByProfileRefIdAndAccountRefIdAndCreatedDateTimeAfter(UUID profileRefId, UUID accountRefId, LocalDateTime startDate, Pageable pageable);

  Slice<InstructionEntity> findByProfileRefIdAndAccountRefIdAndCreatedDateTimeBefore(UUID profileRefId, UUID accountRefId, LocalDateTime endDate, Pageable pageable);

  Slice<InstructionEntity> findByProfileRefIdAndAccountRefIdAndPaymentRailAndCreatedDateTimeAfter(UUID profileRefId, UUID accountRefId, PaymentRailType paymentRail, LocalDateTime startDate,
      Pageable pageable);

  Slice<InstructionEntity> findByProfileRefIdAndAccountRefIdAndPaymentRailAndCreatedDateTimeBefore(UUID profileRefId, UUID accountRefId, PaymentRailType paymentRail, LocalDateTime endDate,
      Pageable pageable);

  Slice<InstructionEntity> findByProfileRefIdAndAccountRefIdAndInstructionRefIdAndPaymentRail(UUID profileRefId,
      UUID accountRefId,
      String instructionRefId,
      PaymentRailType paymentRailType,
      Pageable pageable);

  Slice<InstructionEntity> findByProfileRefIdAndAccountRefIdAndInstructionRefIdAndPaymentRailAndCreatedDateTimeBetween(UUID profileRefId,
      UUID accountRefId,
      String instructionRefId,
      PaymentRailType paymentRailType,
      LocalDateTime startTime,
      LocalDateTime endTime,
      Pageable pageable);

  Slice<InstructionEntity> findByProfileRefIdAndAccountRefIdAndPaymentRailAndCreatedDateTimeBetween(UUID profileRefId,
      UUID accountRefId,
      PaymentRailType paymentRailType,
      LocalDateTime startTime,
      LocalDateTime endTime,
      Pageable pageable);

  Slice<InstructionEntity> findByProfileRefIdAndAccountRefIdAndCreatedDateTimeBetween(UUID profileRefId,
      UUID accountRefId,
      LocalDateTime startTime,
      LocalDateTime endTime,
      Pageable pageable);

  Slice<InstructionEntity> findByProfileRefIdAndAccountRefIdAndInstructionRefIdAndCreatedDateTimeBetween(UUID profileRefId,
      UUID accountRefId,
      String instructionRefId,
      LocalDateTime startTime,
      LocalDateTime endTime,
      Pageable pageable);

  Slice<InstructionEntity> findByProfileRefIdAndAccountRefIdAndInstructionRefId(UUID profileRefId,
      UUID accountRefId,
      String instructionRefId,
      Pageable pageable);


  Slice<InstructionEntity> findByProfileRefIdAndAccountRefIdAndInstructionRefIdAndPaymentRailAndCreatedDateTimeAfter(UUID profileRefId,
      UUID accountRefId,
      String instructionRefId,
      PaymentRailType paymentRailType,
      LocalDateTime startTime,
      Pageable pageable);

  Slice<InstructionEntity> findByProfileRefIdAndAccountRefIdAndInstructionRefIdAndPaymentRailAndCreatedDateTimeBefore(UUID profileRefId,
      UUID accountRefId,
      String instructionRefId,
      PaymentRailType paymentRailType,
      LocalDateTime endTime,
      Pageable pageable);

  /**
   * Returns a Slice of InstructionEntity where profileRefId = ?1, accountRefId = ?2, instructionRefId = ?3, and createdDateTime is **after** (>=) ?4.
   */
  Slice<InstructionEntity> findByProfileRefIdAndAccountRefIdAndInstructionRefIdAndCreatedDateTimeAfter(
      UUID profileRefId,
      UUID accountRefId,
      String instructionRefId,
      LocalDateTime createdDateTime,
      Pageable pageable
  );

  /**
   * Returns a Slice of InstructionEntity where profileRefId = ?1, accountRefId = ?2, instructionRefId = ?3, and createdDateTime is **before** (<=) ?4.
   */
  Slice<InstructionEntity> findByProfileRefIdAndAccountRefIdAndInstructionRefIdAndCreatedDateTimeBefore(
      UUID profileRefId,
      UUID accountRefId,
      String instructionRefId,
      LocalDateTime createdDateTime,
      Pageable pageable
  );
}