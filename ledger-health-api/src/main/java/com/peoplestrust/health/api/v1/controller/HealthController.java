package com.peoplestrust.health.api.v1.controller;

import com.peoples.banking.util.logger.annotation.PerfLogger;
import com.peoplestrust.health.api.v1.config.HealthConstant;
import com.peoplestrust.health.api.v1.config.HealthProperty;
import com.peoplestrust.health.api.v1.service.HealthService;
import com.peoplestrust.health.api.v1.validation.HealthValidation;
import com.peoplestrust.health.domain.model.HealthResponse;
import com.peoplestrust.util.api.common.config.APICommonUtilConstant;
import com.peoplestrust.util.api.common.controller.InternalAPIController;

import java.time.LocalDateTime;

import com.peoplestrust.util.api.common.util.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping(value = "/v1")
public class HealthController extends InternalAPIController {

    /**
     * Service layer.
     */
    @Autowired
    HealthService healthService;

    /**
     * Properties.
     */
    @Autowired
    HealthProperty healthProperty;

    @Autowired
    HealthValidation healthValidation;

    @Autowired
    private Environment env;

    /**
     * Controller to validate BalanceSnapshots
     *
     * @param interactionId   interaction ID header
     * @param interactionTime interaction timestamp header
     * @return response entity
     * @throws Exception
     */
    @RequestMapping(value = "/ledger/health/balance/snapshot",
            produces = MediaType.APPLICATION_JSON_VALUE,
            method = RequestMethod.GET)
    @PerfLogger
    public ResponseEntity<HealthResponse> validateBalanceSnapshots(
            @RequestHeader(name = APICommonUtilConstant.AUTHORIZATION_HEADER, required = true) String authorization,
            @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_ID, required = true) String interactionId,
            @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, required = true) String interactionTime)
            throws Exception {

        // initial headers validation
        initialCheck(interactionTime, interactionId);

        //check user group
        checkJwtTokenAndVerifyGroup(authorization, new String[]{APICommonUtilConstant.GL_ADMINISTRATION,
                APICommonUtilConstant.GL_OPERATIONS, APICommonUtilConstant.GL_SERVICE_TEAM});

        // service layer -- check balance snapShot status
        HealthResponse healthResponse = healthService.validateSnapshots();

        // response
        return new ResponseEntity<>(healthResponse, HttpStatus.OK);
    }

    @Override
    protected int getApiTimeToLiveValue() {
        return healthProperty.getTimeToLive();
    }

    private void checkJwtTokenAndVerifyGroup(String authorization, String[] checkUserGroups) {
        String disableJwt = env.getProperty(APICommonUtilConstant.ENVIRONMENT_VARIABLE_DISABLE_COGNITO_JWT_VERIFICATION);
        log.info("healthApi disableJwt:{}", disableJwt);
        if (!Boolean.parseBoolean(disableJwt)) {
            JwtUtil.isTokenValidAndVerifyGroup(authorization, checkUserGroups);
        }
    }
}
