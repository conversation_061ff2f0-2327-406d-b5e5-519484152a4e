package com.peoplestrust.health.api.v1.service;

import com.peoplestrust.health.domain.model.HealthResponse;
import com.peoplestrust.util.api.common.exception.ResourceNotFoundException;

public interface HealthService {

  /**
   * Service that returns true if a balance snapshot was valid
   * so count # of balance snapshot records taken T-1 days ago, and then ensure T (today) has equal to or more balance snapshots
   * eg. if an account had a snapshot yesterday , it should have one today
   * so we know we should have more or equal to the # of balance snapshots taken yesterday
   * @return True if profile with refId exists
   * @throws ResourceNotFoundException
   */
  HealthResponse validateSnapshots() throws Exception;
}
