package com.peoplestrust.health.api.v1.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.peoples.banking.util.logger.annotation.PerfLogger;
import com.peoplestrust.health.api.v1.config.HealthProperty;
import com.peoplestrust.health.domain.model.BalanceSnapshotStatus;
import com.peoplestrust.health.domain.model.HealthResponse;
import com.peoplestrust.scheduler.persistence.repository.read.ReadBalanceRepository;
import com.peoplestrust.util.api.common.config.APICommonUtilConstant;
import com.peoplestrust.util.api.common.exception.ExceptionUtil;
import com.peoplestrust.util.api.common.exception.InvalidFieldException;
import com.peoplestrust.util.api.common.exception.ResourceNotFoundException;
import com.peoplestrust.util.api.common.exception.ValidationException;
import com.peoplestrust.util.api.common.util.Messages;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.web.client.ResourceAccessException;

/**
 * Health service.
 */
@Slf4j
@Service
@Component
public class HealthServiceImpl implements HealthService {

  /**
   * Balance persistence repository
   */
  @Autowired
  ReadBalanceRepository readBalanceRepository;

  @Autowired
  HealthProperty healthProperty;

  private ObjectMapper objectMapper;

  /**
   * {@inheritDoc}
   */
  @PerfLogger
  @Override
  public HealthResponse validateSnapshots() throws Exception {

    HealthResponse healthResponse = null;
    try {
      LocalDateTime now = LocalDateTime.now();
      LocalDateTime yesterday = now.minusDays(1);

      // Calculate start of yesterday
      LocalDateTime startOfYesterday = yesterday.withHour(0).withMinute(0).withSecond(0)
          .withNano(0);

      // Calculate end of yesterday with the current hour and minute
      LocalDateTime endOfYesterday = yesterday.withHour(now.getHour()).withMinute(now.getMinute())
          .withSecond(0).withNano(0);

      // Calculate start of today
      LocalDateTime startOfToday = now.withHour(0).withMinute(0).withSecond(0).withNano(0);

      // Fetching snapshots count for yesterday
      Long yesterdaySnapshots = readBalanceRepository.countSnapshotsByDateTime(startOfYesterday,
          endOfYesterday);

      log.debug("validateSnapshots yesterday:{} total:{} start:{} end:{}",
          yesterday, yesterdaySnapshots, startOfYesterday, endOfYesterday);

      // Fetching snapshots count for today
      Long todaySnapshots = readBalanceRepository.countSnapshotsByDateTime(startOfToday, now);

      log.debug("validateSnapshots today:{} total:{} start:{} end:{}",
          now, todaySnapshots, startOfToday, now);

      boolean result = todaySnapshots >= yesterdaySnapshots;

      healthResponse = new HealthResponse();
      if (result) {
        healthResponse.setStatus(BalanceSnapshotStatus.SUCCESS);
      } else {
        healthResponse.setStatus(BalanceSnapshotStatus.FAILED);
      }

      log.debug("validateSnapshots yesterday:{} total:{} today:{} total:{} result:{}",
          yesterday, yesterdaySnapshots, now, todaySnapshots, result);

    } catch (DataIntegrityViolationException ex) {
      log.warn("data integrity violation on validate balance Snapshots", ex);
      throw new InvalidFieldException(ex.getCause().getCause().getMessage());
    } catch (IllegalArgumentException ex) {
      log.warn("illegal argument exception on validate balance Snapshots", ex);
      throw new InvalidFieldException(ExceptionUtil.message(ex));
    } catch (org.springframework.web.client.HttpClientErrorException ex) {
      throw new com.peoplestrust.util.api.common.exception.HttpClientErrorException(
          ex.getMessage());
    } catch (ResourceAccessException ex) {
      throw new ResourceNotFoundException(ex.getCause().getMessage());
    } catch (Exception ex) {
      log.error("unexpected exception on validate balance Snapshots", ex);
      throw new Exception(ex.getMessage());
    }
    return healthResponse;
  }

}
