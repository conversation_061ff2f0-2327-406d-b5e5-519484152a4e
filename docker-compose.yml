version: "3.5"
services:
  postgres:
    image: bitnami/postgresql:12
    restart: always
    ports:
      - '5432:5432'
    environment:
      - POSTGRESQL_REPLICATION_MODE=master
      - POSTGRESQL_REPLICATION_USER=postgres
      - POSTGRESQL_REPLICATION_PASSWORD=postgres
      - POSTGRESQL_USERNAME=postgres
      - POSTGRESQL_PASSWORD=postgres
      - POSTGRESQL_DATABASE=postgres
    networks:
      - postgres-network

  postgres-slave:
    image: bitnami/postgresql:12
    restart: always
    ports:
      - '5433:5432'
    depends_on:
      - postgres
    environment:
      - POSTGRESQL_PASSWORD=postgres
      - POSTGRESQL_MASTER_HOST=postgres
      - POSTGRESQL_REPLICATION_MODE=slave
      - POSTGRESQL_REPLICATION_USER=postgres
      - POSTGRESQL_REPLICATION_PASSWORD=postgres
      - POSTGRESQL_MASTER_PORT_NUMBER=5432
    networks:
      - postgres-network

  migrate:
    image: boxfuse/flyway:latest
    volumes:
      - ./data/postgres:/flyway/sql
    command: -url=**************************************** -user=postgres -password=postgres -driver=org.postgresql.Driver migrate -connectRetries=60
    depends_on:
      - postgres
    networks:
      - postgres-network

  redis:
    image: redis
    ports:
      - "6379:6379"
    networks:
      - redis-network

  redis-commander:
    image: rediscommander/redis-commander
    ports:
      - "9090:8081"
    environment:
      - REDIS_HOSTS=localhost:redis:6379
    networks:
      - redis-network

networks:
  postgres-network:
  redis-network:
    driver: bridge

