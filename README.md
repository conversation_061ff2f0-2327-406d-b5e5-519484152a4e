# General Ledger System

## 📋 Table of Contents
- [Overview](#overview)
- [Architecture](#architecture)
- [Technology Stack](#technology-stack)
- [Project Structure](#project-structure)
- [Prerequisites](#prerequisites)
- [Local Development Setup](#local-development-setup)
- [Building & Testing](#building--testing)
- [Deployment](#deployment)
- [Security & Compliance](#security--compliance)
- [CI/CD Pipeline](#cicd-pipeline)
- [Troubleshooting](#troubleshooting)

## 🏗 Overview

The General Ledger (GL) system is a comprehensive financial management platform designed to handle accounting transactions, account management, and financial reporting for People's Trust Company. The system follows a microservices architecture with a modern React frontend and robust backend services.

### Business Capabilities
- **Account Management**: Create, update, and manage financial accounts
- **Transaction Processing**: Handle financial transactions with audit trails
- **Profile Management**: Manage ledger profiles and configurations
- **Health Monitoring**: System health checks and monitoring
- **Scheduled Operations**: Background job processing and automation
- **Financial Reporting**: Generate reports and analytics

## 🏛 Architecture

### System Architecture
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   API Gateway   │    │   Backend       │
│   (React)       │◄──►│   (Load Bal.)   │◄──►│   Services      │
│   Port: 3000    │    │                 │    │   (Spring Boot) │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                       ┌─────────────────┐             │
                       │   Infrastructure │◄────────────┘
                       │   - PostgreSQL   │
                       │   - Redis        │
                       │   - Kafka        │
                       └─────────────────┘
```

### Microservices Architecture
- **Profile Services**: Ledger profile management (internal & external)
- **Account Services**: Account management and external account integration
- **Transaction Services**: Transaction processing (sync & async)
- **Health Services**: System monitoring and health checks
- **Scheduler Services**: Background job processing and rollback operations
- **Common Libraries**: Shared domain models, logging, and API utilities

## 🛠 Technology Stack

### Backend Services
| Component | Technology | Version | Purpose |
|-----------|------------|---------|---------|
| **Runtime** | Java | 17 | Primary programming language |
| **Framework** | Spring Boot | 3.1.12 | Microservices framework |
| **Spring Framework** | Spring | 6.1.14 | Core Spring framework |
| **Build Tool** | Maven | 3.9+ | Dependency management & build |
| **Logging** | SLF4J | 2.0.13 | Logging framework |
| **Validation** | Hibernate Validator | 8.0.1.Final | Data validation |
| **Mapping** | MapStruct | 1.4.1.Final | Object mapping |
| **Documentation** | Swagger | 1.6.6 | API documentation |
| **Testing** | JUnit | 5.9.3 | Unit testing framework |

### Frontend Application
| Component | Technology | Version | Purpose |
|-----------|------------|---------|---------|
| **Framework** | React | 19.1.0 | UI framework |
| **UI Library** | Material-UI (MUI) | 7.2.0 | Component library |
| **Routing** | React Router | 7.7.0 | Client-side routing |
| **HTTP Client** | Axios | 1.10.0 | API communication |
| **Forms** | Formik | 2.4.6 | Form management |
| **Validation** | Yup | 1.6.1 | Schema validation |
| **State Management** | React Query | 3.39.3 | Server state management |
| **Build Tool** | Create React App | 5.0.1 | Build configuration |
| **Node.js** | Node.js | 22.x | Runtime environment |

### Infrastructure & Data
| Component | Technology | Version | Purpose |
|-----------|------------|---------|---------|
| **Database** | PostgreSQL | 12 | Primary database (master-slave) |
| **Cache** | Redis | Latest | Caching and session management |
| **Message Queue** | Kafka | Latest | Event streaming |
| **Migration** | Flyway | Latest | Database migrations |
| **Containerization** | Docker | Latest | Local development |
| **Cloud Platform** | AWS | - | Production deployment |

### DevOps & Security
| Component | Technology | Purpose |
|-----------|------------|---------|
| **CI/CD** | Bitbucket Pipelines | Automated build and deployment |
| **Security Scanning** | Veracode | Software composition analysis |
| **Security Analysis** | Snyk | Vulnerability scanning |
| **Container Registry** | AWS ECR | Docker image storage |
| **Deployment** | AWS S3 | Frontend hosting |

## 📁 Project Structure

```
general-ledger/
├── frontend/                          # React frontend application
│   ├── src/                          # Source code
│   ├── public/                       # Static assets
│   ├── package.json                  # Frontend dependencies
│   └── README.md                     # Frontend-specific documentation
│
├── Backend Services/
│   ├── ledger-profile-api/           # Profile management API
│   ├── ledger-profile-domain/        # Profile domain models
│   ├── ledger-profile-persistence/   # Profile data access
│   │
│   ├── ledger-account-api/           # Account management API
│   ├── ledger-account-domain/        # Account domain models
│   ├── ledger-account-persistence/   # Account data access
│   ├── ledger-account-external-api/  # External account integration
│   │
│   ├── ledger-transaction-api/       # Transaction processing API
│   ├── ledger-transaction-domain/    # Transaction domain models
│   ├── ledger-transaction-persistence/ # Transaction data access
│   ├── ledger-transaction-async-api/ # Async transaction processing
│   ├── ledger-transaction-async-listener/ # Event listeners
│   │
│   ├── ledger-health-api/            # Health monitoring API
│   ├── ledger-health-domain/         # Health domain models
│   │
│   ├── ledger-schedulers-api/        # Background job processing
│   ├── ledger-schedulers-persistence/ # Scheduler data access
│   ├── ledger-rollback-scheduler-api/ # Rollback operations
│   │
│   ├── ledger-internal-profile-api/  # Internal profile management
│   └── ledger-system-api/            # System-level operations
│
├── Common Libraries/
│   ├── common-api/                   # Shared API utilities
│   ├── common-domain/                # Shared domain models
│   ├── common-logger/                # Logging utilities
│   ├── ledger-parent/                # Parent POM configuration
│   ├── ledger-parent-api/            # API parent configuration
│   ├── ledger-parent-domain/         # Domain parent configuration
│   └── ledger-parent-persistence/    # Persistence parent configuration
│
├── Infrastructure/
│   ├── data/                         # Database scripts and migrations
│   ├── environments/                 # Environment configurations
│   ├── docker-compose.yml            # Local development setup
│   ├── kafka-kowl.yaml              # Kafka UI configuration
│   └── cloudwatch/                  # AWS CloudWatch configurations
│
├── Development Tools/
│   ├── development/                  # Development utilities
│   ├── troubleShoot/                # Troubleshooting tools
│   ├── qa-util-api/                 # QA utilities
│   ├── data-loader-util/            # Data loading utilities
│   └── recon-etransfer-batch/       # Reconciliation batch jobs
│
├── CI/CD & Configuration/
│   ├── bitbucket-pipelines.yml      # CI/CD pipeline configuration
│   ├── buildspec_*.yml              # AWS CodeBuild specifications
│   ├── environments/scripts/         # Pipeline generation scripts
│   ├── .vscode/                     # VS Code configuration
│   └── .run/                        # IDE run configurations
│
└── Root Configuration/
    ├── pom.xml                      # Maven parent POM
    ├── package.json                 # Root package configuration
    ├── docker-compose.yml           # Docker services
    ├── CODEOWNERS                   # Code ownership rules
    └── README.md                    # This file
```

## 📋 Prerequisites

### Development Environment
- **Docker Desktop**: For local infrastructure services
- **Java 17**: Required for backend services
- **Maven 3.9+**: Build tool for Java services
- **Node.js 22.x**: Required for frontend development
- **npm or yarn**: Package manager for frontend

### Optional Tools
- **AWS CLI**: For deployment operations
- **Postman/Insomnia**: API testing
- **DBeaver/pgAdmin**: Database management
- **Redis CLI**: Redis management

## 🚀 Local Development Setup

### Quick Start (Recommended)
```bash
# 1. Start all infrastructure services
docker-compose up -d

# 2. Build all backend services
mvn clean install

# 3. Install frontend dependencies
cd frontend && npm install

# 4. Start frontend development server
npm start
```

### Detailed Setup

#### 1. Infrastructure Services

**Start all services with a single command:**
```bash
docker-compose up -d
```

**Or start individual components:**

**PostgreSQL Database (Master-Slave Replication):**
```bash
# Start master database (read-write) - Port 5432
docker-compose up -d postgres

# Start slave database (read-only) - Port 5433
docker-compose up -d postgres-slave

# Run database migrations
docker-compose up migrate
```

**Redis Cache & Session Management:**
```bash
# Start Redis server - Port 6379
docker-compose up -d redis

# Start Redis Commander UI - Port 9090
docker-compose up -d redis-commander
```
Access Redis Commander at: http://localhost:9090

**Kafka Event Streaming:**
```bash
# Start Kafka and Kowl UI
docker-compose -f kafka-kowl.yml up -d
```
Access Kafka Kowl UI at: http://localhost:8080

#### 2. Backend Services

**Build All Services:**
```bash
# Install dependencies and build all modules
mvn clean install

# Skip tests for faster build
mvn clean install -DskipTests
```

**Run Individual Services:**
```bash
# Profile API (Port: 8081)
cd ledger-profile-api && mvn spring-boot:run

# Account API (Port: 8082)
cd ledger-account-api && mvn spring-boot:run

# Transaction API (Port: 8083)
cd ledger-transaction-api && mvn spring-boot:run

# Health API (Port: 8084)
cd ledger-health-api && mvn spring-boot:run
```

#### 3. Frontend Application

```bash
# Navigate to frontend directory
cd frontend

# Install dependencies (updated packages as of PR #798)
npm install

# Start development server
npm start

# Or start with specific environment
npm run start:qa
```

**Frontend Access Points:**
- Development: http://localhost:3000
- API Documentation: Available through individual service endpoints

### Environment Configuration

The system supports multiple environments:
- **Development**: Local development with Docker services
- **QA**: Quality assurance environment
- **Staging**: Pre-production environment  
- **Production**: Live production environment

Environment-specific configurations are located in:
- Backend: `environments/` directory
- Frontend: `.env.*` files

## 🔨 Building & Testing

### Backend Services

**Build All Services:**
```bash
# Full build with tests
mvn clean install

# Fast build without tests
mvn clean install -DskipTests

# Build specific service
mvn -am -pl ledger-profile-api clean install

# Build with specific profile
mvn clean package -P<profile>
```

**Testing:**
```bash
# Run all tests
mvn test

# Run tests for specific service
cd ledger-profile-api && mvn test

# Run tests with coverage
mvn test jacoco:report
```

### Frontend Application

**Build Commands:**
```bash
cd frontend

# Development build
npm run build

# Environment-specific builds
npm run build:qa         # QA environment
npm run build:staging    # Staging environment
npm run build:prod       # Production environment
```

**Testing:**
```bash
cd frontend

# Run tests
npm test

# Run tests without watch mode
npm test -- --watchAll=false

# Run tests with coverage
npm test -- --coverage
```

### Docker Builds

**Build Docker Images:**
```bash
# Build specific service image
export APPLICATION_PATH=ledger-profile-api
export TAG=$(cat tag.txt)
export REPO_NAME="dev/tol/${APPLICATION_PATH}"
docker build -t $REPO_NAME:$TAG -f ${APPLICATION_PATH}/Dockerfile ${APPLICATION_PATH}/.
```

## 🚀 Deployment

### Frontend Deployment (AWS S3)

**Prerequisites:**
- AWS CLI configured with appropriate credentials
- S3 bucket created for target environment

**Deployment Steps:**
```bash
# 1. Build for target environment
cd frontend
npm run build:<env>  # qa, staging, or prod

# 2. Configure AWS credentials
export AWS_ACCESS_KEY_ID="your-access-key"
export AWS_SECRET_ACCESS_KEY="your-secret-key"
export AWS_SESSION_TOKEN="your-session-token"

# 3. Deploy to S3 bucket
aws --region ca-central-1 s3 sync ./build s3://<bucket-name> --delete
```

**Example QA Deployment:**
```bash
npm run build:qa
aws --region ca-central-1 s3 sync ./build s3://gl-qa-************ --delete
```

### Backend Deployment (AWS)

Backend services are deployed using AWS CodeBuild with environment-specific buildspec files:
- `buildspec_ledger-account.yml`
- `buildspec_ledger-profile.yml`
- `buildspec_ledger-transaction.yml`
- `buildspec_ledger-health.yml`
- `buildspec_ledger-schedulers.yml`

## 🔒 Security & Compliance

### Recent Security Updates (PR #798 - July 2025)
All npm packages have been updated to their latest versions to address security vulnerabilities identified by Veracode's Software Composition Analysis (SCA).

**Key Security Improvements:**
- **React 19.1.0**: Latest stable version with security patches
- **Material-UI 7.2.0**: Updated to address known vulnerabilities
- **Axios 1.10.0**: HTTP client with latest security fixes
- **All Testing Libraries**: Updated to secure versions

### Security Scanning

**Veracode Analysis:**
```bash
# Install Veracode wrapper
export VERACODE_VERSION="**********"
curl -O https://repo1.maven.org/maven2/com/veracode/vosp/api/wrappers/vosp-api-wrappers-java/$VERACODE_VERSION/vosp-api-wrappers-java-$VERACODE_VERSION.jar

# Run security scan
./security-analyze.sh
```

**Snyk Vulnerability Scanning:**
```bash
# Run Snyk security analysis
./security-analyze.sh
```

### Security Best Practices
- Regular dependency updates (monthly npm audit, quarterly major updates)
- Automated security scanning in CI/CD pipeline
- Environment-specific security configurations
- Secure credential management through AWS IAM roles

## 🔄 CI/CD Pipeline

### Pipeline Architecture
The CI/CD pipeline is built using Bitbucket Pipelines with AWS integration:

- **Source Control**: Bitbucket
- **Build System**: Bitbucket Pipelines
- **Container Registry**: AWS ECR
- **Deployment**: AWS CodeBuild + S3
- **Security Scanning**: Veracode + Snyk

### Pipeline Generation

**Important**: Always modify the template instead of the pipeline directly.

1. Install [gomplate](https://docs.gomplate.ca/)
2. Execute the template:
```bash
gomplate -f environments/scripts/bitbucket-pipelines.tmpl \
  -d input=environments/scripts/input.yaml \
  -o bitbucket-pipelines.yml
```

### Adding a New Service
1. Add/remove service in `environments/scripts/input.yaml`
2. Regenerate pipeline using the command above
3. Create corresponding buildspec file if needed
4. Update CODEOWNERS file for code review assignments

### Pipeline Features
- **Multi-stage builds**: Development, QA, Staging, Production
- **Parallel execution**: Services build in parallel for efficiency
- **Security integration**: Automated Veracode and Snyk scans
- **Docker image management**: Automated tagging and ECR push
- **Environment promotion**: Controlled deployment across environments

## 🔧 Troubleshooting

### Common Issues & Solutions

#### Database Issues
```bash
# Check if PostgreSQL containers are running
docker ps | grep postgres

# View database logs
docker logs <postgres-container-id>

# Reset database (WARNING: This will delete all data)
docker-compose down -v
docker-compose up -d postgres postgres-slave
docker-compose up migrate
```

#### Backend Service Issues
```bash
# Check Java version
java -version  # Should be Java 17

# Check Maven version
mvn -version   # Should be 3.9+

# Clean and rebuild
mvn clean install -DskipTests

# Check service logs
cd ledger-profile-api && mvn spring-boot:run
```

#### Frontend Issues
```bash
# Check Node.js version
node -v        # Should be 22.x

# Clear npm cache
npm cache clean --force

# Delete node_modules and reinstall
rm -rf node_modules package-lock.json
npm install

# Check for dependency conflicts
npm ls
```

#### Docker Issues
```bash
# Check Docker status
docker ps -a

# Restart all services
docker-compose down
docker-compose up -d

# View service logs
docker-compose logs <service-name>

# Clean up Docker resources
docker system prune -a
```

#### Network & Port Issues
```bash
# Check if ports are in use
lsof -i :3000  # Frontend
lsof -i :5432  # PostgreSQL master
lsof -i :5433  # PostgreSQL slave
lsof -i :6379  # Redis
lsof -i :8080  # Kafka UI
lsof -i :9090  # Redis Commander
```

### Performance Issues
- **Slow builds**: Use `mvn clean install -T 1C` for parallel builds
- **Memory issues**: Increase Docker memory allocation
- **Database performance**: Check connection pool settings

### Security Issues
- **Dependency vulnerabilities**: Run `npm audit` and `mvn dependency:check`
- **Authentication issues**: Verify AWS Cognito configuration
- **CORS errors**: Check API gateway and frontend configurations

## 📚 Additional Resources

### Documentation
- [Frontend README](frontend/README.md) - Detailed frontend documentation
- [API Documentation](http://localhost:8080/swagger-ui.html) - When services are running
- [Spring Boot Documentation](https://spring.io/projects/spring-boot)
- [React Documentation](https://reactjs.org/)
- [Material-UI Documentation](https://mui.com/)

### Development Tools
- **Database Management**: DBeaver, pgAdmin
- **API Testing**: Postman, Insomnia
- **Redis Management**: Redis Commander (http://localhost:9090)
- **Kafka Management**: Kowl UI (http://localhost:8080)

### Monitoring & Observability
- **Application Logs**: Check individual service logs
- **Database Monitoring**: PostgreSQL logs and metrics
- **Cache Monitoring**: Redis Commander
- **Message Queue**: Kafka Kowl UI

## 🤝 Contributing

### Development Workflow
1. Create feature branch from `master`
2. Make changes and test locally
3. Run security scans: `./security-analyze.sh`
4. Update documentation if needed
5. Create pull request with detailed description
6. Ensure all CI/CD checks pass
7. Request code review from CODEOWNERS

### Code Standards
- **Java**: Follow Spring Boot best practices
- **React**: Use functional components and hooks
- **Testing**: Maintain test coverage above 80%
- **Documentation**: Update README files for significant changes

### Dependency Management
- **Backend**: Update parent POM versions carefully
- **Frontend**: Test thoroughly after dependency updates
- **Security**: Regular vulnerability scans and updates

---

**Project**: General Ledger System  
**Last Updated**: July 2025 (PR #798 - Package Updates)  
**Maintainer**: People's Trust Development Team  
**Repository**: peoplestrust/general_ledger  

**Technology Stack Summary**:
- **Backend**: Java 17, Spring Boot 3.1.12, Maven
- **Frontend**: React 19.1.0, Material-UI 7.2.0, Node.js 22.x
- **Database**: PostgreSQL 12 (Master-Slave)
- **Cache**: Redis
- **Message Queue**: Kafka
- **Cloud**: AWS (S3, ECR, CodeBuild)
- **CI/CD**: Bitbucket Pipelines

