openapi: 3.0.3
info:
  title: Transaction Orchestration Layer -- Internal
  description: |
    Transaction Orchestration Layer is the record-keeping system for a partners balances across the various Peoples Group products, with debit and credit account records validated by a trial balance.
  version: 1.4.0
servers:
  - url: 'https://ledger-stg-api.peoplescloud.io/admin-ui'
    description: Transaction Orchestration Layer Staging Environment
paths:
  '/v1/internal/account/validate/{account_ref_id}':
    get:
      tags:
        - Ledger Account
      parameters:
        - description: A unique id  (uuid v4) generated for each request
          in: header
          name: x-pg-interaction-id
          schema:
            description: Uniquely identifies the message.
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[a-zA-Z0-9-.]$'
            example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          required: true
          example: 0a0d74f6-49b5-4b0b-b723-48bdfb142117
        - description: A unique id that identifies the partner
          in: header
          name: x-pg-profile-id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
        - description: A unique id
          in: path
          name: account_ref_id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
      operationId: validateAccountAndProfile
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                type: boolean
        '400':
          description: Bad Request
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '404':
          description: Resource not found
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '500':
          description: Unexpected internal system error
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '503':
          description: System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
  '/v1/internal/account/{account_ref_id}':
    get:
      tags:
        - Ledger Account
      parameters:
        - description: A unique id  (uuid v4) generated for each request
          in: header
          name: x-pg-interaction-id
          schema:
            description: Uniquely identifies the message.
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[a-zA-Z0-9-.]$'
            example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          required: true
          example: 0a0d74f6-49b5-4b0b-b723-48bdfb142117
        - description: A unique id that identifies the partner
          in: header
          name: x-pg-profile-id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
        - description: A unique id
          in: path
          name: account_ref_id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
      operationId: retrieveAccountDetails
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  ref_Id:
                    type: string
                    format: uuid
                    example: 2a42f783-ca99-4e3d-a87e-d3325013136c
                  name:
                    type: string
                    example: CryptoFin Peer2Peer Settlement Acct
                    minLength: 3
                    maxLength: 50
                    pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
                  description:
                    type: string
                    example: For all peer to peer transfers
                    minLength: 3
                    maxLength: 255
                    pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
                  monetary_unit:
                    type: string
                    description: Currency codes as per ISO 4217
                    example: CAD
                    minLength: 3
                    maxLength: 3
                    pattern: '^[A-Z]+$'
                  options:
                    type: object
                    properties:
                      overdraft_amount:
                        type: number
                        multipleOf: 0.01
                        minimum: 0
                        maximum: *********.99
                        example: 100000
                      fund_hold_days:
                        type: string
                        minimum: 0
                        maximum: 99
                        pattern: '^[0-9]+$'
                        example: 5
                  status:
                    type: string
                    enum:
                      - ACTIVE
                      - INACTIVE
                      - SUSPENDED
                    example: ACTIVE
                  created_date_time:
                    type: string
                    format: date-time
                    example: '2021-10-22T15:59:05.586Z '
                  updated_date_time:
                    type: string
                    format: date-time
                    example: '2021-10-22T15:59:05.586Z'
        '400':
          description: Bad Request
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '404':
          description: Resource not found
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '500':
          description: Unexpected internal system error
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '503':
          description: System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
  '/v1/internal/transaction/balance/{account_ref_id}':
    get:
      tags:
        - Ledger Transaction
      parameters:
        - description: A unique id  (uuid v4) generated for each request
          in: header
          name: x-pg-interaction-id
          schema:
            description: Uniquely identifies the message.
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[a-zA-Z0-9-.]$'
            example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          required: true
          example: 0a0d74f6-49b5-4b0b-b723-48bdfb142117
        - description: Overdraft amount for an specific account
          in: header
          name: overdraft_amount
          required: true
          schema:
            type: number
            multipleOf: 0.01
            minimum: 0
            maximum: *********.99
            example: 100000
        - description: A unique id that identifies the partner
          in: header
          name: x-pg-profile-id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
        - description: A unique id
          in: path
          name: account_ref_id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
      operationId: retreiveAccountBalance
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  available_balance:
                    type: number
                    multipleOf: 0.01
                    minimum: 0
                    example: **********.3
                  account_balance:
                    type: number
                    multipleOf: 0.01
                    minimum: 0
                    example: **********.08
                  fund_hold_amount:
                    type: number
                    multipleOf: 0.01
                    minimum: 0
                    example: 113692.76
                  prefund_reserve_amount:
                    type: number
                    example: 100000.12
                    multipleOf: 0.01
                    minimum: 0
                    maximum: *********
                  overdraft_amount:
                    type: number
                    multipleOf: 0.01
                    minimum: 0
                    maximum: *********.99
                    example: 100000
                  effective_on:
                    type: string
                    format: date-time
                    example: '2021-10-23T15:59:05.586Z'
        '400':
          description: Bad Request -- Validation failure
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '401':
          description: Unauthorized -- Authorization required to access resource
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '404':
          description: 'Resource Not Found -- Any of service account, austomer, account, etc.'
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '500':
          description: Internal Server Error -- Unexpected error
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '503':
          description: Service Unavailable -- System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
  '/v1/internal/transaction/balance/search/{account_ref_id}':
    get:
      tags:
        - Ledger Transaction
      operationId: searchBalanceSnapshots
      parameters:
        - description: A unique id  (uuid v4) generated for each request
          in: header
          name: x-pg-interaction-id
          schema:
            description: Uniquely identifies the message.
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[a-zA-Z0-9-.]$'
            example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          required: true
          example: 0a0d74f6-49b5-4b0b-b723-48bdfb142117
        - description: A unique id that identifies the partner
          in: header
          name: x-pg-profile-id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
        - description: A unique id
          in: path
          name: account_ref_id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
        - in: query
          name: from_date
          description: Start date/time in UTC of the inquiry. Search for transactions created on or after this date (default is 30 days back)
          type: string
          format: date-time
          example: '2021-10-23T15:59:05.586Z'
        - in: query
          name: to_date
          description: End date/time in UTC of the inquiry. Search for transactions created before this date (default is today)
          type: string
          format: date-time
          example: '2021-10-23T15:59:05.586Z'
        - in: query
          name: offset
          description: offset is starting point of retrieve transaction request filter; if offset is not provided it would be defaulted to zero;
          required: false
          schema:
            type: integer
        - in: query
          name: max_items
          description: Maximum number of response items to be returned.
          required: false
          schema:
            type: integer
            default: 25
            maximum: 250
      responses:
        '200':
          description: Resource successfully retrieved
          content:
            application/json:
              schema:
                type: object
                properties:
                  total_amount_credit:
                    type: number
                    description: Total amount of credit transactions
                    multipleOf: 0.01
                    minimum: 0
                    example: 2500.5
                  total_amount_debit:
                    type: number
                    description: Total amount of credit transactions
                    multipleOf: 0.01
                    minimum: 0
                    example: 2500.5
                  total_amount:
                    type: number
                    description: Total amount of credit transactions
                    multipleOf: 0.01
                    minimum: 0
                    example: 2500.5
                  total_reserve_amount:
                    type: number
                    description: Total amount of credit transactions
                    multipleOf: 0.01
                    minimum: 0
                    example: 2500.5
                  total_pending_amount:
                    type: number
                    description: Total amount of credit transactions
                    multipleOf: 0.01
                    minimum: 0
                    example: 2500.5
                  monetary_unit:
                    type: string
                    description: Currency codes as per ISO 4217
                    example: CAD
                    minLength: 3
                    maxLength: 3
                    pattern: '^[A-Z]+$'
                  effective_to_date_time:
                    type: string
                    format: date-time
                    example: '2021-10-23T15:59:05.586Z'
                  effective_from_date_time:
                    type: string
                    format: date-time
                    example: '2021-10-23T15:59:05.586Z'
                  created_date_time:
                    type: string
                    format: date-time
                    example: '2021-10-22T15:59:05.586Z '
        '400':
          description: Bad Request -- Validation failure
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '401':
          description: Unauthorized -- Authorization required to access resource
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '404':
          description: 'Resource Not Found -- Any of service account, austomer, account, etc.'
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '500':
          description: Internal Server Error -- Unexpected error
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '503':
          description: Service Unavailable -- System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
  '/v1/internal/ledger/transaction/rollback/{instruction_ref_id}':
    get:
      tags:
        - Ledger Transaction
      parameters:
        - description: A unique id  (uuid v4) generated for each request
          in: header
          name: x-pg-interaction-id
          schema:
            description: Uniquely identifies the message.
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[a-zA-Z0-9-.]$'
            example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          required: true
          example: 0a0d74f6-49b5-4b0b-b723-48bdfb142117
        - description: A unique id for ledger account of partner
          in: header
          name: x-pg-account-id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
        - description: A unique id that identifies the partner
          in: header
          name: x-pg-profile-id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
        - description: A unique id for an instruction
          in: path
          name: instruction_ref_id
          required: true
          schema:
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[0-9a-zA-Z-._]+$'
            example: FILEREF001
      operationId: rollbackOrphanTransactions
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                type: boolean
        '400':
          description: Bad Request -- Validation failure
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '401':
          description: Unauthorized -- Authorization required to access resource
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '404':
          description: 'Resource Not Found -- Any of service account, customer, account, etc.'
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '500':
          description: Internal Server Error -- Unexpected error
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '503':
          description: Service Unavailable -- System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
  /v1/internal/profiles:
    get:
      tags:
        - Ledger Profile
      operationId: retrieveAllProfiles
      responses:
        '200':
          description: Profiles retrieved successfully
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                properties:
                  profiles:
                    type: array
                    items:
                      type: object
                      properties:
                        ref_id:
                          type: string
                          format: uuid
                          example: 2a42f783-ca99-4e3d-a87e-d3325013136c
                        crm_id:
                          type: string
                          example: j41klvu
                          minLength: 3
                          maxLength: 50
                          pattern: '^[0-9a-zA-Z]+$'
                        display_name:
                          type: string
                          pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
                          minLength: 3
                          maxLength: 50
                          example: Crypto Fintech
                        legal_name:
                          type: string
                          example: Crypto Fintech Inc.
                          minLength: 3
                          maxLength: 50
                          pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
                        status:
                          type: string
                          enum:
                            - ENABLED
                            - DISABLED
                          example: ENABLED
                        created_date_time:
                          type: string
                          format: date-time
                          example: '2021-10-22T15:59:05.586Z '
                        updated_date_time:
                          type: string
                          format: date-time
                          example: '2021-10-22T15:59:05.586Z'
                        cognito_client_id:
                          type: string
                          maxLength: 50
                        reason:
                          type: string
                          minLength: 3
                          maxLength: 240
                          pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
                          example: 'Business relationship was suspended '
        '400':
          description: Bad Request
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '404':
          description: Resource not found
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '500':
          description: Unexpected internal system error
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '503':
          description: System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
  '/v1/internal/profile/{profile_ref_id}':
    get:
      tags:
        - Ledger Profile
      operationId: retrieveProfile
      parameters:
        - description: A unique id  (uuid v4) generated for each request
          in: header
          name: x-pg-interaction-id
          schema:
            description: Uniquely identifies the message.
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[a-zA-Z0-9-.]$'
            example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          required: true
          example: 0a0d74f6-49b5-4b0b-b723-48bdfb142117
        - description: A unique id
          in: path
          name: ref_id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
      responses:
        '200':
          description: retrieve profile related to a profileId to check whether it exists or not
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: boolean
        '400':
          description: Bad Request
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '404':
          description: Resource not found
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '500':
          description: Unexpected internal system error
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '503':
          description: System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
  '/v1/internal/profile/{profile_ref_id}/enabled':
    get:
      tags:
        - Ledger Profile
      operationId: retrieveProfileStatus
      parameters:
        - description: A unique id  (uuid v4) generated for each request
          in: header
          name: x-pg-interaction-id
          schema:
            description: Uniquely identifies the message.
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[a-zA-Z0-9-.]$'
            example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          required: true
          example: 0a0d74f6-49b5-4b0b-b723-48bdfb142117
        - description: A unique id
          in: path
          name: ref_id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
      responses:
        '200':
          description: retrieve status related to a profileId to check whether it is enabled or not
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: boolean
        '400':
          description: Bad Request
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '404':
          description: Resource not found
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '500':
          description: Unexpected internal system error
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '503':
          description: System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
  '/v1/internal/profile/validate/{profile_ref_id}/{client_id}':
    get:
      tags:
        - Ledger Profile
      operationId: validateProfileStatusAndClientId
      parameters:
        - description: A unique id  (uuid v4) generated for each request
          in: header
          name: x-pg-interaction-id
          schema:
            description: Uniquely identifies the message.
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[a-zA-Z0-9-.]$'
            example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          required: true
          example: 0a0d74f6-49b5-4b0b-b723-48bdfb142117
        - description: A unique id
          in: path
          name: ref_id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
        - description: A unique id
          in: path
          name: client_id
          required: true
          schema:
            type: string
            maxLength: 50
      responses:
        '200':
          description: retrieve status related to a profileId to check whether it is enabled or not and check whether client id belongs to profile
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: boolean
        '400':
          description: Bad Request
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '404':
          description: Resource not found
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '500':
          description: Unexpected internal system error
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '503':
          description: System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
components:
  schemas:
    BalanceSnapshotResponse:
      type: object
      properties:
        total_amount_credit:
          type: number
          description: Total amount of credit transactions
          multipleOf: 0.01
          minimum: 0
          example: 2500.5
        total_amount_debit:
          type: number
          description: Total amount of credit transactions
          multipleOf: 0.01
          minimum: 0
          example: 2500.5
        total_amount:
          type: number
          description: Total amount of credit transactions
          multipleOf: 0.01
          minimum: 0
          example: 2500.5
        total_reserve_amount:
          type: number
          description: Total amount of credit transactions
          multipleOf: 0.01
          minimum: 0
          example: 2500.5
        total_pending_amount:
          type: number
          description: Total amount of credit transactions
          multipleOf: 0.01
          minimum: 0
          example: 2500.5
        monetary_unit:
          type: string
          description: Currency codes as per ISO 4217
          example: CAD
          minLength: 3
          maxLength: 3
          pattern: '^[A-Z]+$'
        effective_to_date_time:
          type: string
          format: date-time
          example: '2021-10-23T15:59:05.586Z'
        effective_from_date_time:
          type: string
          format: date-time
          example: '2021-10-23T15:59:05.586Z'
        created_date_time:
          type: string
          format: date-time
          example: '2021-10-22T15:59:05.586Z '
    RetrieveBalanceResponse:
      type: object
      properties:
        available_balance:
          type: number
          multipleOf: 0.01
          minimum: 0
          example: **********.3
        account_balance:
          type: number
          multipleOf: 0.01
          minimum: 0
          example: **********.08
        fund_hold_amount:
          type: number
          multipleOf: 0.01
          minimum: 0
          example: 113692.76
        prefund_reserve_amount:
          type: number
          example: 100000.12
          multipleOf: 0.01
          minimum: 0
          maximum: *********
        overdraft_amount:
          type: number
          multipleOf: 0.01
          minimum: 0
          maximum: *********.99
          example: 100000
        effective_on:
          type: string
          format: date-time
          example: '2021-10-23T15:59:05.586Z'
    LedgerRetrieveTransaction:
      type: object
      required:
        - transaction_ref_id
        - amount
        - acceptance_date_time
      properties:
        transaction_ref_id:
          type: string
          minLength: 3
          maxLength: 36
          pattern: '^[0-9a-zA-Z-._]+$'
          example: f9443bba-0443-4b71-a17d-42a8057534c8
        amount:
          type: number
          multipleOf: 0.01
          minimum: 0
          maximum: 1000000
          example: 123
        acceptance_date_time:
          type: string
          format: date-time
          example: '2021-10-23T15:59:05.586Z'
    RetrieveLedgerProfilesResponse:
      type: object
      properties:
        profiles:
          type: array
          items:
            type: object
            properties:
              ref_id:
                type: string
                format: uuid
                example: 2a42f783-ca99-4e3d-a87e-d3325013136c
              crm_id:
                type: string
                example: j41klvu
                minLength: 3
                maxLength: 50
                pattern: '^[0-9a-zA-Z]+$'
              display_name:
                type: string
                pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
                minLength: 3
                maxLength: 50
                example: Crypto Fintech
              legal_name:
                type: string
                example: Crypto Fintech Inc.
                minLength: 3
                maxLength: 50
                pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
              status:
                type: string
                enum:
                  - ENABLED
                  - DISABLED
                example: ENABLED
              created_date_time:
                type: string
                format: date-time
                example: '2021-10-22T15:59:05.586Z '
              updated_date_time:
                type: string
                format: date-time
                example: '2021-10-22T15:59:05.586Z'
              cognito_client_id:
                type: string
                maxLength: 50
              reason:
                type: string
                minLength: 3
                maxLength: 240
                pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
                example: 'Business relationship was suspended '
