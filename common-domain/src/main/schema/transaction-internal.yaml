openapi: 3.0.2
paths:

  #########################################################################################################
  /v1/internal/transaction/balance/{account_ref_id}:
    get:
      tags:
        - Ledger Transaction
      parameters:
        # Headers -- General #
        - $ref: 'common/common.yaml#/components/parameters/InteractionId'
        # Headers #
        - $ref: 'common/common.yaml#/components/parameters/OverdraftAmountHeader'
        - $ref: 'common/common.yaml#/components/parameters/ProfileRefIdHeader'
        # Path Parameters #
        - $ref: 'common/common.yaml#/components/parameters/AccountRefIdPath'
      operationId: retreiveAccountBalance
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RetrieveBalanceResponse'
        '400':
          description: Bad Request -- Validation failure
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '404':
          description: Resource Not Found -- Any of service account, austomer, account, etc.
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized -- Authorization required to access resource
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error -- Unexpected error
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '503':
          description: Service Unavailable -- System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'

  #########################################################################################################
  /v1/internal/transaction/balance/search/{account_ref_id}:
    get:
      tags:
        - Ledger Transaction
      operationId: searchBalanceSnapshots
      parameters:
        # Headers -- General #
        - $ref: 'common/common.yaml#/components/parameters/InteractionId'
        # Headers #
        - $ref: 'common/common.yaml#/components/parameters/ProfileRefIdHeader'
        # Path Parameters #
        - $ref: 'common/common.yaml#/components/parameters/AccountRefIdPath'
        - in: query
          name: from_date
          description: Start date/time in UTC of the inquiry. Search for transactions created on or after this date (default is 30 days back)
          type: string
          format: date-time
          example: '2021-10-23T15:59:05.586Z'
        - in: query
          name: to_date
          description: End date/time in UTC of the inquiry. Search for transactions created before this date (default is today)
          type: string
          format: date-time
          example: '2021-10-23T15:59:05.586Z'
        - in: query
          name: offset
          description: offset is starting point of retrieve transaction request filter; if offset
            is not provided it would be defaulted to zero;
          required: false
          schema:
            type: integer
        - in: query
          name: max_items
          description: Maximum number of response items to be returned.
          required: false
          schema:
            type: integer
            default: 25
            maximum: 250
      responses:
        '200':
          description: Resource successfully retrieved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BalanceSnapshotResponse'
        '400':
          description: Bad Request -- Validation failure
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '404':
          description: Resource Not Found -- Any of service account, austomer, account, etc.
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized -- Authorization required to access resource
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error -- Unexpected error
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '503':
          description: Service Unavailable -- System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'

  #########################################################################################################
  /v1/internal/ledger/transaction/rollback/{instruction_ref_id}:
    get:
      tags:
        - Ledger Transaction
      parameters:
        # Headers -- General #
        - $ref: 'common/common.yaml#/components/parameters/InteractionId'
        # Headers #
        - $ref: 'common/common.yaml#/components/parameters/AccountRefIdHeader'
        - $ref: 'common/common.yaml#/components/parameters/ProfileRefIdHeader'
        # Path Parameters #
        - $ref: 'common/common.yaml#/components/parameters/InstructionRefIdPath'
      operationId: rollbackOrphanTransactions
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                type: boolean
        '400':
          description: Bad Request -- Validation failure
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '404':
          description: Resource Not Found -- Any of service account, customer, account, etc.
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized -- Authorization required to access resource
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error -- Unexpected error
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '503':
          description: Service Unavailable -- System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
components:
  schemas:
    BalanceSnapshotResponse:
      type: object
      properties:
        total_amount_credit:
          $ref: 'common/common.yaml#/components/schemas/CreditTotal'
        total_amount_debit:
          $ref: 'common/common.yaml#/components/schemas/CreditTotal'
        total_amount:
          $ref: 'common/common.yaml#/components/schemas/CreditTotal'
        total_reserve_amount:
          $ref: 'common/common.yaml#/components/schemas/CreditTotal'
        total_pending_amount:
          $ref: 'common/common.yaml#/components/schemas/CreditTotal'
        monetary_unit:
          $ref: 'common/common.yaml#/components/schemas/MonetaryUnit'
        effective_to_date_time:
          $ref: 'common/common.yaml#/components/schemas/EffectiveDateTime'
        effective_from_date_time:
          $ref: 'common/common.yaml#/components/schemas/EffectiveDateTime'
        created_date_time:
          $ref: 'common/common.yaml#/components/schemas/CreatedDateTime'

    RetrieveBalanceResponse:
      type: object
      properties:
        available_balance:
          $ref: 'common/common.yaml#/components/schemas/AvailableBalance'
        account_balance:
          $ref: 'common/common.yaml#/components/schemas/AccountBalance'
        fund_hold_amount:
          $ref: 'common/common.yaml#/components/schemas/FundHoldAmount'
        prefund_reserve_amount:
          $ref: 'common/common.yaml#/components/schemas/PrefundReserveAmount'
        overdraft_amount:
          $ref: 'common/common.yaml#/components/schemas/OverdraftAmount'
        effective_on:
          $ref: 'common/common.yaml#/components/schemas/EffectiveDateTime'

    LedgerRetrieveTransaction:
      type: object
      required:
        - transaction_ref_id
        - amount
        - acceptance_date_time
      properties:
        transaction_ref_id:
          $ref: 'common/common.yaml#/components/schemas/TransactionRefId'
        amount:
          $ref: 'common/common.yaml#/components/schemas/Amount'
        acceptance_date_time:
          $ref: 'common/common.yaml#/components/schemas/AcceptanceDateTime'
