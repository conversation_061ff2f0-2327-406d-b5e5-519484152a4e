openapi: 3.0.2
paths:
  #########################################################################################################
  /v1/internal/account/validate/{account_ref_id}:
    get:
      tags:
        - Ledger Account
      parameters:
        # Headers -- General #
        - $ref: 'common/common.yaml#/components/parameters/InteractionId'
        # Headers #
        - $ref: 'common/common.yaml#/components/parameters/ProfileRefIdHeader'
        # Path Parameters #
        - $ref: 'common/common.yaml#/components/parameters/AccountRefIdPath'
      operationId: validateAccountAndProfile
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                type: boolean
        '400':
          description: Bad Request
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '404':
          description: Resource not found
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '500':
          description: Unexpected internal system error
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '503':
          description: System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'

  #########################################################################################################
  /v1/internal/account/{account_ref_id}:
    get:
      tags:
        - Ledger Account
      parameters:
        # Headers -- General #
        - $ref: 'common/common.yaml#/components/parameters/InteractionId'
        # Headers #
        - $ref: 'common/common.yaml#/components/parameters/ProfileRefIdHeader'
        # Path Parameters #
        - $ref: 'common/common.yaml#/components/parameters/AccountRefIdPath'
      operationId: retrieveAccountDetails
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: 'account-adminui.yaml#/components/schemas/RetrieveLedgerAccountResponse'
        '400':
          description: Bad Request
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '404':
          description: Resource not found
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '500':
          description: Unexpected internal system error
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '503':
          description: System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'

