openapi: 3.0.3
info:
  title: Transaction Orchestration Layer -- System to System
  description: |
    Transaction Orchestration Layer is the record-keeping system for a partners balances across the various Peoples Group products, with debit and credit account records validated by a trial balance.
  version: 1.4.0
servers:
  - url: 'http://adbaa1885f84b49aaa088263413b4579-2c7922fd882b9561.elb.ca-central-1.amazonaws.com:8080'
    description: Transaction Orchestration Layer QA Environment
  - url: 'https://ledger-stg-api.peoplescloud.io/internal'
    description: Transaction Orchestration Layer Staging Environment
  - url: 'https://ledger-api.peoplesgroup.com'
    description: Transaction Orchestration Layer Production Environment
paths:
  /v1/ledger/transaction:
    post:
      tags:
        - Ledger Transaction
      operationId: initiateInstruction
      parameters:
        - description: A unique id  (uuid v4) generated for each request
          in: header
          name: x-pg-interaction-id
          schema:
            description: Uniquely identifies the message.
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[a-zA-Z0-9-.]$'
            example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          required: true
          example: 0a0d74f6-49b5-4b0b-b723-48bdfb142117
        - description: A particular point in progression of time defined (GMT)
          in: header
          name: x-pg-interaction-timestamp
          required: true
          schema:
            type: string
            format: date-time
            example: '2020-05-29T17:19:26.951000Z'
        - description: A unique id that identifies the partner
          in: header
          name: x-pg-profile-id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
        - description: A unique id for ledger account of partner
          in: header
          name: x-pg-account-id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
                - instruction_ref_id
                - payment_rail
                - transactions
              properties:
                instruction_ref_id:
                  type: string
                  minLength: 3
                  maxLength: 36
                  pattern: '^[0-9a-zA-Z-._]+$'
                  example: FILEREF001
                payment_rail:
                  type: string
                  enum:
                    - EFT
                    - ETRANSFER
                    - WIRES
                    - VISA
                    - INTERNAL
                  example: EFT
                transactions:
                  type: array
                  minItems: 1
                  maxItems: 25
                  items:
                    type: object
                    required:
                      - transaction_ref_id
                      - payment_category
                      - amount
                      - monetary_unit
                      - acceptance_date_time
                    properties:
                      transaction_ref_id:
                        type: string
                        minLength: 3
                        maxLength: 36
                        pattern: '^[0-9a-zA-Z-._]+$'
                        example: f9443bba-0443-4b71-a17d-42a8057534c8
                      payment_category:
                        type: string
                        description: Category of the payment associated with the payment rail
                        enum:
                          - DEBIT_PULL
                          - CREDIT_PUSH
                          - SEND_PAYMENT
                          - COMPLETE_PAYMENT
                          - ALIAS_DEPOSIT_PAYMENT
                          - ACCOUNT_NUMBER_DEPOSIT_PAYMENT
                          - FULFILL_REQUEST_FOR_PAYMENT
                          - REVERSAL
                          - CORRECTION
                          - PREFUND_RESERVE
                        example: DEBIT_PULL
                      amount:
                        type: number
                        multipleOf: 0.01
                        minimum: 0
                        maximum: 1000000
                        example: 123
                      monetary_unit:
                        type: string
                        description: Currency codes as per ISO 4217
                        example: CAD
                        minLength: 3
                        maxLength: 3
                        pattern: '^[A-Z]+$'
                      acceptance_date_time:
                        type: string
                        format: date-time
                        example: '2021-10-23T15:59:05.586Z'
                      due_date_time:
                        type: string
                        format: date-time
                        example: '2021-10-25T15:59:05.586Z'
              example:
                instruction_ref_id: 32e655ed-e162-4168-a924-dcbac87fb127
                payment_rail: EFT
                transactions:
                  - transaction_ref_id: f9443bba-0443-4b71-a17d-42a8057534c8
                    payment_category: DEBIT_PULL
                    amount: 2500.5
                    monetary_unit: CAD
                    acceptance_date_time: '2021-10-23T15:59:05.586Z'
                    due_date_time: '2021-10-25T15:59:05.586Z'
                  - transaction_ref_id: 5a530373-2f3a-455f-a66d-751a01e3b0f7
                    payment_category: CREDIT_PUSH
                    amount: 1500.5
                    monetary_unit: CAD
                    acceptance_date_time: '2021-10-23T15:59:05.586Z'
        required: true
      responses:
        '201':
          description: Resource successfully created
          content:
            application/json:
              schema:
                allOf:
                  - type: object
                    required:
                      - instruction_ref_id
                      - payment_rail
                      - status
                      - transactions
                      - created_date_time
                    properties:
                      instruction_ref_id:
                        type: string
                        minLength: 3
                        maxLength: 36
                        pattern: '^[0-9a-zA-Z-._]+$'
                        example: FILEREF001
                      payment_rail:
                        type: string
                        enum:
                          - EFT
                          - ETRANSFER
                          - WIRES
                          - VISA
                          - INTERNAL
                        example: EFT
                      totals:
                        type: object
                        description: 'Credit and debit totals (count, amount)'
                        properties:
                          debit_count:
                            type: integer
                            description: Number of debit transactions
                            format: int32
                            minimum: 0
                            maximum: 25
                            example: 1
                          debit_total:
                            type: number
                            description: Total amount of debit transactions
                            multipleOf: 0.01
                            minimum: 0
                            example: 1500.5
                          credit_count:
                            type: integer
                            description: Number of credit transactions
                            format: int32
                            minimum: 0
                            maximum: 25
                            example: 1
                          credit_total:
                            type: number
                            description: Total amount of credit transactions
                            multipleOf: 0.01
                            minimum: 0
                            example: 2500.5
                      status:
                        type: string
                        description: Status of the instruction
                        enum:
                          - FAILED
                          - PENDING
                          - ROLLBACKED
                          - POSTED
                        example: PENDING
                      transactions:
                        type: array
                        minItems: 1
                        maxItems: 25
                        items:
                          type: object
                          required:
                            - transaction_ref_id
                            - payment_category
                            - transaction_flow
                            - amount
                            - monetary_unit
                            - acceptance_date_time
                            - effective_date_time
                            - status
                          properties:
                            transaction_ref_id:
                              type: string
                              minLength: 3
                              maxLength: 36
                              pattern: '^[0-9a-zA-Z-._]+$'
                              example: f9443bba-0443-4b71-a17d-42a8057534c8
                            payment_category:
                              type: string
                              description: Category of the payment associated with the payment rail
                              enum:
                                - DEBIT_PULL
                                - CREDIT_PUSH
                                - SEND_PAYMENT
                                - COMPLETE_PAYMENT
                                - ALIAS_DEPOSIT_PAYMENT
                                - ACCOUNT_NUMBER_DEPOSIT_PAYMENT
                                - FULFILL_REQUEST_FOR_PAYMENT
                                - REVERSAL
                                - CORRECTION
                                - PREFUND_RESERVE
                              example: DEBIT_PULL
                            transaction_flow:
                              type: string
                              enum:
                                - CREDIT
                                - DEBIT
                              example: CREDIT
                            amount:
                              type: number
                              multipleOf: 0.01
                              minimum: 0
                              maximum: 1000000
                              example: 123
                            monetary_unit:
                              type: string
                              description: Currency codes as per ISO 4217
                              example: CAD
                              minLength: 3
                              maxLength: 3
                              pattern: '^[A-Z]+$'
                            acceptance_date_time:
                              type: string
                              format: date-time
                              example: '2021-10-23T15:59:05.586Z'
                            due_date_time:
                              type: string
                              format: date-time
                              example: '2021-10-25T15:59:05.586Z'
                            effective_date_time:
                              type: string
                              format: date-time
                              example: '2021-10-23T15:59:05.586Z'
                            events:
                              type: array
                              items:
                                type: object
                                required:
                                  - event
                                  - created_date_time
                                properties:
                                  event:
                                    type: string
                                    enum:
                                      - FAILED
                                      - PENDING
                                      - POSTED
                                      - ROLLBACKED
                                      - REVERSED
                                    example: PENDING
                                  created_date_time:
                                    type: string
                                    format: date-time
                                    example: '2021-10-22T15:59:05.586Z '
                            status:
                              type: string
                              description: Status of the transaction
                              enum:
                                - FAILED
                                - PENDING
                                - POSTED
                                - ROLLBACKED
                                - REVERSED
                              example: PENDING
                      created_date_time:
                        type: string
                        format: date-time
                        example: '2021-10-22T15:59:05.586Z '
                      updated_date_time:
                        type: string
                        format: date-time
                        example: '2021-10-22T15:59:05.586Z'
                  - example:
                      instruction_ref_id: 32e655ed-e162-4168-a924-dcbac87fb127
                      payment_rail: EFT
                      totals:
                        debit_count: 1
                        debit_total: 1500.5
                        credit_count: 1
                        credit_total: 2500.5
                      status: PENDING
                      transactions:
                        - transaction_ref_id: f9443bba-0443-4b71-a17d-42a8057534c8
                          payment_category: DEBIT_PULL
                          transaction_flow: CREDIT
                          amount: 2500.5
                          monetary_unit: CAD
                          acceptance_date_time: '2021-10-23T15:59:05.586Z'
                          due_date_time: '2021-10-23T15:59:05.615Z'
                          effective_date_time: '2021-10-23T15:59:05.615Z'
                          status: PENDING
                          events:
                            - event: PENDING
                              created_date_time: '2021-10-23T15:59:11.174Z'
                        - transaction_ref_id: 5a530373-2f3a-455f-a66d-751a01e3b0f7
                          payment_category: CREDIT_PUSH
                          transaction_flow: DEBIT
                          amount: 1500.5
                          monetary_unit: CAD
                          acceptance_date_time: '2021-10-23T15:59:05.586Z'
                          effective_date_time: '2021-10-23T15:59:05.586Z'
                          status: PENDING
                          events:
                            - event: PENDING
                              created_date_time: '2021-10-23T15:59:11.174Z'
                      created_date_time: '2021-10-23T16:00:51.615Z'
        '400':
          description: Bad Request -- Validation failure
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '401':
          description: Unauthorized -- Authorization required to access resource
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '404':
          description: 'Resource Not Found -- Any of service account, customer, account, etc.'
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '500':
          description: Internal Server Error -- Unexpected error
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '503':
          description: Service Unavailable -- System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
  '/v1/ledger/transaction/{instruction_ref_id}':
    patch:
      tags:
        - Ledger Transaction
      operationId: commitInstruction
      parameters:
        - description: A unique id  (uuid v4) generated for each request
          in: header
          name: x-pg-interaction-id
          schema:
            description: Uniquely identifies the message.
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[a-zA-Z0-9-.]$'
            example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          required: true
          example: 0a0d74f6-49b5-4b0b-b723-48bdfb142117
        - description: A particular point in progression of time defined (GMT)
          in: header
          name: x-pg-interaction-timestamp
          required: true
          schema:
            type: string
            format: date-time
            example: '2020-05-29T17:19:26.951000Z'
        - description: A unique id that identifies the partner
          in: header
          name: x-pg-profile-id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
        - description: A unique id for ledger account of partner
          in: header
          name: x-pg-account-id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
        - description: A unique id for an instruction
          in: path
          name: instruction_ref_id
          required: true
          schema:
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[0-9a-zA-Z-._]+$'
            example: FILEREF001
      responses:
        '200':
          description: Resource successfully updated
          content:
            application/json:
              schema:
                allOf:
                  - type: object
                    required:
                      - instruction_ref_id
                      - payment_rail
                      - status
                      - transactions
                      - created_date_time
                    properties:
                      instruction_ref_id:
                        type: string
                        minLength: 3
                        maxLength: 36
                        pattern: '^[0-9a-zA-Z-._]+$'
                        example: FILEREF001
                      payment_rail:
                        type: string
                        enum:
                          - EFT
                          - ETRANSFER
                          - WIRES
                          - VISA
                          - INTERNAL
                        example: EFT
                      totals:
                        type: object
                        description: 'Credit and debit totals (count, amount)'
                        properties:
                          debit_count:
                            type: integer
                            description: Number of debit transactions
                            format: int32
                            minimum: 0
                            maximum: 25
                            example: 1
                          debit_total:
                            type: number
                            description: Total amount of debit transactions
                            multipleOf: 0.01
                            minimum: 0
                            example: 1500.5
                          credit_count:
                            type: integer
                            description: Number of credit transactions
                            format: int32
                            minimum: 0
                            maximum: 25
                            example: 1
                          credit_total:
                            type: number
                            description: Total amount of credit transactions
                            multipleOf: 0.01
                            minimum: 0
                            example: 2500.5
                      status:
                        type: string
                        description: Status of the instruction
                        enum:
                          - FAILED
                          - PENDING
                          - ROLLBACKED
                          - POSTED
                        example: PENDING
                      transactions:
                        type: array
                        minItems: 1
                        maxItems: 25
                        items:
                          type: object
                          required:
                            - transaction_ref_id
                            - payment_category
                            - transaction_flow
                            - amount
                            - monetary_unit
                            - acceptance_date_time
                            - effective_date_time
                            - status
                          properties:
                            transaction_ref_id:
                              type: string
                              minLength: 3
                              maxLength: 36
                              pattern: '^[0-9a-zA-Z-._]+$'
                              example: f9443bba-0443-4b71-a17d-42a8057534c8
                            payment_category:
                              type: string
                              description: Category of the payment associated with the payment rail
                              enum:
                                - DEBIT_PULL
                                - CREDIT_PUSH
                                - SEND_PAYMENT
                                - COMPLETE_PAYMENT
                                - ALIAS_DEPOSIT_PAYMENT
                                - ACCOUNT_NUMBER_DEPOSIT_PAYMENT
                                - FULFILL_REQUEST_FOR_PAYMENT
                                - REVERSAL
                                - CORRECTION
                                - PREFUND_RESERVE
                              example: DEBIT_PULL
                            transaction_flow:
                              type: string
                              enum:
                                - CREDIT
                                - DEBIT
                              example: CREDIT
                            amount:
                              type: number
                              multipleOf: 0.01
                              minimum: 0
                              maximum: 1000000
                              example: 123
                            monetary_unit:
                              type: string
                              description: Currency codes as per ISO 4217
                              example: CAD
                              minLength: 3
                              maxLength: 3
                              pattern: '^[A-Z]+$'
                            acceptance_date_time:
                              type: string
                              format: date-time
                              example: '2021-10-23T15:59:05.586Z'
                            due_date_time:
                              type: string
                              format: date-time
                              example: '2021-10-25T15:59:05.586Z'
                            effective_date_time:
                              type: string
                              format: date-time
                              example: '2021-10-23T15:59:05.586Z'
                            events:
                              type: array
                              items:
                                type: object
                                required:
                                  - event
                                  - created_date_time
                                properties:
                                  event:
                                    type: string
                                    enum:
                                      - FAILED
                                      - PENDING
                                      - POSTED
                                      - ROLLBACKED
                                      - REVERSED
                                    example: PENDING
                                  created_date_time:
                                    type: string
                                    format: date-time
                                    example: '2021-10-22T15:59:05.586Z '
                            status:
                              type: string
                              description: Status of the transaction
                              enum:
                                - FAILED
                                - PENDING
                                - POSTED
                                - ROLLBACKED
                                - REVERSED
                              example: PENDING
                      created_date_time:
                        type: string
                        format: date-time
                        example: '2021-10-22T15:59:05.586Z '
                      updated_date_time:
                        type: string
                        format: date-time
                        example: '2021-10-22T15:59:05.586Z'
                  - example:
                      instruction_ref_id: 32e655ed-e162-4168-a924-dcbac87fb127
                      payment_rail: EFT
                      totals:
                        debit_count: 1
                        debit_total: 1500.5
                        credit_count: 1
                        credit_total: 2500.5
                      status: POSTED
                      transactions:
                        - transaction_ref_id: f9443bba-0443-4b71-a17d-42a8057534c8
                          payment_category: DEBIT_PULL
                          transaction_flow: CREDIT
                          amount: 2500.5
                          monetary_unit: CAD
                          acceptance_date_time: '2021-10-23T15:59:05.586Z'
                          due_date_time: '2021-10-23T15:59:05.615Z'
                          effective_date_time: '2021-10-23T15:59:05.615Z'
                          status: POSTED
                          events:
                            - event: PENDING
                              created_date_time: '2021-10-23T15:59:11.174Z'
                            - event: POSTED
                              created_date_time: '2021-10-23T16:02:06.412Z'
                        - transaction_ref_id: 5a530373-2f3a-455f-a66d-751a01e3b0f7
                          payment_category: CREDIT_PUSH
                          transaction_flow: DEBIT
                          amount: 1500.5
                          monetary_unit: CAD
                          acceptance_date_time: '2021-10-23T15:59:05.586Z'
                          effective_date_time: '2021-10-23T15:59:05.586Z'
                          status: POSTED
                          events:
                            - event: PENDING
                              created_date_time: '2021-10-23T15:59:11.174Z'
                            - event: POSTED
                              created_date_time: '2021-10-23T16:02:06.412Z'
                      created_date_time: '2021-10-23T16:00:51.615Z'
                      updated_date_time: '2021-10-23T16:02:06.412Z'
        '400':
          description: Bad Request -- Validation failure
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '401':
          description: Unauthorized -- Authorization required to access resource
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '404':
          description: 'Resource Not Found -- Any of service account, customer, account, etc.'
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '500':
          description: Internal Server Error -- Unexpected error
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '503':
          description: Service Unavailable -- System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
    get:
      tags:
        - Ledger Transaction
      operationId: retrieveInstruction
      parameters:
        - description: A unique id  (uuid v4) generated for each request
          in: header
          name: x-pg-interaction-id
          schema:
            description: Uniquely identifies the message.
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[a-zA-Z0-9-.]$'
            example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          required: true
          example: 0a0d74f6-49b5-4b0b-b723-48bdfb142117
        - description: A particular point in progression of time defined (GMT)
          in: header
          name: x-pg-interaction-timestamp
          required: true
          schema:
            type: string
            format: date-time
            example: '2020-05-29T17:19:26.951000Z'
        - description: A unique id that identifies the partner
          in: header
          name: x-pg-profile-id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
        - description: A unique id for ledger account of partner
          in: header
          name: x-pg-account-id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
        - description: A unique id for an instruction
          in: path
          name: instruction_ref_id
          required: true
          schema:
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[0-9a-zA-Z-._]+$'
            example: FILEREF001
      responses:
        '200':
          description: Resource successfully retrieved
          content:
            application/json:
              schema:
                allOf:
                  - type: object
                    required:
                      - instruction_ref_id
                      - payment_rail
                      - status
                      - transactions
                      - created_date_time
                    properties:
                      instruction_ref_id:
                        type: string
                        minLength: 3
                        maxLength: 36
                        pattern: '^[0-9a-zA-Z-._]+$'
                        example: FILEREF001
                      payment_rail:
                        type: string
                        enum:
                          - EFT
                          - ETRANSFER
                          - WIRES
                          - VISA
                          - INTERNAL
                        example: EFT
                      totals:
                        type: object
                        description: 'Credit and debit totals (count, amount)'
                        properties:
                          debit_count:
                            type: integer
                            description: Number of debit transactions
                            format: int32
                            minimum: 0
                            maximum: 25
                            example: 1
                          debit_total:
                            type: number
                            description: Total amount of debit transactions
                            multipleOf: 0.01
                            minimum: 0
                            example: 1500.5
                          credit_count:
                            type: integer
                            description: Number of credit transactions
                            format: int32
                            minimum: 0
                            maximum: 25
                            example: 1
                          credit_total:
                            type: number
                            description: Total amount of credit transactions
                            multipleOf: 0.01
                            minimum: 0
                            example: 2500.5
                      status:
                        type: string
                        description: Status of the instruction
                        enum:
                          - FAILED
                          - PENDING
                          - ROLLBACKED
                          - POSTED
                        example: PENDING
                      transactions:
                        type: array
                        minItems: 1
                        maxItems: 25
                        items:
                          type: object
                          required:
                            - transaction_ref_id
                            - payment_category
                            - transaction_flow
                            - amount
                            - monetary_unit
                            - acceptance_date_time
                            - effective_date_time
                            - status
                          properties:
                            transaction_ref_id:
                              type: string
                              minLength: 3
                              maxLength: 36
                              pattern: '^[0-9a-zA-Z-._]+$'
                              example: f9443bba-0443-4b71-a17d-42a8057534c8
                            payment_category:
                              type: string
                              description: Category of the payment associated with the payment rail
                              enum:
                                - DEBIT_PULL
                                - CREDIT_PUSH
                                - SEND_PAYMENT
                                - COMPLETE_PAYMENT
                                - ALIAS_DEPOSIT_PAYMENT
                                - ACCOUNT_NUMBER_DEPOSIT_PAYMENT
                                - FULFILL_REQUEST_FOR_PAYMENT
                                - REVERSAL
                                - CORRECTION
                                - PREFUND_RESERVE
                              example: DEBIT_PULL
                            transaction_flow:
                              type: string
                              enum:
                                - CREDIT
                                - DEBIT
                              example: CREDIT
                            amount:
                              type: number
                              multipleOf: 0.01
                              minimum: 0
                              maximum: 1000000
                              example: 123
                            monetary_unit:
                              type: string
                              description: Currency codes as per ISO 4217
                              example: CAD
                              minLength: 3
                              maxLength: 3
                              pattern: '^[A-Z]+$'
                            acceptance_date_time:
                              type: string
                              format: date-time
                              example: '2021-10-23T15:59:05.586Z'
                            due_date_time:
                              type: string
                              format: date-time
                              example: '2021-10-25T15:59:05.586Z'
                            effective_date_time:
                              type: string
                              format: date-time
                              example: '2021-10-23T15:59:05.586Z'
                            events:
                              type: array
                              items:
                                type: object
                                required:
                                  - event
                                  - created_date_time
                                properties:
                                  event:
                                    type: string
                                    enum:
                                      - FAILED
                                      - PENDING
                                      - POSTED
                                      - ROLLBACKED
                                      - REVERSED
                                    example: PENDING
                                  created_date_time:
                                    type: string
                                    format: date-time
                                    example: '2021-10-22T15:59:05.586Z '
                            status:
                              type: string
                              description: Status of the transaction
                              enum:
                                - FAILED
                                - PENDING
                                - POSTED
                                - ROLLBACKED
                                - REVERSED
                              example: PENDING
                      created_date_time:
                        type: string
                        format: date-time
                        example: '2021-10-22T15:59:05.586Z '
                      updated_date_time:
                        type: string
                        format: date-time
                        example: '2021-10-22T15:59:05.586Z'
                  - example:
                      instruction_ref_id: 32e655ed-e162-4168-a924-dcbac87fb127
                      payment_rail: EFT
                      totals:
                        debit_count: 1
                        debit_total: 1500.5
                        credit_count: 1
                        credit_total: 2500.5
                      status: POSTED
                      transactions:
                        - transaction_ref_id: f9443bba-0443-4b71-a17d-42a8057534c8
                          payment_category: DEBIT_PULL
                          transaction_flow: CREDIT
                          amount: 2500.5
                          monetary_unit: CAD
                          acceptance_date_time: '2021-10-23T15:59:05.586Z'
                          due_date_time: '2021-10-23T15:59:05.615Z'
                          effective_date_time: '2021-10-23T15:59:05.615Z'
                          status: POSTED
                          events:
                            - event: PENDING
                              created_date_time: '2021-10-23T15:59:11.174Z'
                            - event: POSTED
                              created_date_time: '2021-10-23T16:02:06.412Z'
                        - transaction_ref_id: 5a530373-2f3a-455f-a66d-751a01e3b0f7
                          payment_category: CREDIT_PUSH
                          transaction_flow: DEBIT
                          amount: 1500.5
                          monetary_unit: CAD
                          acceptance_date_time: '2021-10-23T15:59:05.586Z'
                          effective_date_time: '2021-10-23T15:59:05.586Z'
                          status: POSTED
                          events:
                            - event: PENDING
                              created_date_time: '2021-10-23T15:59:11.174Z'
                            - event: POSTED
                              created_date_time: '2021-10-23T16:02:06.412Z'
                      created_date_time: '2021-10-23T16:00:51.615Z'
                      updated_date_time: '2021-10-23T16:02:06.412Z'
        '400':
          description: Bad Request -- Validation failure
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '401':
          description: Unauthorized -- Authorization required to access resource
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '404':
          description: 'Resource Not Found -- Any of service account, customer, account, etc.'
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '500':
          description: Internal Server Error -- Unexpected error
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '503':
          description: Service Unavailable -- System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
    delete:
      tags:
        - Ledger Transaction
      operationId: rollbackInstruction
      parameters:
        - description: A unique id  (uuid v4) generated for each request
          in: header
          name: x-pg-interaction-id
          schema:
            description: Uniquely identifies the message.
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[a-zA-Z0-9-.]$'
            example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          required: true
          example: 0a0d74f6-49b5-4b0b-b723-48bdfb142117
        - description: A particular point in progression of time defined (GMT)
          in: header
          name: x-pg-interaction-timestamp
          required: true
          schema:
            type: string
            format: date-time
            example: '2020-05-29T17:19:26.951000Z'
        - description: A unique id that identifies the partner
          in: header
          name: x-pg-profile-id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
        - description: A unique id for ledger account of partner
          in: header
          name: x-pg-account-id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
        - description: A unique id for an instruction
          in: path
          name: instruction_ref_id
          required: true
          schema:
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[0-9a-zA-Z-._]+$'
            example: FILEREF001
      responses:
        '204':
          description: Resource successfully rolledback
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
        '400':
          description: Bad Request -- Validation failure
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '401':
          description: Unauthorized -- Authorization required to access resource
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '404':
          description: 'Resource Not Found -- Any of service account, austomer, account, etc.'
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '500':
          description: Internal Server Error -- Unexpected error
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '503':
          description: Service Unavailable -- System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
  '/v1/ledger/transaction/{instruction_ref_id}/{transaction_ref_id}':
    delete:
      tags:
        - Ledger Transaction
      operationId: reverseTransaction
      parameters:
        - description: A unique id  (uuid v4) generated for each request
          in: header
          name: x-pg-interaction-id
          schema:
            description: Uniquely identifies the message.
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[a-zA-Z0-9-.]$'
            example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          required: true
          example: 0a0d74f6-49b5-4b0b-b723-48bdfb142117
        - description: A particular point in progression of time defined (GMT)
          in: header
          name: x-pg-interaction-timestamp
          required: true
          schema:
            type: string
            format: date-time
            example: '2020-05-29T17:19:26.951000Z'
        - description: A unique id that identifies the partner
          in: header
          name: x-pg-profile-id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
        - description: A unique id for ledger account of partner
          in: header
          name: x-pg-account-id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
        - description: A unique id for an instruction
          in: path
          name: instruction_ref_id
          required: true
          schema:
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[0-9a-zA-Z-._]+$'
            example: FILEREF001
        - description: A unique id for a transaction
          in: path
          name: transaction_ref_id
          required: true
          schema:
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[0-9a-zA-Z-._]+$'
            example: f9443bba-0443-4b71-a17d-42a8057534c8
      responses:
        '204':
          description: Resource successfully reversed
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
        '400':
          description: Bad Request -- Validation failure
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '401':
          description: Unauthorized -- Authorization required to access resource
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '404':
          description: 'Resource Not Found -- Any of service account, austomer, account, etc.'
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '500':
          description: Internal Server Error -- Unexpected error
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '503':
          description: Service Unavailable -- System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
    get:
      tags:
        - Ledger Transaction
      operationId: retrieveTransaction
      parameters:
        - description: A unique id  (uuid v4) generated for each request
          in: header
          name: x-pg-interaction-id
          schema:
            description: Uniquely identifies the message.
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[a-zA-Z0-9-.]$'
            example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          required: true
          example: 0a0d74f6-49b5-4b0b-b723-48bdfb142117
        - description: A particular point in progression of time defined (GMT)
          in: header
          name: x-pg-interaction-timestamp
          required: true
          schema:
            type: string
            format: date-time
            example: '2020-05-29T17:19:26.951000Z'
        - description: A unique id that identifies the partner
          in: header
          name: x-pg-profile-id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
        - description: A unique id for ledger account of partner
          in: header
          name: x-pg-account-id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
        - description: A unique id for an instruction
          in: path
          name: instruction_ref_id
          required: true
          schema:
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[0-9a-zA-Z-._]+$'
            example: FILEREF001
        - description: A unique id for a transaction
          in: path
          name: transaction_ref_id
          required: true
          schema:
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[0-9a-zA-Z-._]+$'
            example: f9443bba-0443-4b71-a17d-42a8057534c8
      responses:
        '200':
          description: Resource successfully retrieved
          content:
            application/json:
              schema:
                allOf:
                  - type: object
                    required:
                      - transaction_ref_id
                      - payment_category
                      - transaction_flow
                      - amount
                      - monetary_unit
                      - acceptance_date_time
                      - effective_date_time
                      - status
                    properties:
                      transaction_ref_id:
                        type: string
                        minLength: 3
                        maxLength: 36
                        pattern: '^[0-9a-zA-Z-._]+$'
                        example: f9443bba-0443-4b71-a17d-42a8057534c8
                      payment_category:
                        type: string
                        description: Category of the payment associated with the payment rail
                        enum:
                          - DEBIT_PULL
                          - CREDIT_PUSH
                          - SEND_PAYMENT
                          - COMPLETE_PAYMENT
                          - ALIAS_DEPOSIT_PAYMENT
                          - ACCOUNT_NUMBER_DEPOSIT_PAYMENT
                          - FULFILL_REQUEST_FOR_PAYMENT
                          - REVERSAL
                          - CORRECTION
                          - PREFUND_RESERVE
                        example: DEBIT_PULL
                      transaction_flow:
                        type: string
                        enum:
                          - CREDIT
                          - DEBIT
                        example: CREDIT
                      amount:
                        type: number
                        multipleOf: 0.01
                        minimum: 0
                        maximum: 1000000
                        example: 123
                      monetary_unit:
                        type: string
                        description: Currency codes as per ISO 4217
                        example: CAD
                        minLength: 3
                        maxLength: 3
                        pattern: '^[A-Z]+$'
                      acceptance_date_time:
                        type: string
                        format: date-time
                        example: '2021-10-23T15:59:05.586Z'
                      due_date_time:
                        type: string
                        format: date-time
                        example: '2021-10-25T15:59:05.586Z'
                      effective_date_time:
                        type: string
                        format: date-time
                        example: '2021-10-23T15:59:05.586Z'
                      events:
                        type: array
                        items:
                          type: object
                          required:
                            - event
                            - created_date_time
                          properties:
                            event:
                              type: string
                              enum:
                                - FAILED
                                - PENDING
                                - POSTED
                                - ROLLBACKED
                                - REVERSED
                              example: PENDING
                            created_date_time:
                              type: string
                              format: date-time
                              example: '2021-10-22T15:59:05.586Z '
                      status:
                        type: string
                        description: Status of the transaction
                        enum:
                          - FAILED
                          - PENDING
                          - POSTED
                          - ROLLBACKED
                          - REVERSED
                        example: PENDING
                  - example:
                      transaction_ref_id: 5a530373-2f3a-455f-a66d-751a01e3b0f7
                      payment_category: CREDIT_PUSH
                      transaction_flow: DEBIT
                      amount: 1500.5
                      monetary_unit: CAD
                      acceptance_date_time: '2021-10-23T15:59:05.586Z'
                      effective_date_time: '2021-10-23T15:59:05.586Z'
                      status: POSTED
                      events:
                        - event: PENDING
                          created_date_time: '2021-10-23T15:59:11.174Z'
                        - event: POSTED
                          created_date_time: '2021-10-23T16:02:06.412Z'
        '400':
          description: Bad Request -- Validation failure
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '401':
          description: Unauthorized -- Authorization required to access resource
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '404':
          description: 'Resource Not Found -- Any of service account, austomer, account, etc.'
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '500':
          description: Internal Server Error -- Unexpected error
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '503':
          description: Service Unavailable -- System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
  /v1/ledger/transaction/async:
    post:
      tags:
        - Ledger Transaction Async
      operationId: initiateInstruction1
      parameters:
        - description: A unique id  (uuid v4) generated for each request
          in: header
          name: x-pg-interaction-id
          schema:
            description: Uniquely identifies the message.
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[a-zA-Z0-9-.]$'
            example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          required: true
          example: 0a0d74f6-49b5-4b0b-b723-48bdfb142117
        - description: A particular point in progression of time defined (GMT)
          in: header
          name: x-pg-interaction-timestamp
          required: true
          schema:
            type: string
            format: date-time
            example: '2020-05-29T17:19:26.951000Z'
        - description: A unique id that identifies the partner
          in: header
          name: x-pg-profile-id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
        - description: A unique id for ledger account of partner
          in: header
          name: x-pg-account-id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
                - instruction_ref_id
                - payment_rail
                - transactions
              properties:
                instruction_ref_id:
                  type: string
                  minLength: 3
                  maxLength: 36
                  pattern: '^[0-9a-zA-Z-._]+$'
                  example: FILEREF001
                payment_rail:
                  type: string
                  enum:
                    - EFT
                    - ETRANSFER
                    - WIRES
                    - VISA
                    - INTERNAL
                  example: EFT
                transactions:
                  type: array
                  minItems: 1
                  maxItems: 25
                  items:
                    type: object
                    required:
                      - transaction_ref_id
                      - amount
                      - monetary_unit
                      - acceptance_date_time
                    properties:
                      transaction_ref_id:
                        type: string
                        minLength: 3
                        maxLength: 36
                        pattern: '^[0-9a-zA-Z-._]+$'
                        example: f9443bba-0443-4b71-a17d-42a8057534c8
                      payment_category:
                        type: string
                        description: Category of the payment associated with the payment rail
                        enum:
                          - DEBIT_PULL
                          - CREDIT_PUSH
                          - SEND_PAYMENT
                          - COMPLETE_PAYMENT
                          - ALIAS_DEPOSIT_PAYMENT
                          - ACCOUNT_NUMBER_DEPOSIT_PAYMENT
                          - FULFILL_REQUEST_FOR_PAYMENT
                          - REVERSAL
                          - CORRECTION
                          - PREFUND_RESERVE
                        example: DEBIT_PULL
                      amount:
                        type: number
                        multipleOf: 0.01
                        minimum: 0
                        maximum: 1000000
                        example: 123
                      monetary_unit:
                        type: string
                        description: Currency codes as per ISO 4217
                        example: CAD
                        minLength: 3
                        maxLength: 3
                        pattern: '^[A-Z]+$'
                      acceptance_date_time:
                        type: string
                        format: date-time
                        example: '2021-10-23T15:59:05.586Z'
                      due_date_time:
                        type: string
                        format: date-time
                        example: '2021-10-25T15:59:05.586Z'
              example:
                instruction_ref_id: 32e655ed-e162-4168-a924-dcbac87fb127
                payment_rail: EFT
                transactions:
                  - transaction_ref_id: f9443bba-0443-4b71-a17d-42a8057534c8
                    payment_category: DEBIT_PULL
                    amount: 2500.5
                    monetary_unit: CAD
                    acceptance_date_time: '2021-10-23T15:59:05.586Z'
                    due_date_time: '2021-10-25T15:59:05.586Z'
                  - transaction_ref_id: 5a530373-2f3a-455f-a66d-751a01e3b0f7
                    payment_category: CREDIT_PUSH
                    amount: 1500.5
                    monetary_unit: CAD
                    acceptance_date_time: '2021-10-23T15:59:05.586Z'
        required: true
      responses:
        '204':
          description: Resource successfully created
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
        '400':
          description: Bad Request -- Validation failure
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '401':
          description: Unauthorized -- Authorization required to access resource
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '404':
          description: 'Resource Not Found -- Any of service account, austomer, account, etc.'
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '500':
          description: Internal Server Error -- Unexpected error
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '503':
          description: Service Unavailable -- System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
  '/v1/ledger/transaction/async/{instruction_ref_id}':
    patch:
      tags:
        - Ledger Transaction Async
      operationId: commitInstruction1
      parameters:
        - description: A unique id  (uuid v4) generated for each request
          in: header
          name: x-pg-interaction-id
          schema:
            description: Uniquely identifies the message.
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[a-zA-Z0-9-.]$'
            example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          required: true
          example: 0a0d74f6-49b5-4b0b-b723-48bdfb142117
        - description: A particular point in progression of time defined (GMT)
          in: header
          name: x-pg-interaction-timestamp
          required: true
          schema:
            type: string
            format: date-time
            example: '2020-05-29T17:19:26.951000Z'
        - description: A unique id that identifies the partner
          in: header
          name: x-pg-profile-id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
        - description: A unique id for ledger account of partner
          in: header
          name: x-pg-account-id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
        - description: A unique id for an instruction
          in: path
          name: instruction_ref_id
          required: true
          schema:
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[0-9a-zA-Z-._]+$'
            example: FILEREF001
      responses:
        '204':
          description: Resource successfully updated
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
        '400':
          description: Bad Request -- Validation failure
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '401':
          description: Unauthorized -- Authorization required to access resource
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '404':
          description: 'Resource Not Found -- Any of service account, austomer, account, etc.'
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '500':
          description: Internal Server Error -- Unexpected error
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '503':
          description: Service Unavailable -- System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
    delete:
      tags:
        - Ledger Transaction Async
      operationId: rollbackInstruction1
      parameters:
        - description: A unique id  (uuid v4) generated for each request
          in: header
          name: x-pg-interaction-id
          schema:
            description: Uniquely identifies the message.
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[a-zA-Z0-9-.]$'
            example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          required: true
          example: 0a0d74f6-49b5-4b0b-b723-48bdfb142117
        - description: A particular point in progression of time defined (GMT)
          in: header
          name: x-pg-interaction-timestamp
          required: true
          schema:
            type: string
            format: date-time
            example: '2020-05-29T17:19:26.951000Z'
        - description: A unique id that identifies the partner
          in: header
          name: x-pg-profile-id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
        - description: A unique id for ledger account of partner
          in: header
          name: x-pg-account-id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
        - description: A unique id for an instruction
          in: path
          name: instruction_ref_id
          required: true
          schema:
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[0-9a-zA-Z-._]+$'
            example: FILEREF001
      responses:
        '204':
          description: Resource successfully rolledback
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
        '400':
          description: Bad Request -- Validation failure
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '401':
          description: Unauthorized -- Authorization required to access resource
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '404':
          description: 'Resource Not Found -- Any of service account, austomer, account, etc.'
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '500':
          description: Internal Server Error -- Unexpected error
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '503':
          description: Service Unavailable -- System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
  '/v1/ledger/transaction/async/{instruction_ref_id}/{transaction_ref_id}':
    delete:
      tags:
        - Ledger Transaction Async
      operationId: reverseTransaction1
      parameters:
        - description: A unique id  (uuid v4) generated for each request
          in: header
          name: x-pg-interaction-id
          schema:
            description: Uniquely identifies the message.
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[a-zA-Z0-9-.]$'
            example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          required: true
          example: 0a0d74f6-49b5-4b0b-b723-48bdfb142117
        - description: A particular point in progression of time defined (GMT)
          in: header
          name: x-pg-interaction-timestamp
          required: true
          schema:
            type: string
            format: date-time
            example: '2020-05-29T17:19:26.951000Z'
        - description: A unique id that identifies the partner
          in: header
          name: x-pg-profile-id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
        - description: A unique id for ledger account of partner
          in: header
          name: x-pg-account-id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
        - description: A unique id for an instruction
          in: path
          name: instruction_ref_id
          required: true
          schema:
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[0-9a-zA-Z-._]+$'
            example: FILEREF001
        - description: A unique id for a transaction
          in: path
          name: transaction_ref_id
          required: true
          schema:
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[0-9a-zA-Z-._]+$'
            example: f9443bba-0443-4b71-a17d-42a8057534c8
      responses:
        '204':
          description: Resource successfully reversed
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
        '400':
          description: Bad Request -- Validation failure
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '401':
          description: Unauthorized -- Authorization required to access resource
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '404':
          description: 'Resource Not Found -- Any of service account, austomer, account, etc.'
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '500':
          description: Internal Server Error -- Unexpected error
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '503':
          description: Service Unavailable -- System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
components:
  schemas:
    LedgerTransaction:
      type: object
      required:
        - transaction_ref_id
        - payment_category
        - transaction_flow
        - amount
        - monetary_unit
        - acceptance_date_time
        - effective_date_time
        - status
      properties:
        transaction_ref_id:
          type: string
          minLength: 3
          maxLength: 36
          pattern: '^[0-9a-zA-Z-._]+$'
          example: f9443bba-0443-4b71-a17d-42a8057534c8
        payment_category:
          type: string
          description: Category of the payment associated with the payment rail
          enum:
            - DEBIT_PULL
            - CREDIT_PUSH
            - SEND_PAYMENT
            - COMPLETE_PAYMENT
            - ALIAS_DEPOSIT_PAYMENT
            - ACCOUNT_NUMBER_DEPOSIT_PAYMENT
            - FULFILL_REQUEST_FOR_PAYMENT
            - REVERSAL
            - CORRECTION
            - PREFUND_RESERVE
          example: DEBIT_PULL
        transaction_flow:
          type: string
          enum:
            - CREDIT
            - DEBIT
          example: CREDIT
        amount:
          type: number
          multipleOf: 0.01
          minimum: 0
          maximum: 1000000
          example: 123
        monetary_unit:
          type: string
          description: Currency codes as per ISO 4217
          example: CAD
          minLength: 3
          maxLength: 3
          pattern: '^[A-Z]+$'
        acceptance_date_time:
          type: string
          format: date-time
          example: '2021-10-23T15:59:05.586Z'
        due_date_time:
          type: string
          format: date-time
          example: '2021-10-25T15:59:05.586Z'
        effective_date_time:
          type: string
          format: date-time
          example: '2021-10-23T15:59:05.586Z'
        events:
          type: array
          items:
            type: object
            required:
              - event
              - created_date_time
            properties:
              event:
                type: string
                enum:
                  - FAILED
                  - PENDING
                  - POSTED
                  - ROLLBACKED
                  - REVERSED
                example: PENDING
              created_date_time:
                type: string
                format: date-time
                example: '2021-10-22T15:59:05.586Z '
        status:
          type: string
          description: Status of the transaction
          enum:
            - FAILED
            - PENDING
            - POSTED
            - ROLLBACKED
            - REVERSED
          example: PENDING
    LedgerInstruction:
      type: object
      required:
        - instruction_ref_id
        - payment_rail
        - status
        - transactions
        - created_date_time
      properties:
        instruction_ref_id:
          type: string
          minLength: 3
          maxLength: 36
          pattern: '^[0-9a-zA-Z-._]+$'
          example: FILEREF001
        payment_rail:
          type: string
          enum:
            - EFT
            - ETRANSFER
            - WIRES
            - VISA
            - INTERNAL
          example: EFT
        totals:
          type: object
          description: 'Credit and debit totals (count, amount)'
          properties:
            debit_count:
              type: integer
              description: Number of debit transactions
              format: int32
              minimum: 0
              maximum: 25
              example: 1
            debit_total:
              type: number
              description: Total amount of debit transactions
              multipleOf: 0.01
              minimum: 0
              example: 1500.5
            credit_count:
              type: integer
              description: Number of credit transactions
              format: int32
              minimum: 0
              maximum: 25
              example: 1
            credit_total:
              type: number
              description: Total amount of credit transactions
              multipleOf: 0.01
              minimum: 0
              example: 2500.5
        status:
          type: string
          description: Status of the instruction
          enum:
            - FAILED
            - PENDING
            - ROLLBACKED
            - POSTED
          example: PENDING
        transactions:
          type: array
          minItems: 1
          maxItems: 25
          items:
            type: object
            required:
              - transaction_ref_id
              - payment_category
              - transaction_flow
              - amount
              - monetary_unit
              - acceptance_date_time
              - effective_date_time
              - status
            properties:
              transaction_ref_id:
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[0-9a-zA-Z-._]+$'
                example: f9443bba-0443-4b71-a17d-42a8057534c8
              payment_category:
                type: string
                description: Category of the payment associated with the payment rail
                enum:
                  - DEBIT_PULL
                  - CREDIT_PUSH
                  - SEND_PAYMENT
                  - COMPLETE_PAYMENT
                  - ALIAS_DEPOSIT_PAYMENT
                  - ACCOUNT_NUMBER_DEPOSIT_PAYMENT
                  - FULFILL_REQUEST_FOR_PAYMENT
                  - REVERSAL
                  - CORRECTION
                  - PREFUND_RESERVE
                example: DEBIT_PULL
              transaction_flow:
                type: string
                enum:
                  - CREDIT
                  - DEBIT
                example: CREDIT
              amount:
                type: number
                multipleOf: 0.01
                minimum: 0
                maximum: 1000000
                example: 123
              monetary_unit:
                type: string
                description: Currency codes as per ISO 4217
                example: CAD
                minLength: 3
                maxLength: 3
                pattern: '^[A-Z]+$'
              acceptance_date_time:
                type: string
                format: date-time
                example: '2021-10-23T15:59:05.586Z'
              due_date_time:
                type: string
                format: date-time
                example: '2021-10-25T15:59:05.586Z'
              effective_date_time:
                type: string
                format: date-time
                example: '2021-10-23T15:59:05.586Z'
              events:
                type: array
                items:
                  type: object
                  required:
                    - event
                    - created_date_time
                  properties:
                    event:
                      type: string
                      enum:
                        - FAILED
                        - PENDING
                        - POSTED
                        - ROLLBACKED
                        - REVERSED
                      example: PENDING
                    created_date_time:
                      type: string
                      format: date-time
                      example: '2021-10-22T15:59:05.586Z '
              status:
                type: string
                description: Status of the transaction
                enum:
                  - FAILED
                  - PENDING
                  - POSTED
                  - ROLLBACKED
                  - REVERSED
                example: PENDING
        created_date_time:
          type: string
          format: date-time
          example: '2021-10-22T15:59:05.586Z '
        updated_date_time:
          type: string
          format: date-time
          example: '2021-10-22T15:59:05.586Z'
    InitiateLedgerTransactionRequest:
      type: object
      required:
        - instruction_ref_id
        - payment_rail
        - transactions
      properties:
        instruction_ref_id:
          type: string
          minLength: 3
          maxLength: 36
          pattern: '^[0-9a-zA-Z-._]+$'
          example: FILEREF001
        payment_rail:
          type: string
          enum:
            - EFT
            - ETRANSFER
            - WIRES
            - VISA
            - INTERNAL
          example: EFT
        transactions:
          type: array
          minItems: 1
          maxItems: 25
          items:
            type: object
            required:
              - transaction_ref_id
              - payment_category
              - amount
              - monetary_unit
              - acceptance_date_time
            properties:
              transaction_ref_id:
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[0-9a-zA-Z-._]+$'
                example: f9443bba-0443-4b71-a17d-42a8057534c8
              payment_category:
                type: string
                description: Category of the payment associated with the payment rail
                enum:
                  - DEBIT_PULL
                  - CREDIT_PUSH
                  - SEND_PAYMENT
                  - COMPLETE_PAYMENT
                  - ALIAS_DEPOSIT_PAYMENT
                  - ACCOUNT_NUMBER_DEPOSIT_PAYMENT
                  - FULFILL_REQUEST_FOR_PAYMENT
                  - REVERSAL
                  - CORRECTION
                  - PREFUND_RESERVE
                example: DEBIT_PULL
              amount:
                type: number
                multipleOf: 0.01
                minimum: 0
                maximum: 1000000
                example: 123
              monetary_unit:
                type: string
                description: Currency codes as per ISO 4217
                example: CAD
                minLength: 3
                maxLength: 3
                pattern: '^[A-Z]+$'
              acceptance_date_time:
                type: string
                format: date-time
                example: '2021-10-23T15:59:05.586Z'
              due_date_time:
                type: string
                format: date-time
                example: '2021-10-25T15:59:05.586Z'
      example:
        instruction_ref_id: 32e655ed-e162-4168-a924-dcbac87fb127
        payment_rail: EFT
        transactions:
          - transaction_ref_id: f9443bba-0443-4b71-a17d-42a8057534c8
            payment_category: DEBIT_PULL
            amount: 2500.5
            monetary_unit: CAD
            acceptance_date_time: '2021-10-23T15:59:05.586Z'
            due_date_time: '2021-10-25T15:59:05.586Z'
          - transaction_ref_id: 5a530373-2f3a-455f-a66d-751a01e3b0f7
            payment_category: CREDIT_PUSH
            amount: 1500.5
            monetary_unit: CAD
            acceptance_date_time: '2021-10-23T15:59:05.586Z'
    InitiateLedgerTransactionResponse:
      allOf:
        - type: object
          required:
            - instruction_ref_id
            - payment_rail
            - status
            - transactions
            - created_date_time
          properties:
            instruction_ref_id:
              type: string
              minLength: 3
              maxLength: 36
              pattern: '^[0-9a-zA-Z-._]+$'
              example: FILEREF001
            payment_rail:
              type: string
              enum:
                - EFT
                - ETRANSFER
                - WIRES
                - VISA
                - INTERNAL
              example: EFT
            totals:
              type: object
              description: 'Credit and debit totals (count, amount)'
              properties:
                debit_count:
                  type: integer
                  description: Number of debit transactions
                  format: int32
                  minimum: 0
                  maximum: 25
                  example: 1
                debit_total:
                  type: number
                  description: Total amount of debit transactions
                  multipleOf: 0.01
                  minimum: 0
                  example: 1500.5
                credit_count:
                  type: integer
                  description: Number of credit transactions
                  format: int32
                  minimum: 0
                  maximum: 25
                  example: 1
                credit_total:
                  type: number
                  description: Total amount of credit transactions
                  multipleOf: 0.01
                  minimum: 0
                  example: 2500.5
            status:
              type: string
              description: Status of the instruction
              enum:
                - FAILED
                - PENDING
                - ROLLBACKED
                - POSTED
              example: PENDING
            transactions:
              type: array
              minItems: 1
              maxItems: 25
              items:
                type: object
                required:
                  - transaction_ref_id
                  - payment_category
                  - transaction_flow
                  - amount
                  - monetary_unit
                  - acceptance_date_time
                  - effective_date_time
                  - status
                properties:
                  transaction_ref_id:
                    type: string
                    minLength: 3
                    maxLength: 36
                    pattern: '^[0-9a-zA-Z-._]+$'
                    example: f9443bba-0443-4b71-a17d-42a8057534c8
                  payment_category:
                    type: string
                    description: Category of the payment associated with the payment rail
                    enum:
                      - DEBIT_PULL
                      - CREDIT_PUSH
                      - SEND_PAYMENT
                      - COMPLETE_PAYMENT
                      - ALIAS_DEPOSIT_PAYMENT
                      - ACCOUNT_NUMBER_DEPOSIT_PAYMENT
                      - FULFILL_REQUEST_FOR_PAYMENT
                      - REVERSAL
                      - CORRECTION
                      - PREFUND_RESERVE
                    example: DEBIT_PULL
                  transaction_flow:
                    type: string
                    enum:
                      - CREDIT
                      - DEBIT
                    example: CREDIT
                  amount:
                    type: number
                    multipleOf: 0.01
                    minimum: 0
                    maximum: 1000000
                    example: 123
                  monetary_unit:
                    type: string
                    description: Currency codes as per ISO 4217
                    example: CAD
                    minLength: 3
                    maxLength: 3
                    pattern: '^[A-Z]+$'
                  acceptance_date_time:
                    type: string
                    format: date-time
                    example: '2021-10-23T15:59:05.586Z'
                  due_date_time:
                    type: string
                    format: date-time
                    example: '2021-10-25T15:59:05.586Z'
                  effective_date_time:
                    type: string
                    format: date-time
                    example: '2021-10-23T15:59:05.586Z'
                  events:
                    type: array
                    items:
                      type: object
                      required:
                        - event
                        - created_date_time
                      properties:
                        event:
                          type: string
                          enum:
                            - FAILED
                            - PENDING
                            - POSTED
                            - ROLLBACKED
                            - REVERSED
                          example: PENDING
                        created_date_time:
                          type: string
                          format: date-time
                          example: '2021-10-22T15:59:05.586Z '
                  status:
                    type: string
                    description: Status of the transaction
                    enum:
                      - FAILED
                      - PENDING
                      - POSTED
                      - ROLLBACKED
                      - REVERSED
                    example: PENDING
            created_date_time:
              type: string
              format: date-time
              example: '2021-10-22T15:59:05.586Z '
            updated_date_time:
              type: string
              format: date-time
              example: '2021-10-22T15:59:05.586Z'
        - example:
            instruction_ref_id: 32e655ed-e162-4168-a924-dcbac87fb127
            payment_rail: EFT
            totals:
              debit_count: 1
              debit_total: 1500.5
              credit_count: 1
              credit_total: 2500.5
            status: PENDING
            transactions:
              - transaction_ref_id: f9443bba-0443-4b71-a17d-42a8057534c8
                payment_category: DEBIT_PULL
                transaction_flow: CREDIT
                amount: 2500.5
                monetary_unit: CAD
                acceptance_date_time: '2021-10-23T15:59:05.586Z'
                due_date_time: '2021-10-23T15:59:05.615Z'
                effective_date_time: '2021-10-23T15:59:05.615Z'
                status: PENDING
                events:
                  - event: PENDING
                    created_date_time: '2021-10-23T15:59:11.174Z'
              - transaction_ref_id: 5a530373-2f3a-455f-a66d-751a01e3b0f7
                payment_category: CREDIT_PUSH
                transaction_flow: DEBIT
                amount: 1500.5
                monetary_unit: CAD
                acceptance_date_time: '2021-10-23T15:59:05.586Z'
                effective_date_time: '2021-10-23T15:59:05.586Z'
                status: PENDING
                events:
                  - event: PENDING
                    created_date_time: '2021-10-23T15:59:11.174Z'
            created_date_time: '2021-10-23T16:00:51.615Z'
    CommitLedgerTransactionResponse:
      allOf:
        - type: object
          required:
            - instruction_ref_id
            - payment_rail
            - status
            - transactions
            - created_date_time
          properties:
            instruction_ref_id:
              type: string
              minLength: 3
              maxLength: 36
              pattern: '^[0-9a-zA-Z-._]+$'
              example: FILEREF001
            payment_rail:
              type: string
              enum:
                - EFT
                - ETRANSFER
                - WIRES
                - VISA
                - INTERNAL
              example: EFT
            totals:
              type: object
              description: 'Credit and debit totals (count, amount)'
              properties:
                debit_count:
                  type: integer
                  description: Number of debit transactions
                  format: int32
                  minimum: 0
                  maximum: 25
                  example: 1
                debit_total:
                  type: number
                  description: Total amount of debit transactions
                  multipleOf: 0.01
                  minimum: 0
                  example: 1500.5
                credit_count:
                  type: integer
                  description: Number of credit transactions
                  format: int32
                  minimum: 0
                  maximum: 25
                  example: 1
                credit_total:
                  type: number
                  description: Total amount of credit transactions
                  multipleOf: 0.01
                  minimum: 0
                  example: 2500.5
            status:
              type: string
              description: Status of the instruction
              enum:
                - FAILED
                - PENDING
                - ROLLBACKED
                - POSTED
              example: PENDING
            transactions:
              type: array
              minItems: 1
              maxItems: 25
              items:
                type: object
                required:
                  - transaction_ref_id
                  - payment_category
                  - transaction_flow
                  - amount
                  - monetary_unit
                  - acceptance_date_time
                  - effective_date_time
                  - status
                properties:
                  transaction_ref_id:
                    type: string
                    minLength: 3
                    maxLength: 36
                    pattern: '^[0-9a-zA-Z-._]+$'
                    example: f9443bba-0443-4b71-a17d-42a8057534c8
                  payment_category:
                    type: string
                    description: Category of the payment associated with the payment rail
                    enum:
                      - DEBIT_PULL
                      - CREDIT_PUSH
                      - SEND_PAYMENT
                      - COMPLETE_PAYMENT
                      - ALIAS_DEPOSIT_PAYMENT
                      - ACCOUNT_NUMBER_DEPOSIT_PAYMENT
                      - FULFILL_REQUEST_FOR_PAYMENT
                      - REVERSAL
                      - CORRECTION
                      - PREFUND_RESERVE
                    example: DEBIT_PULL
                  transaction_flow:
                    type: string
                    enum:
                      - CREDIT
                      - DEBIT
                    example: CREDIT
                  amount:
                    type: number
                    multipleOf: 0.01
                    minimum: 0
                    maximum: 1000000
                    example: 123
                  monetary_unit:
                    type: string
                    description: Currency codes as per ISO 4217
                    example: CAD
                    minLength: 3
                    maxLength: 3
                    pattern: '^[A-Z]+$'
                  acceptance_date_time:
                    type: string
                    format: date-time
                    example: '2021-10-23T15:59:05.586Z'
                  due_date_time:
                    type: string
                    format: date-time
                    example: '2021-10-25T15:59:05.586Z'
                  effective_date_time:
                    type: string
                    format: date-time
                    example: '2021-10-23T15:59:05.586Z'
                  events:
                    type: array
                    items:
                      type: object
                      required:
                        - event
                        - created_date_time
                      properties:
                        event:
                          type: string
                          enum:
                            - FAILED
                            - PENDING
                            - POSTED
                            - ROLLBACKED
                            - REVERSED
                          example: PENDING
                        created_date_time:
                          type: string
                          format: date-time
                          example: '2021-10-22T15:59:05.586Z '
                  status:
                    type: string
                    description: Status of the transaction
                    enum:
                      - FAILED
                      - PENDING
                      - POSTED
                      - ROLLBACKED
                      - REVERSED
                    example: PENDING
            created_date_time:
              type: string
              format: date-time
              example: '2021-10-22T15:59:05.586Z '
            updated_date_time:
              type: string
              format: date-time
              example: '2021-10-22T15:59:05.586Z'
        - example:
            instruction_ref_id: 32e655ed-e162-4168-a924-dcbac87fb127
            payment_rail: EFT
            totals:
              debit_count: 1
              debit_total: 1500.5
              credit_count: 1
              credit_total: 2500.5
            status: POSTED
            transactions:
              - transaction_ref_id: f9443bba-0443-4b71-a17d-42a8057534c8
                payment_category: DEBIT_PULL
                transaction_flow: CREDIT
                amount: 2500.5
                monetary_unit: CAD
                acceptance_date_time: '2021-10-23T15:59:05.586Z'
                due_date_time: '2021-10-23T15:59:05.615Z'
                effective_date_time: '2021-10-23T15:59:05.615Z'
                status: POSTED
                events:
                  - event: PENDING
                    created_date_time: '2021-10-23T15:59:11.174Z'
                  - event: POSTED
                    created_date_time: '2021-10-23T16:02:06.412Z'
              - transaction_ref_id: 5a530373-2f3a-455f-a66d-751a01e3b0f7
                payment_category: CREDIT_PUSH
                transaction_flow: DEBIT
                amount: 1500.5
                monetary_unit: CAD
                acceptance_date_time: '2021-10-23T15:59:05.586Z'
                effective_date_time: '2021-10-23T15:59:05.586Z'
                status: POSTED
                events:
                  - event: PENDING
                    created_date_time: '2021-10-23T15:59:11.174Z'
                  - event: POSTED
                    created_date_time: '2021-10-23T16:02:06.412Z'
            created_date_time: '2021-10-23T16:00:51.615Z'
            updated_date_time: '2021-10-23T16:02:06.412Z'
    RetrieveLedgerTransactionResponse:
      allOf:
        - type: object
          required:
            - transaction_ref_id
            - payment_category
            - transaction_flow
            - amount
            - monetary_unit
            - acceptance_date_time
            - effective_date_time
            - status
          properties:
            transaction_ref_id:
              type: string
              minLength: 3
              maxLength: 36
              pattern: '^[0-9a-zA-Z-._]+$'
              example: f9443bba-0443-4b71-a17d-42a8057534c8
            payment_category:
              type: string
              description: Category of the payment associated with the payment rail
              enum:
                - DEBIT_PULL
                - CREDIT_PUSH
                - SEND_PAYMENT
                - COMPLETE_PAYMENT
                - ALIAS_DEPOSIT_PAYMENT
                - ACCOUNT_NUMBER_DEPOSIT_PAYMENT
                - FULFILL_REQUEST_FOR_PAYMENT
                - REVERSAL
                - CORRECTION
                - PREFUND_RESERVE
              example: DEBIT_PULL
            transaction_flow:
              type: string
              enum:
                - CREDIT
                - DEBIT
              example: CREDIT
            amount:
              type: number
              multipleOf: 0.01
              minimum: 0
              maximum: 1000000
              example: 123
            monetary_unit:
              type: string
              description: Currency codes as per ISO 4217
              example: CAD
              minLength: 3
              maxLength: 3
              pattern: '^[A-Z]+$'
            acceptance_date_time:
              type: string
              format: date-time
              example: '2021-10-23T15:59:05.586Z'
            due_date_time:
              type: string
              format: date-time
              example: '2021-10-25T15:59:05.586Z'
            effective_date_time:
              type: string
              format: date-time
              example: '2021-10-23T15:59:05.586Z'
            events:
              type: array
              items:
                type: object
                required:
                  - event
                  - created_date_time
                properties:
                  event:
                    type: string
                    enum:
                      - FAILED
                      - PENDING
                      - POSTED
                      - ROLLBACKED
                      - REVERSED
                    example: PENDING
                  created_date_time:
                    type: string
                    format: date-time
                    example: '2021-10-22T15:59:05.586Z '
            status:
              type: string
              description: Status of the transaction
              enum:
                - FAILED
                - PENDING
                - POSTED
                - ROLLBACKED
                - REVERSED
              example: PENDING
        - example:
            transaction_ref_id: 5a530373-2f3a-455f-a66d-751a01e3b0f7
            payment_category: CREDIT_PUSH
            transaction_flow: DEBIT
            amount: 1500.5
            monetary_unit: CAD
            acceptance_date_time: '2021-10-23T15:59:05.586Z'
            effective_date_time: '2021-10-23T15:59:05.586Z'
            status: POSTED
            events:
              - event: PENDING
                created_date_time: '2021-10-23T15:59:11.174Z'
              - event: POSTED
                created_date_time: '2021-10-23T16:02:06.412Z'
    RetrieveLedgerTransactionsResponse:
      allOf:
        - type: object
          required:
            - instruction_ref_id
            - payment_rail
            - status
            - transactions
            - created_date_time
          properties:
            instruction_ref_id:
              type: string
              minLength: 3
              maxLength: 36
              pattern: '^[0-9a-zA-Z-._]+$'
              example: FILEREF001
            payment_rail:
              type: string
              enum:
                - EFT
                - ETRANSFER
                - WIRES
                - VISA
                - INTERNAL
              example: EFT
            totals:
              type: object
              description: 'Credit and debit totals (count, amount)'
              properties:
                debit_count:
                  type: integer
                  description: Number of debit transactions
                  format: int32
                  minimum: 0
                  maximum: 25
                  example: 1
                debit_total:
                  type: number
                  description: Total amount of debit transactions
                  multipleOf: 0.01
                  minimum: 0
                  example: 1500.5
                credit_count:
                  type: integer
                  description: Number of credit transactions
                  format: int32
                  minimum: 0
                  maximum: 25
                  example: 1
                credit_total:
                  type: number
                  description: Total amount of credit transactions
                  multipleOf: 0.01
                  minimum: 0
                  example: 2500.5
            status:
              type: string
              description: Status of the instruction
              enum:
                - FAILED
                - PENDING
                - ROLLBACKED
                - POSTED
              example: PENDING
            transactions:
              type: array
              minItems: 1
              maxItems: 25
              items:
                type: object
                required:
                  - transaction_ref_id
                  - payment_category
                  - transaction_flow
                  - amount
                  - monetary_unit
                  - acceptance_date_time
                  - effective_date_time
                  - status
                properties:
                  transaction_ref_id:
                    type: string
                    minLength: 3
                    maxLength: 36
                    pattern: '^[0-9a-zA-Z-._]+$'
                    example: f9443bba-0443-4b71-a17d-42a8057534c8
                  payment_category:
                    type: string
                    description: Category of the payment associated with the payment rail
                    enum:
                      - DEBIT_PULL
                      - CREDIT_PUSH
                      - SEND_PAYMENT
                      - COMPLETE_PAYMENT
                      - ALIAS_DEPOSIT_PAYMENT
                      - ACCOUNT_NUMBER_DEPOSIT_PAYMENT
                      - FULFILL_REQUEST_FOR_PAYMENT
                      - REVERSAL
                      - CORRECTION
                      - PREFUND_RESERVE
                    example: DEBIT_PULL
                  transaction_flow:
                    type: string
                    enum:
                      - CREDIT
                      - DEBIT
                    example: CREDIT
                  amount:
                    type: number
                    multipleOf: 0.01
                    minimum: 0
                    maximum: 1000000
                    example: 123
                  monetary_unit:
                    type: string
                    description: Currency codes as per ISO 4217
                    example: CAD
                    minLength: 3
                    maxLength: 3
                    pattern: '^[A-Z]+$'
                  acceptance_date_time:
                    type: string
                    format: date-time
                    example: '2021-10-23T15:59:05.586Z'
                  due_date_time:
                    type: string
                    format: date-time
                    example: '2021-10-25T15:59:05.586Z'
                  effective_date_time:
                    type: string
                    format: date-time
                    example: '2021-10-23T15:59:05.586Z'
                  events:
                    type: array
                    items:
                      type: object
                      required:
                        - event
                        - created_date_time
                      properties:
                        event:
                          type: string
                          enum:
                            - FAILED
                            - PENDING
                            - POSTED
                            - ROLLBACKED
                            - REVERSED
                          example: PENDING
                        created_date_time:
                          type: string
                          format: date-time
                          example: '2021-10-22T15:59:05.586Z '
                  status:
                    type: string
                    description: Status of the transaction
                    enum:
                      - FAILED
                      - PENDING
                      - POSTED
                      - ROLLBACKED
                      - REVERSED
                    example: PENDING
            created_date_time:
              type: string
              format: date-time
              example: '2021-10-22T15:59:05.586Z '
            updated_date_time:
              type: string
              format: date-time
              example: '2021-10-22T15:59:05.586Z'
        - example:
            instruction_ref_id: 32e655ed-e162-4168-a924-dcbac87fb127
            payment_rail: EFT
            totals:
              debit_count: 1
              debit_total: 1500.5
              credit_count: 1
              credit_total: 2500.5
            status: POSTED
            transactions:
              - transaction_ref_id: f9443bba-0443-4b71-a17d-42a8057534c8
                payment_category: DEBIT_PULL
                transaction_flow: CREDIT
                amount: 2500.5
                monetary_unit: CAD
                acceptance_date_time: '2021-10-23T15:59:05.586Z'
                due_date_time: '2021-10-23T15:59:05.615Z'
                effective_date_time: '2021-10-23T15:59:05.615Z'
                status: POSTED
                events:
                  - event: PENDING
                    created_date_time: '2021-10-23T15:59:11.174Z'
                  - event: POSTED
                    created_date_time: '2021-10-23T16:02:06.412Z'
              - transaction_ref_id: 5a530373-2f3a-455f-a66d-751a01e3b0f7
                payment_category: CREDIT_PUSH
                transaction_flow: DEBIT
                amount: 1500.5
                monetary_unit: CAD
                acceptance_date_time: '2021-10-23T15:59:05.586Z'
                effective_date_time: '2021-10-23T15:59:05.586Z'
                status: POSTED
                events:
                  - event: PENDING
                    created_date_time: '2021-10-23T15:59:11.174Z'
                  - event: POSTED
                    created_date_time: '2021-10-23T16:02:06.412Z'
            created_date_time: '2021-10-23T16:00:51.615Z'
            updated_date_time: '2021-10-23T16:02:06.412Z'
    InitiateLedgerTransactionAsyncRequest:
      type: object
      required:
        - instruction_ref_id
        - payment_rail
        - transactions
      properties:
        instruction_ref_id:
          type: string
          minLength: 3
          maxLength: 36
          pattern: '^[0-9a-zA-Z-._]+$'
          example: FILEREF001
        payment_rail:
          type: string
          enum:
            - EFT
            - ETRANSFER
            - WIRES
            - VISA
            - INTERNAL
          example: EFT
        transactions:
          type: array
          minItems: 1
          maxItems: 25
          items:
            type: object
            required:
              - transaction_ref_id
              - amount
              - monetary_unit
              - acceptance_date_time
            properties:
              transaction_ref_id:
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[0-9a-zA-Z-._]+$'
                example: f9443bba-0443-4b71-a17d-42a8057534c8
              payment_category:
                type: string
                description: Category of the payment associated with the payment rail
                enum:
                  - DEBIT_PULL
                  - CREDIT_PUSH
                  - SEND_PAYMENT
                  - COMPLETE_PAYMENT
                  - ALIAS_DEPOSIT_PAYMENT
                  - ACCOUNT_NUMBER_DEPOSIT_PAYMENT
                  - FULFILL_REQUEST_FOR_PAYMENT
                  - REVERSAL
                  - CORRECTION
                  - PREFUND_RESERVE
                example: DEBIT_PULL
              amount:
                type: number
                multipleOf: 0.01
                minimum: 0
                maximum: 1000000
                example: 123
              monetary_unit:
                type: string
                description: Currency codes as per ISO 4217
                example: CAD
                minLength: 3
                maxLength: 3
                pattern: '^[A-Z]+$'
              acceptance_date_time:
                type: string
                format: date-time
                example: '2021-10-23T15:59:05.586Z'
              due_date_time:
                type: string
                format: date-time
                example: '2021-10-25T15:59:05.586Z'
      example:
        instruction_ref_id: 32e655ed-e162-4168-a924-dcbac87fb127
        payment_rail: EFT
        transactions:
          - transaction_ref_id: f9443bba-0443-4b71-a17d-42a8057534c8
            payment_category: DEBIT_PULL
            amount: 2500.5
            monetary_unit: CAD
            acceptance_date_time: '2021-10-23T15:59:05.586Z'
            due_date_time: '2021-10-25T15:59:05.586Z'
          - transaction_ref_id: 5a530373-2f3a-455f-a66d-751a01e3b0f7
            payment_category: CREDIT_PUSH
            amount: 1500.5
            monetary_unit: CAD
            acceptance_date_time: '2021-10-23T15:59:05.586Z'
