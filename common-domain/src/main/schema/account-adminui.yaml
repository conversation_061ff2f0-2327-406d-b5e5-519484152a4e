openapi: 3.0.2
paths:
  #########################################################################################################
  /v1/ledger/account:
    post:
      tags:
        - Ledger Account
      operationId: createAccount
      parameters:
        # Headers -- General #
        - $ref: 'common/common.yaml#/components/parameters/InteractionId'
        - $ref: 'common/common.yaml#/components/parameters/InteractionTimestamp'
        # Headers #
        - $ref: 'common/common.yaml#/components/parameters/ProfileRefIdHeader'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateLedgerAccountRequest'
      responses:
        '201':
          description: Account succesfully created
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateLedgerAccountResponse'
        '400':
          description: Bad Request
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '404':
          description: Profile not found
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '500':
          description: Unexpected internal system error
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '503':
          description: System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'

    #########################################################################################################
    get:
      tags:
        - Ledger Account
      parameters:
        # Headers -- General #
        - $ref: 'common/common.yaml#/components/parameters/InteractionId'
        - $ref: 'common/common.yaml#/components/parameters/InteractionTimestamp'
        # Headers #
        - $ref: 'common/common.yaml#/components/parameters/ProfileRefIdHeader'
      operationId: retrieveAccounts
      responses:
        '200':
          description: account retrieved successfully
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RetrieveLedgerAccountsResponse'
        '400':
          description: Bad Request
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '404':
          description: Profile not found
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '500':
          description: Unexpected internal system error
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '503':
          description: System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'

  #########################################################################################################
  /v1/ledger/account/{account_ref_id}:
    get:
      tags:
        - Ledger Account
      parameters:
        # Headers -- General #
        - $ref: 'common/common.yaml#/components/parameters/InteractionId'
        - $ref: 'common/common.yaml#/components/parameters/InteractionTimestamp'
        # Headers #
        - $ref: 'common/common.yaml#/components/parameters/ProfileRefIdHeader'
        # Path Parameters #
        - $ref: 'common/common.yaml#/components/parameters/AccountRefIdPath'
      operationId: retrieveAccount
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RetrieveLedgerAccountResponse'
        '400':
          description: Bad Request
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '404':
          description: account not found
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '500':
          description: Unexpected internal system error
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '503':
          description: System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'

    #########################################################################################################
    put:
      tags:
        - Ledger Account
      operationId: updateAccount
      parameters:
        # Headers -- General #
        - $ref: 'common/common.yaml#/components/parameters/InteractionId'
        - $ref: 'common/common.yaml#/components/parameters/InteractionTimestamp'
        # Headers #
        - $ref: 'common/common.yaml#/components/parameters/ProfileRefIdHeader'
        # Path Parameters #
        - $ref: 'common/common.yaml#/components/parameters/AccountRefIdPath'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateLedgerAccountRequest'
        required: true
      responses:
        '200':
          description: updated successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UpdateLedgerAccountResponse'
        '400':
          description: Bad Request
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '404':
          description: Account not found
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '500':
          description: Unexpected internal system error
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '503':
          description: System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'

  #########################################################################################################
  /v1/ledger/account/{account_ref_id}/status:
    patch:
      tags:
        - Ledger Account
      parameters:
        # Headers -- General #
        - $ref: 'common/common.yaml#/components/parameters/InteractionId'
        - $ref: 'common/common.yaml#/components/parameters/InteractionTimestamp'
        # Headers #
        - $ref: 'common/common.yaml#/components/parameters/ProfileRefIdHeader'
        # Path Parameters #
        - $ref: 'common/common.yaml#/components/parameters/AccountRefIdPath'
      operationId: updateAccountStatus
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateLedgerAccountStatusRequest'
      responses:
        '204':
          description: Account status updated
        '400':
          description: Bad Request
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '404':
          description: account not found
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '500':
          description: Unexpected internal system error
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '503':
          description: System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'

  #########################################################################################################
  /v1/ledger/account/balance_search/{account_ref_id}:
    get:
      tags:
        - Ledger Account
      parameters:
        # Headers -- General #
        - $ref: 'common/common.yaml#/components/parameters/InteractionId'
        - $ref: 'common/common.yaml#/components/parameters/InteractionTimestamp'
        # Headers #
        - $ref: 'common/common.yaml#/components/parameters/ProfileRefIdHeader'
        # Path Parameters #
        - $ref: 'common/common.yaml#/components/parameters/AccountRefIdPath'
        - in: query
          name: from_date
          description: Start date/time in UTC of the inquiry. Search for transactions created on or after this date (default is 30 days back)
          type: string
          format: date-time
          example: '2021-10-23T15:59:05.586Z'
        - in: query
          name: to_date
          description: End date/time in UTC of the inquiry. Search for transactions created before this date (default is today)
          type: string
          format: date-time
          example: '2021-10-23T15:59:05.586Z'
        - in: query
          name: offset
          description: offset is starting point of retrieve transaction request filter; if offset
            is not provided it would be defaulted to zero;
          required: false
          schema:
            type: integer
        - in: query
          name: max_items
          description: Maximum number of response items to be returned.
          required: false
          schema:
            type: integer
            default: 25
            maximum: 250
      operationId: retrieve14DaysAccountBalances
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RetrieveLedgerAccountBalanceHistoryResponse'
        '400':
          description: Bad Request
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '404':
          description: account not found
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '500':
          description: Unexpected internal system error
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '503':
          description: System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'

  #########################################################################################################
  /v1/ledger/account/{account_ref_id}/balance:
    get:
      tags:
        - Ledger Account
      parameters:
        # Headers -- General #
        - $ref: 'common/common.yaml#/components/parameters/InteractionId'
        - $ref: 'common/common.yaml#/components/parameters/InteractionTimestamp'
        # Headers #
        - $ref: 'common/common.yaml#/components/parameters/ProfileRefIdHeader'
        # Path Parameters #
        - $ref: 'common/common.yaml#/components/parameters/AccountRefIdPath'
      operationId: retrieveAccountBalance
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RetrieveLedgerAccountBalanceResponse'
        '400':
          description: Bad Request
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '404':
          description: account not found
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '500':
          description: Unexpected internal system error
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '503':
          description: System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
components:
  schemas:
    CreateLedgerAccountRequest:
      type: object
      required:
        - name
        - description
        - monetary_unit
      properties:
        name:
          $ref: 'common/common.yaml#/components/schemas/Name'
        description:
          $ref: 'common/common.yaml#/components/schemas/Description'
        monetary_unit:
          $ref: 'common/common.yaml#/components/schemas/MonetaryUnit'
        options:
          $ref: 'common/common.yaml#/components/schemas/Options'

    CreateLedgerAccountResponse:
      type: object
      properties:
        ref_id:
          $ref: 'common/common.yaml#/components/schemas/AccountRefId'
        name:
          $ref: 'common/common.yaml#/components/schemas/Name'
        description:
          $ref: 'common/common.yaml#/components/schemas/Description'
        monetary_unit:
          $ref: 'common/common.yaml#/components/schemas/MonetaryUnit'
        options:
          $ref: 'common/common.yaml#/components/schemas/Options'
        status:
          $ref: 'common/common.yaml#/components/schemas/AccountStatus'
        created_date_time:
          $ref: 'common/common.yaml#/components/schemas/CreatedDateTime'

    RetrieveLedgerAccountsResponse:
      type: object
      properties:
        accounts:
          type: array
          items:
            type: object
            properties:
              ref_Id:
                $ref: 'common/common.yaml#/components/schemas/AccountRefId'
              name:
                $ref: 'common/common.yaml#/components/schemas/Name'
              status:
                $ref: 'common/common.yaml#/components/schemas/AccountStatus'

          example:
            - ref_Id: "d6qzvW7"
              name: "CryptoFin Peer2Peer Settlement Acct"
              status: 'ACTIVE'

            - ref_Id: "snCz89k"
              name: "CryptoFin Bulk Settlement Acct"
              status: 'ACTIVE'

    RetrieveLedgerAccountResponse:
      type: object
      properties:
        ref_Id:
          $ref: 'common/common.yaml#/components/schemas/AccountRefId'
        name:
          $ref: 'common/common.yaml#/components/schemas/Name'
        description:
          $ref: 'common/common.yaml#/components/schemas/Description'
        monetary_unit:
          $ref: 'common/common.yaml#/components/schemas/MonetaryUnit'
        options:
          $ref: 'common/common.yaml#/components/schemas/Options'
        status:
          $ref: 'common/common.yaml#/components/schemas/AccountStatus'
        created_date_time:
          $ref: 'common/common.yaml#/components/schemas/CreatedDateTime'
        updated_date_time:
          $ref: 'common/common.yaml#/components/schemas/UpdatedDateTime'

    UpdateLedgerAccountRequest:
      type: object
      required:
        - name
        - description
        - monetary_unit
      properties:
        name:
          $ref: 'common/common.yaml#/components/schemas/Name'
        description:
          $ref: 'common/common.yaml#/components/schemas/Description'
        monetary_unit:
          $ref: 'common/common.yaml#/components/schemas/MonetaryUnit'
        options:
          $ref: 'common/common.yaml#/components/schemas/Options'

    UpdateLedgerAccountResponse:
      type: object
      properties:
        ref_id:
          $ref: 'common/common.yaml#/components/schemas/AccountRefId'
        name:
          $ref: 'common/common.yaml#/components/schemas/Name'
        description:
          $ref: 'common/common.yaml#/components/schemas/Description'
        monetary_unit:
          $ref: 'common/common.yaml#/components/schemas/MonetaryUnit'
        options:
          $ref: 'common/common.yaml#/components/schemas/Options'
        status:
          $ref: 'common/common.yaml#/components/schemas/AccountStatus'
        created_date_time:
          $ref: 'common/common.yaml#/components/schemas/CreatedDateTime'
        updated_date_time:
          $ref: 'common/common.yaml#/components/schemas/UpdatedDateTime'

    UpdateLedgerAccountStatusRequest:
      type: object
      required:
        - status
        - reason
      properties:
        status:
          $ref: 'common/common.yaml#/components/schemas/AccountStatus'
        reason:
          $ref: 'common/common.yaml#/components/schemas/Reason'

    RetrieveLedgerAccountBalanceHistoryResponse:
      type: object
      properties:
        total_credit_amount:
          $ref: 'common/common.yaml#/components/schemas/Amount'
        total_debit_amount:
          $ref: 'common/common.yaml#/components/schemas/Amount'
        total_reserve_amount:
          $ref: 'common/common.yaml#/components/schemas/Amount'
        total_amount:
          $ref: 'common/common.yaml#/components/schemas/Amount'
        total_pending_amount:
          $ref: 'common/common.yaml#/components/schemas/Amount'
        effective_on:
          $ref: 'common/common.yaml#/components/schemas/EffectiveDateTime'

    RetrieveLedgerAccountBalanceResponse:
      type: object
      properties:
        available_balance:
          $ref: 'common/common.yaml#/components/schemas/AvailableBalance'
        account_balance:
          $ref: 'common/common.yaml#/components/schemas/AccountBalance'
        fund_hold_amount:
          $ref: 'common/common.yaml#/components/schemas/FundHoldAmount'
        prefund_reserve_amount:
          $ref: 'common/common.yaml#/components/schemas/PrefundReserveAmount'
        overdraft_amount:
          $ref: 'common/common.yaml#/components/schemas/OverdraftAmount'
        effective_on:
          $ref: 'common/common.yaml#/components/schemas/EffectiveDateTime'
