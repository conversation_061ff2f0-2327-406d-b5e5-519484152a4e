openapi: 3.0.2
paths:
  /v1/ledger/transaction/async:
    #########################################################################################################
    post:
      tags:
        - Ledger Transaction Async
      operationId: initiateInstruction
      parameters:
        # Headers -- General #
        - $ref: 'common/common.yaml#/components/parameters/InteractionId'
        - $ref: 'common/common.yaml#/components/parameters/InteractionTimestamp'
        # Headers #
        - $ref: 'common/common.yaml#/components/parameters/ProfileRefIdHeader'
        - $ref: 'common/common.yaml#/components/parameters/AccountRefIdHeader'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/InitiateLedgerTransactionAsyncRequest'
        required: true
      responses:
        '204':
          description: Resource successfully created
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
        '400':
          description: Bad Request -- Validation failure
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '404':
          description: Resource Not Found -- Any of service account, austomer, account, etc.
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized -- Authorization required to access resource
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error -- Unexpected error
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '503':
          description: Service Unavailable -- System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'

  #########################################################################################################
  /v1/ledger/transaction/async/{instruction_ref_id}:
    patch:
      tags:
        - Ledger Transaction Async
      operationId: commitInstruction
      parameters:
        # Headers -- General #
        - $ref: 'common/common.yaml#/components/parameters/InteractionId'
        - $ref: 'common/common.yaml#/components/parameters/InteractionTimestamp'
        # Headers #
        - $ref: 'common/common.yaml#/components/parameters/ProfileRefIdHeader'
        - $ref: 'common/common.yaml#/components/parameters/AccountRefIdHeader'
        # Path Parameters #
        - $ref: 'common/common.yaml#/components/parameters/InstructionRefIdPath'
      responses:
        '204':
          description: Resource successfully updated
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
        '400':
          description: Bad Request -- Validation failure
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '404':
          description: Resource Not Found -- Any of service account, austomer, account, etc.
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized -- Authorization required to access resource
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error -- Unexpected error
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '503':
          description: Service Unavailable -- System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'

    #########################################################################################################
    delete:
      tags:
        - Ledger Transaction Async
      operationId: rollbackInstruction
      parameters:
        # Headers -- General #
        - $ref: 'common/common.yaml#/components/parameters/InteractionId'
        - $ref: 'common/common.yaml#/components/parameters/InteractionTimestamp'
        # Headers #
        - $ref: 'common/common.yaml#/components/parameters/ProfileRefIdHeader'
        - $ref: 'common/common.yaml#/components/parameters/AccountRefIdHeader'
        # Path Parameters #
        - $ref: 'common/common.yaml#/components/parameters/InstructionRefIdPath'
      responses:
        '204':
          description: Resource successfully rolledback
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
        '400':
          description: Bad Request -- Validation failure
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '404':
          description: Resource Not Found -- Any of service account, austomer, account, etc.
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized -- Authorization required to access resource
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error -- Unexpected error
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '503':
          description: Service Unavailable -- System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'

  #########################################################################################################
  /v1/ledger/transaction/async/{instruction_ref_id}/{transaction_ref_id}:
    delete:
      tags:
        - Ledger Transaction Async
      operationId: reverseTransaction
      parameters:
        # Headers -- General #
        - $ref: 'common/common.yaml#/components/parameters/InteractionId'
        - $ref: 'common/common.yaml#/components/parameters/InteractionTimestamp'
        # Headers #
        - $ref: 'common/common.yaml#/components/parameters/ProfileRefIdHeader'
        - $ref: 'common/common.yaml#/components/parameters/AccountRefIdHeader'
        # Path Parameters #
        - $ref: 'common/common.yaml#/components/parameters/InstructionRefIdPath'
        - $ref: 'common/common.yaml#/components/parameters/TransactionRefIdPath'
      responses:
        '204':
          description: Resource successfully reversed
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
        '400':
          description: Bad Request -- Validation failure
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '404':
          description: Resource Not Found -- Any of service account, austomer, account, etc.
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized -- Authorization required to access resource
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error -- Unexpected error
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '503':
          description: Service Unavailable -- System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'

components:
  schemas:
    #
    # Initiate or begin
    #
    InitiateLedgerTransactionAsyncRequest:
      type: object
      required:
        - instruction_ref_id
        - payment_rail
        - transactions
      properties:
        instruction_ref_id:
          $ref: 'common/common.yaml#/components/schemas/InstructionRefId'
        payment_rail:
          $ref: 'common/common.yaml#/components/schemas/PaymentRail'
        transactions:
          type: array
          minItems: 1
          maxItems: 25
          items:
            type: object
            required:
              - transaction_ref_id
              - amount
              - monetary_unit
              - acceptance_date_time
            properties:
              transaction_ref_id:
                $ref: 'common/common.yaml#/components/schemas/TransactionRefId'
              payment_category:
                $ref: 'common/common.yaml#/components/schemas/PaymentCategory'
              amount:
                $ref: 'common/common.yaml#/components/schemas/Amount'
              monetary_unit:
                $ref: 'common/common.yaml#/components/schemas/MonetaryUnit'
              acceptance_date_time:
                $ref: 'common/common.yaml#/components/schemas/AcceptanceDateTime'
              due_date_time:
                $ref: 'common/common.yaml#/components/schemas/DueDateTime'
      example:
        instruction_ref_id: '32e655ed-e162-4168-a924-dcbac87fb127'
        payment_rail: 'EFT'
        transactions:
          - transaction_ref_id: "f9443bba-0443-4b71-a17d-42a8057534c8"
            payment_category: "DEBIT_PULL"
            amount: 2500.50
            monetary_unit: "CAD"
            acceptance_date_time: "2021-10-23T15:59:05.586Z"
            due_date_time: "2021-10-25T15:59:05.586Z"
          - transaction_ref_id: "5a530373-2f3a-455f-a66d-751a01e3b0f7"
            payment_category: "CREDIT_PUSH"
            amount: 1500.50
            monetary_unit: "CAD"
            acceptance_date_time: "2021-10-23T15:59:05.586Z"
