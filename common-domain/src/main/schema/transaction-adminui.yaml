openapi: 3.0.2
paths:

  #########################################################################################################
  /v1/ledger/transaction/{instruction_ref_id}:
    get:
      tags:
        - Ledger Transaction
      operationId: retrieveInstruction
      parameters:
        # Headers -- General #
        - $ref: 'common/common.yaml#/components/parameters/InteractionId'
        - $ref: 'common/common.yaml#/components/parameters/InteractionTimestamp'
        # Headers #
        - $ref: 'common/common.yaml#/components/parameters/ProfileRefIdHeader'
        - $ref: 'common/common.yaml#/components/parameters/AccountRefIdHeader'
        # Path Parameters #
        - $ref: 'common/common.yaml#/components/parameters/InstructionRefIdPath'
      responses:
        '200':
          description: Resource successfully retrieved
          content:
            application/json:
              schema:
                $ref: 'transaction-system.yaml#/components/schemas/RetrieveLedgerTransactionsResponse'
        '400':
          description: Bad Request -- Validation failure
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '404':
          description: Resource Not Found -- Any of service account, austomer, account, etc.
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized -- Authorization required to access resource
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error -- Unexpected error
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '503':
          description: Service Unavailable -- System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'

  #########################################################################################################
  /v1/ledger/transaction/{instruction_ref_id}/{transaction_ref_id}:
    get:
      tags:
        - Ledger Transaction
      operationId: retrieveTransaction
      parameters:
        # Headers -- General #
        - $ref: 'common/common.yaml#/components/parameters/InteractionId'
        - $ref: 'common/common.yaml#/components/parameters/InteractionTimestamp'
        # Headers #
        - $ref: 'common/common.yaml#/components/parameters/ProfileRefIdHeader'
        - $ref: 'common/common.yaml#/components/parameters/AccountRefIdHeader'
        # Path Parameters #
        - $ref: 'common/common.yaml#/components/parameters/InstructionRefIdPath'
        - $ref: 'common/common.yaml#/components/parameters/TransactionRefIdPath'
      responses:
        '200':
          description: Resource successfully retrieved
          content:
            application/json:
              schema:
                $ref: 'transaction-system.yaml#/components/schemas/RetrieveLedgerTransactionResponse'
        '400':
          description: Bad Request -- Validation failure
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '404':
          description: Resource Not Found -- Any of service account, austomer, account, etc.
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized -- Authorization required to access resource
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error -- Unexpected error
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '503':
          description: Service Unavailable -- System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'

  /v1/ledger/transaction/{instruction_ref_id}/{transaction_ref_id}/metadata:
    put:
      tags:
        - Ledger Transaction
      operationId: updateTransactionMetadata
      parameters:
        # Headers -- General #
        - $ref: 'common/common.yaml#/components/parameters/InteractionId'
        - $ref: 'common/common.yaml#/components/parameters/InteractionTimestamp'
        # Headers #
        - $ref: 'common/common.yaml#/components/parameters/ProfileRefIdHeader'
        - $ref: 'common/common.yaml#/components/parameters/AccountRefIdHeader'
        # Path Parameters #
        - $ref: 'common/common.yaml#/components/parameters/InstructionRefIdPath'
        - $ref: 'common/common.yaml#/components/parameters/TransactionRefIdPath'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Metadata'
        required: true
      responses:
        '201':
          description: Resource successfully created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Metadata'
        '204':
          description: Resource successfully updated
        '400':
          description: Bad Request -- Validation failure
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '404':
          description: Resource Not Found -- Any of instruction or transaction.
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized -- Authorization required to access resource
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error -- Unexpected error
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '503':
          description: Service Unavailable -- System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
    get:
      tags:
        - Ledger Transaction
      operationId: getTransactionMetadata
      parameters:
        # Headers -- General #
        - $ref: 'common/common.yaml#/components/parameters/InteractionId'
        - $ref: 'common/common.yaml#/components/parameters/InteractionTimestamp'
        # Headers #
        - $ref: 'common/common.yaml#/components/parameters/ProfileRefIdHeader'
        - $ref: 'common/common.yaml#/components/parameters/AccountRefIdHeader'
        # Path Parameters #
        - $ref: 'common/common.yaml#/components/parameters/InstructionRefIdPath'
        - $ref: 'common/common.yaml#/components/parameters/TransactionRefIdPath'
      responses:
        '200':
          description: Resource successfully updated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RetrieveMetadata'
        '400':
          description: Bad Request -- Validation failure
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '404':
          description: Resource Not Found -- Any of instruction or transaction.
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized -- Authorization required to access resource
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error -- Unexpected error
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '503':
          description: Service Unavailable -- System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'

  #########################################################################################################
  /v1/ledger/transaction/internal:
    post:
      tags:
        - Ledger Transaction
      operationId: internalTransaction
      parameters:
        # Headers -- General #
        - $ref: 'common/common.yaml#/components/parameters/InteractionId'
        - $ref: 'common/common.yaml#/components/parameters/InteractionTimestamp'
        # Headers #
        - $ref: 'common/common.yaml#/components/parameters/ProfileRefIdHeader'
        - $ref: 'common/common.yaml#/components/parameters/AccountRefIdHeader'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/InitiateLedgerPrefundReserveRequest'
        required: true
      responses:
        '201':
          description: Resource successfully created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InitiateLedgerPrefundReserveResponse'
        '400':
          description: Bad Request -- Validation failure
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '404':
          description: Resource Not Found -- Any of service account, austomer, account, etc.
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized -- Authorization required to access resource
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error -- Unexpected error
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '503':
          description: Service Unavailable -- System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'

  #########################################################################################################
  /v1/ledger/transaction:
    get:
      tags:
        - Ledger Transaction
      operationId: retrieveTransactionList
      parameters:
        # Headers -- General #
        - $ref: 'common/common.yaml#/components/parameters/InteractionId'
        - $ref: 'common/common.yaml#/components/parameters/InteractionTimestamp'
        # Headers #
        - $ref: 'common/common.yaml#/components/parameters/ProfileRefIdHeader'
        - $ref: 'common/common.yaml#/components/parameters/AccountRefIdHeader'
      responses:
        '200':
          description: Resource successfully retrieved
          content:
            application/json:
              schema:
                $ref: 'transaction-system.yaml#/components/schemas/RetrieveLedgerTransactionsResponse'
        '400':
          description: Bad Request -- Validation failure
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '404':
          description: Resource Not Found -- Any of service account, austomer, account, etc.
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized -- Authorization required to access resource
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error -- Unexpected error
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '503':
          description: Service Unavailable -- System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
  #########################################################################################################
  /v1/ledger/transaction/internal/{transaction_ref_id}:
    get:
      tags:
        - Ledger Transaction
      parameters:
        # Headers -- General #
        - $ref: 'common/common.yaml#/components/parameters/InteractionId'
        - $ref: 'common/common.yaml#/components/parameters/InteractionTimestamp'
        # Path Parameters #
        - $ref: 'common/common.yaml#/components/parameters/TransactionRefIdPath'
      operationId: getTransaction
      responses:
        '200':
          description: Resource successfully retrieved
          content:
            application/json:
              schema:
                $ref: 'transaction-system.yaml#/components/schemas/RetrieveLedgerTransactionResponse'
        '400':
          description: Bad Request -- Validation failure
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '404':
          description: Resource Not Found -- Any of service account, customer, account, etc.
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized -- Authorization required to access resource
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error -- Unexpected error
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '503':
          description: Service Unavailable -- System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
  /v1/ledger/transaction/search:
    post:
      tags:
        - Ledger Transaction
      operationId: getAllTransactions
      parameters:
        #Headers -- General #
        - $ref: 'common/common.yaml#/components/parameters/InteractionId'
        - $ref: 'common/common.yaml#/components/parameters/InteractionTimestamp'
        #query string
        - in: query
          name: profile_id
          schema:
            $ref: 'common/common.yaml#/components/parameters/ProfileRefIdQuery'
        - in: query
          name: instruction_id
          schema:
            $ref: 'common/common.yaml#/components/parameters/InstructionRefIdQuery'
        - in: query
          name: payment_rail
          schema:
            $ref: 'common/common.yaml#/components/schemas/PaymentRail'
        - in: query
          name: from_date
          description: Start date/time in UTC of the inquiry. Search for transactions created on or after this date (default is 30 days back)
          type: string
          format: date-time
          example: '2021-10-23T15:59:05.586Z'
        - in: query
          name: to_date
          description: End date/time in UTC of the inquiry. Search for transactions created before this date (default is today)
          type: string
          format: date-time
          example: '2021-10-23T15:59:05.586Z'
        - in: query
          name: offset
          description: offset is starting point of retrieve transaction request filter; if offset
            is not provided it would be defaulted to zero;
          schema:
            type: integer
            minimum: 0
        - in: query
          name: max_items
          description: Maximum number of response items to be returned.
          schema:
            type: integer
            minimum: 1
            maximum: 25
      responses:
        '200':
          description: Resource successfully created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RetrieveTransactionsResponse'
        '400':
          description: Bad Request -- Validation failure
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '404':
          description: Resource Not Found -- Any of service account, customer, account, etc.
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized -- Authorization required to access resource
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error -- Unexpected error
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '503':
          description: Service Unavailable -- System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
  /v1/ledger/transaction/search/instruction:
    post:
      tags:
        - Ledger Transaction
      operationId: getAllInstructions
      parameters:
        #Headers -- General #
        - $ref: 'common/common.yaml#/components/parameters/InteractionId'
        - $ref: 'common/common.yaml#/components/parameters/InteractionTimestamp'
        #query string
        - in: query
          name: profile_id
          schema:
            $ref: 'common/common.yaml#/components/parameters/ProfileRefIdQuery'
          required: true
        - in: query
          name: account_id
          schema:
            $ref: 'common/common.yaml#/components/parameters/AccountRefIdQuery'
          required: true
        - in: query
          name: instruction_id
          schema:
            $ref: 'common/common.yaml#/components/parameters/InstructionRefIdQuery'
        - in: query
          name: payment_rail
          schema:
            $ref: 'common/common.yaml#/components/schemas/PaymentRail'
        - in: query
          name: from_date
          description: Start date/time in UTC of the inquiry. Search for transactions created on or after this date (default is 30 days back)
          type: string
          format: date-time
          example: '2021-10-23T15:59:05.586Z'
        - in: query
          name: to_date
          description: End date/time in UTC of the inquiry. Search for transactions created before this date (default is today)
          type: string
          format: date-time
          example: '2021-10-23T15:59:05.586Z'
        - in: query
          name: offset
          description: offset is starting point of retrieve transaction request filter; if offset
            is not provided it would be defaulted to zero;
          schema:
            type: integer
            minimum: 0
        - in: query
          name: max_items
          description: Maximum number of response items to be returned.
          schema:
            type: integer
            minimum: 1
            maximum: 25
      responses:
        '200':
          description: Resource successfully created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RetrieveInstructionsResponse'
        '400':
          description: Bad Request -- Validation failure
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '404':
          description: Resource Not Found -- Any of service account, customer, account, etc.
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized -- Authorization required to access resource
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error -- Unexpected error
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '503':
          description: Service Unavailable -- System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
            x-pg-correspondent-id:
              $ref: 'common/common.yaml#/components/headers/CorrespondentId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
components:
  schemas:
    InitiateLedgerPrefundReserveRequest:
      type: object
      required:
        - instruction_ref_id
        - payment_rail
        - transactions
      properties:
        instruction_ref_id:
          $ref: 'common/common.yaml#/components/schemas/InstructionRefId'
        payment_rail:
          $ref: 'common/common.yaml#/components/schemas/InternalPaymentRail'
        transactions:
          type: array
          minItems: 1
          maxItems: 25
          items:
            type: object
            required:
              - transaction_ref_id
              - payment_category
              - transaction_flow
              - amount
              - monetary_unit
            properties:
              transaction_ref_id:
                $ref: 'common/common.yaml#/components/schemas/TransactionRefId'
              payment_category:
                $ref: 'common/common.yaml#/components/schemas/InternalPaymentCategory'
              transaction_flow:
                $ref: 'common/common.yaml#/components/schemas/TransactionFlow'
              amount:
                $ref: 'common/common.yaml#/components/schemas/PrefundReserveAmount'
              monetary_unit:
                $ref: 'common/common.yaml#/components/schemas/MonetaryUnit'
      example:
        instruction_ref_id: '32e655ed-e162-4168-a924-dcbac87fb127'
        payment_rail: 'INTERNAL'
        transactions:
          - transaction_ref_id: "f9443bba-0443-4b71-a17d-42a8057534c8"
            payment_category: "CORRECTION"
            amount: 2500.50
            monetary_unit: "CAD"
            acceptance_date_time: "2021-10-23T15:59:05.586Z"
          - transaction_ref_id: "5a530373-2f3a-455f-a66d-751a01e3b0f7"
            payment_category: "PREFUND_RESERVE"
            amount: 1500.50
            monetary_unit: "CAD"
            acceptance_date_time: "2021-10-23T15:59:05.586Z"

    InitiateLedgerPrefundReserveResponse:
      type: object
      required:
        - instruction_ref_id
        - payment_rail
        - totals
        - status
        - transactions
        - created_date_time
      properties:
        instruction_ref_id:
          $ref: 'common/common.yaml#/components/schemas/InstructionRefId'
        payment_rail:
          $ref: 'common/common.yaml#/components/schemas/InternalPaymentRail'
        totals:
          $ref: 'common/common.yaml#/components/schemas/Totals'
        status:
          $ref: 'common/common.yaml#/components/schemas/InstructionStatus'
        transactions:
          type: array
          minItems: 1
          maxItems: 25
          items:
            type: object
            required:
              - transaction_ref_id
              - payment_category
              - transaction_flow
              - amount
              - monetary_unit
              - acceptance_date_time
              - effective_date_time
              - events
              - status
            properties:
              transaction_ref_id:
                $ref: 'common/common.yaml#/components/schemas/TransactionRefId'
              payment_category:
                $ref: 'common/common.yaml#/components/schemas/InternalPaymentCategory'
              transaction_flow:
                $ref: 'common/common.yaml#/components/schemas/TransactionFlow'
              amount:
                $ref: 'common/common.yaml#/components/schemas/PrefundReserveAmount'
              monetary_unit:
                $ref: 'common/common.yaml#/components/schemas/MonetaryUnit'
              acceptance_date_time:
                $ref: 'common/common.yaml#/components/schemas/AcceptanceDateTime'
              effective_date_time:
                $ref: 'common/common.yaml#/components/schemas/EffectiveDateTime'
              events:
                type: array
                items:
                  type: object
                  required:
                    - event
                    - created_date_time
                  properties:
                    event:
                      $ref: 'common/common.yaml#/components/schemas/Event'
                    created_date_time:
                      $ref: 'common/common.yaml#/components/schemas/CreatedDateTime'
              status:
                $ref: 'common/common.yaml#/components/schemas/TransactionStatus'
        created_date_time:
          $ref: 'common/common.yaml#/components/schemas/CreatedDateTime'

    LedgerRetrieveSearchTransaction:
      type: object
      required:
        - transaction_ref_id
        - instruction_ref_id
        - account_ref_id
        - amount
        - acceptance_date_time
      properties:
        transaction_ref_id:
          $ref: 'common/common.yaml#/components/schemas/TransactionRefId'
        instruction_ref_id:
          $ref: 'common/common.yaml#/components/schemas/InstructionRefId'
        account_ref_id:
          $ref: 'common/common.yaml#/components/schemas/AccountRefId'
        amount:
          $ref: 'common/common.yaml#/components/schemas/Amount'
        acceptance_date_time:
          $ref: 'common/common.yaml#/components/schemas/AcceptanceDateTime'

    RetrieveTransactionsResponse:
      type: object
      required:
        - transactions
      properties:
        transactions:
          type: array
          minItems: 1
          maxItems: 25
          items:
            $ref: '#/components/schemas/LedgerRetrieveSearchTransaction'
        next_offset:
          description: offset for next page, present only if next page available
          type: integer
          minimum: 1
          example: 30
        more_records:
          type: boolean

    RetrieveInstructionsResponse:
      type: object
      required:
        - transactions
      properties:
        instructions:
          type: array
          minItems: 0
          maxItems: 250
          items:
            $ref: '#/components/schemas/LedgerRetrieveSearchInstruction'
        more_records:
          type: boolean

    LedgerRetrieveSearchInstruction:
      type: object
      required:
        - instruction_ref_id
        - payment_rail
        - created_date_time
        - status
      properties:
        instruction_ref_id:
          $ref: 'common/common.yaml#/components/schemas/InstructionRefId'
        payment_rail:
          $ref: 'common/common.yaml#/components/schemas/PaymentRail'
        status:
          $ref: 'common/common.yaml#/components/schemas/InstructionStatus'
        created_date_time:
          $ref: 'common/common.yaml#/components/schemas/CreatedDateTime'
        updated_date_time:
          $ref: 'common/common.yaml#/components/schemas/UpdatedDateTime'

    Metadata:
      type: object
      properties:
        ticket_id:
          type: string
          maxLength: 25
        ticket_type:
          type: string
          enum:
            - JIRA
            - SERVICE_NOW
        notes:
          type: string
          minLength: 5
          maxLength: 256
      anyOf:
        - required: [ "ticket_id", "ticket_type" ]
        - required: [ "notes" ]

    RetrieveMetadata:
      allOf:
        - $ref: '#/components/schemas/Metadata'
        - type: object
          properties:
            created_date_time:
              $ref: 'common/common.yaml#/components/schemas/CreatedDateTime'
            updated_date_time:
              $ref: 'common/common.yaml#/components/schemas/UpdatedDateTime'