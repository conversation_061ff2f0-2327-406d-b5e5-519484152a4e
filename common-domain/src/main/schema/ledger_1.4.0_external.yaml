openapi: 3.0.3
info:
  title: Transaction Orchestration Layer
  description: |
    Transaction Orchestration Layer is the record-keeping system for a partners balances across the various Peoples Group products, with debit and credit account records validated by a trial balance.
  version: 1.4.0
servers:
  - url: 'https://ledger-stg-api.peoplescloud.io'
    description: Transaction Orchestration Layer Staging Environment
  - url: 'https://ledger-api.peoplesgroup.com'
    description: Transaction Orchestration Layer Production Environment
security:
  - OAuth2:
      - all
paths:
  '/v1/ledger/account/external/{account_ref_id}/balance':
    get:
      tags:
        - Ledger Account
      parameters:
        - description: A unique id  (uuid v4) generated for each request
          in: header
          name: x-pg-interaction-id
          schema:
            description: Uniquely identifies the message.
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[a-zA-Z0-9-.]$'
            example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          required: true
          example: 0a0d74f6-49b5-4b0b-b723-48bdfb142117
        - description: A particular point in progression of time defined (GMT)
          in: header
          name: x-pg-interaction-timestamp
          required: true
          schema:
            type: string
            format: date-time
            example: '2020-05-29T17:19:26.951000Z'
        - description: A unique id that identifies the partner
          in: header
          name: x-pg-profile-id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
        - description: A unique id
          in: path
          name: account_ref_id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
      operationId: retreiveAccountBalanceByClient
      responses:
        '200':
          description: Balance succesfully retrieved
          content:
            application/json:
              schema:
                type: object
                properties:
                  available_balance:
                    type: number
                    multipleOf: 0.01
                    minimum: 0
                    example: **********.3
                  account_balance:
                    type: number
                    multipleOf: 0.01
                    minimum: 0
                    example: **********.08
                  fund_hold_amount:
                    type: number
                    multipleOf: 0.01
                    minimum: 0
                    example: 113692.76
                  prefund_reserve_amount:
                    type: number
                    multipleOf: 0.01
                    minimum: 0
                    example: **********.9
                  overdraft_amount:
                    type: number
                    multipleOf: 0.01
                    minimum: 0
                    maximum: *********.99
                    example: 100000
                  effective_on:
                    type: string
                    format: date-time
                    example: '2021-10-23T15:59:05.586Z'
        '400':
          description: Bad Request
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '404':
          description: Profile or account not found
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '500':
          description: Unexpected internal system error
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '503':
          description: System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
components:
  schemas:
    RetrieveLedgerAccountBalanceByClientResponse:
      type: object
      properties:
        available_balance:
          type: number
          multipleOf: 0.01
          minimum: 0
          example: **********.3
        account_balance:
          type: number
          multipleOf: 0.01
          minimum: 0
          example: **********.08
        fund_hold_amount:
          type: number
          multipleOf: 0.01
          minimum: 0
          example: 113692.76
        prefund_reserve_amount:
          type: number
          multipleOf: 0.01
          minimum: 0
          example: **********.9
        overdraft_amount:
          type: number
          multipleOf: 0.01
          minimum: 0
          maximum: *********.99
          example: 100000
        effective_on:
          type: string
          format: date-time
          example: '2021-10-23T15:59:05.586Z'
  securitySchemes:
    OAuth2:
      type: oauth2
      description: This API uses OAuth 2.0 with client credentials flow
      flows:
        clientCredentials:
          tokenUrl: /oauth2/token
          scopes:
            all: access to all operations
