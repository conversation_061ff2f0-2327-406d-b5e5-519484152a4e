openapi: 3.0.3
info:
  title: Transaction Orchestration Layer -- Admin UI
  description: |
    Transaction Orchestration Layer is the record-keeping system for a partners balances across the various Peoples Group products, with debit and credit account records validated by a trial balance.
  version: 1.4.0
servers:
  - url: 'https://ledger-stg-api.peoplescloud.io/admin-ui'
    description: Transaction Orchestration Layer Staging Environment
  - url: ledger-admin.peoplesgroup.com
    description: Transaction Orchestration Layer Production Environment
paths:
  /v1/ledger/profile:
    parameters:
      - description: A unique id  (uuid v4) generated for each request
        in: header
        name: x-pg-interaction-id
        schema:
          description: Uniquely identifies the message.
          type: string
          minLength: 3
          maxLength: 36
          pattern: '^[a-zA-Z0-9-.]$'
          example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
        required: true
        example: 0a0d74f6-49b5-4b0b-b723-48bdfb142117
      - description: A particular point in progression of time defined (GMT)
        in: header
        name: x-pg-interaction-timestamp
        required: true
        schema:
          type: string
          format: date-time
          example: '2020-05-29T17:19:26.951000Z'
    post:
      tags:
        - Ledger Profile
      operationId: createProfile
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
                - crm_id
                - display_name
                - legal_name
              properties:
                crm_id:
                  type: string
                  example: j41klvu
                  minLength: 3
                  maxLength: 50
                  pattern: '^[0-9a-zA-Z]+$'
                display_name:
                  type: string
                  pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
                  minLength: 3
                  maxLength: 50
                  example: Crypto Fintech
                legal_name:
                  type: string
                  example: Crypto Fintech Inc.
                  minLength: 3
                  maxLength: 50
                  pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
                cognito_client_id:
                  type: string
                  maxLength: 50
        required: true
      responses:
        '201':
          description: Profile successfully created
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                properties:
                  ref_id:
                    type: string
                    format: uuid
                    example: 2a42f783-ca99-4e3d-a87e-d3325013136c
                  crm_id:
                    type: string
                    example: j41klvu
                    minLength: 3
                    maxLength: 50
                    pattern: '^[0-9a-zA-Z]+$'
                  cognito_client_id:
                    type: string
                    maxLength: 50
                  display_name:
                    type: string
                    pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
                    minLength: 3
                    maxLength: 50
                    example: Crypto Fintech
                  legal_name:
                    type: string
                    example: Crypto Fintech Inc.
                    minLength: 3
                    maxLength: 50
                    pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
                  status:
                    type: string
                    enum:
                      - ENABLED
                      - DISABLED
                    example: ENABLED
                  created_date_time:
                    type: string
                    format: date-time
                    example: '2021-10-22T15:59:05.586Z '
        '400':
          description: Bad Request
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '404':
          description: Resource not found
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '500':
          description: internal system error
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '503':
          description: System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
    get:
      tags:
        - Ledger Profile
      parameters:
        - description: A unique id  (uuid v4) generated for each request
          in: header
          name: x-pg-interaction-id
          schema:
            description: Uniquely identifies the message.
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[a-zA-Z0-9-.]$'
            example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          required: true
          example: 0a0d74f6-49b5-4b0b-b723-48bdfb142117
        - description: A particular point in progression of time defined (GMT)
          in: header
          name: x-pg-interaction-timestamp
          required: true
          schema:
            type: string
            format: date-time
            example: '2020-05-29T17:19:26.951000Z'
      operationId: retrieveProfiles
      responses:
        '200':
          description: Profiles retrieved successfully
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                properties:
                  profiles:
                    type: array
                    items:
                      type: object
                      properties:
                        ref_id:
                          type: string
                          format: uuid
                          example: 2a42f783-ca99-4e3d-a87e-d3325013136c
                        crm_id:
                          type: string
                          example: j41klvu
                          minLength: 3
                          maxLength: 50
                          pattern: '^[0-9a-zA-Z]+$'
                        display_name:
                          type: string
                          pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
                          minLength: 3
                          maxLength: 50
                          example: Crypto Fintech
                        legal_name:
                          type: string
                          example: Crypto Fintech Inc.
                          minLength: 3
                          maxLength: 50
                          pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
                        status:
                          type: string
                          enum:
                            - ENABLED
                            - DISABLED
                          example: ENABLED
                        created_date_time:
                          type: string
                          format: date-time
                          example: '2021-10-22T15:59:05.586Z '
                        updated_date_time:
                          type: string
                          format: date-time
                          example: '2021-10-22T15:59:05.586Z'
                        cognito_client_id:
                          type: string
                          maxLength: 50
                        reason:
                          type: string
                          minLength: 3
                          maxLength: 240
                          pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
                          example: 'Business relationship was suspended '
        '400':
          description: Bad Request
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '404':
          description: Resource not found
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '500':
          description: Unexpected internal system error
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '503':
          description: System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
  '/v1/ledger/profile/{ref_id}':
    put:
      operationId: updateProfile
      tags:
        - Ledger Profile
      parameters:
        - description: A unique id  (uuid v4) generated for each request
          in: header
          name: x-pg-interaction-id
          schema:
            description: Uniquely identifies the message.
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[a-zA-Z0-9-.]$'
            example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          required: true
          example: 0a0d74f6-49b5-4b0b-b723-48bdfb142117
        - description: A particular point in progression of time defined (GMT)
          in: header
          name: x-pg-interaction-timestamp
          required: true
          schema:
            type: string
            format: date-time
            example: '2020-05-29T17:19:26.951000Z'
        - description: A unique id
          in: path
          name: ref_id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
                - display_name
                - crm_id
                - legal_name
              properties:
                crm_id:
                  type: string
                  example: j41klvu
                  minLength: 3
                  maxLength: 50
                  pattern: '^[0-9a-zA-Z]+$'
                cognito_client_id:
                  type: string
                  maxLength: 50
                display_name:
                  type: string
                  pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
                  minLength: 3
                  maxLength: 50
                  example: Crypto Fintech
                legal_name:
                  type: string
                  example: Crypto Fintech Inc.
                  minLength: 3
                  maxLength: 50
                  pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
        required: true
      responses:
        '200':
          description: updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  ref_id:
                    type: string
                    format: uuid
                    example: 2a42f783-ca99-4e3d-a87e-d3325013136c
                  crm_id:
                    type: string
                    example: j41klvu
                    minLength: 3
                    maxLength: 50
                    pattern: '^[0-9a-zA-Z]+$'
                  cognito_client_id:
                    type: string
                    maxLength: 50
                  display_name:
                    type: string
                    pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
                    minLength: 3
                    maxLength: 50
                    example: Crypto Fintech
                  legal_name:
                    type: string
                    example: Crypto Fintech Inc.
                    minLength: 3
                    maxLength: 50
                    pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
                  status:
                    type: string
                    enum:
                      - ENABLED
                      - DISABLED
                    example: ENABLED
                  created_date_time:
                    type: string
                    format: date-time
                    example: '2021-10-22T15:59:05.586Z '
                  updated_date_time:
                    type: string
                    format: date-time
                    example: '2021-10-22T15:59:05.586Z'
        '400':
          description: Bad Request
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '404':
          description: Resource not found
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '500':
          description: internal system error
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '503':
          description: System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
    get:
      tags:
        - Ledger Profile
      parameters:
        - description: A unique id  (uuid v4) generated for each request
          in: header
          name: x-pg-interaction-id
          schema:
            description: Uniquely identifies the message.
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[a-zA-Z0-9-.]$'
            example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          required: true
          example: 0a0d74f6-49b5-4b0b-b723-48bdfb142117
        - description: A particular point in progression of time defined (GMT)
          in: header
          name: x-pg-interaction-timestamp
          required: true
          schema:
            type: string
            format: date-time
            example: '2020-05-29T17:19:26.951000Z'
        - description: A unique id
          in: path
          name: ref_id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
      operationId: retrieveProfile
      responses:
        '200':
          description: Profile retrieved successfully
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                properties:
                  ref_id:
                    type: string
                    format: uuid
                    example: 2a42f783-ca99-4e3d-a87e-d3325013136c
                  crm_id:
                    type: string
                    example: j41klvu
                    minLength: 3
                    maxLength: 50
                    pattern: '^[0-9a-zA-Z]+$'
                  display_name:
                    type: string
                    pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
                    minLength: 3
                    maxLength: 50
                    example: Crypto Fintech
                  legal_name:
                    type: string
                    example: Crypto Fintech Inc.
                    minLength: 3
                    maxLength: 50
                    pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
                  status:
                    type: string
                    enum:
                      - ENABLED
                      - DISABLED
                    example: ENABLED
                  created_date_time:
                    type: string
                    format: date-time
                    example: '2021-10-22T15:59:05.586Z '
                  updated_date_time:
                    type: string
                    format: date-time
                    example: '2021-10-22T15:59:05.586Z'
                  cognito_client_id:
                    type: string
                    maxLength: 50
                  reason:
                    type: string
                    minLength: 3
                    maxLength: 240
                    pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
                    example: 'Business relationship was suspended '
        '400':
          description: Bad Request
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '404':
          description: Resource not found
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '500':
          description: Unexpected internal system error
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '503':
          description: System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
  '/v1/ledger/profile/{ref_id}/status':
    patch:
      tags:
        - Ledger Profile
      operationId: updateProfileStatus
      parameters:
        - description: A unique id  (uuid v4) generated for each request
          in: header
          name: x-pg-interaction-id
          schema:
            description: Uniquely identifies the message.
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[a-zA-Z0-9-.]$'
            example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          required: true
          example: 0a0d74f6-49b5-4b0b-b723-48bdfb142117
        - description: A particular point in progression of time defined (GMT)
          in: header
          name: x-pg-interaction-timestamp
          required: true
          schema:
            type: string
            format: date-time
            example: '2020-05-29T17:19:26.951000Z'
        - description: A unique id
          in: path
          name: ref_id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
                - status
                - reason
              properties:
                status:
                  type: string
                  enum:
                    - ENABLED
                    - DISABLED
                  example: ENABLED
                reason:
                  type: string
                  minLength: 3
                  maxLength: 240
                  pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
                  example: 'Business relationship was suspended '
        required: true
      responses:
        '204':
          description: No Content
        '400':
          description: Bad Request
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '404':
          description: Resource not found
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '500':
          description: internal system error
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '503':
          description: System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
  /v1/ledger/profile/search:
    parameters:
      - description: A unique id  (uuid v4) generated for each request
        in: header
        name: x-pg-interaction-id
        schema:
          description: Uniquely identifies the message.
          type: string
          minLength: 3
          maxLength: 36
          pattern: '^[a-zA-Z0-9-.]$'
          example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
        required: true
        example: 0a0d74f6-49b5-4b0b-b723-48bdfb142117
      - description: A particular point in progression of time defined (GMT)
        in: header
        name: x-pg-interaction-timestamp
        required: true
        schema:
          type: string
          format: date-time
          example: '2020-05-29T17:19:26.951000Z'
    get:
      tags:
        - Ledger Profile
      parameters:
        - description: A unique id  (uuid v4) generated for each request
          in: header
          name: x-pg-interaction-id
          schema:
            description: Uniquely identifies the message.
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[a-zA-Z0-9-.]$'
            example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          required: true
          example: 0a0d74f6-49b5-4b0b-b723-48bdfb142117
        - description: A particular point in progression of time defined (GMT)
          in: header
          name: x-pg-interaction-timestamp
          required: true
          schema:
            type: string
            format: date-time
            example: '2020-05-29T17:19:26.951000Z'
        - in: query
          name: profile_name
          description: transaction profile display name.
          type: string
          example: pgval
        - in: query
          name: status
          type: string
          enum:
            - ENABLED
            - DISABLED
          example: ENABLED
      operationId: retrieveProfilesByPartialName
      responses:
        '200':
          description: Profiles retrieved successfully
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                properties:
                  profiles:
                    type: array
                    items:
                      type: object
                      properties:
                        ref_id:
                          type: string
                          format: uuid
                          example: 2a42f783-ca99-4e3d-a87e-d3325013136c
                        crm_id:
                          type: string
                          example: j41klvu
                          minLength: 3
                          maxLength: 50
                          pattern: '^[0-9a-zA-Z]+$'
                        display_name:
                          type: string
                          pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
                          minLength: 3
                          maxLength: 50
                          example: Crypto Fintech
                        legal_name:
                          type: string
                          example: Crypto Fintech Inc.
                          minLength: 3
                          maxLength: 50
                          pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
                        status:
                          type: string
                          enum:
                            - ENABLED
                            - DISABLED
                          example: ENABLED
                        created_date_time:
                          type: string
                          format: date-time
                          example: '2021-10-22T15:59:05.586Z '
                        updated_date_time:
                          type: string
                          format: date-time
                          example: '2021-10-22T15:59:05.586Z'
                        cognito_client_id:
                          type: string
                          maxLength: 50
                        reason:
                          type: string
                          minLength: 3
                          maxLength: 240
                          pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
                          example: 'Business relationship was suspended '
        '400':
          description: Bad Request
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '404':
          description: Resource not found
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '500':
          description: Unexpected internal system error
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '503':
          description: System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
  /v1/ledger/account:
    post:
      tags:
        - Ledger Account
      operationId: createAccount
      parameters:
        - description: A unique id  (uuid v4) generated for each request
          in: header
          name: x-pg-interaction-id
          schema:
            description: Uniquely identifies the message.
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[a-zA-Z0-9-.]$'
            example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          required: true
          example: 0a0d74f6-49b5-4b0b-b723-48bdfb142117
        - description: A particular point in progression of time defined (GMT)
          in: header
          name: x-pg-interaction-timestamp
          required: true
          schema:
            type: string
            format: date-time
            example: '2020-05-29T17:19:26.951000Z'
        - description: A unique id that identifies the partner
          in: header
          name: x-pg-profile-id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
                - name
                - description
                - monetary_unit
              properties:
                name:
                  type: string
                  example: CryptoFin Peer2Peer Settlement Acct
                  minLength: 3
                  maxLength: 50
                  pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
                description:
                  type: string
                  example: For all peer to peer transfers
                  minLength: 3
                  maxLength: 255
                  pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
                monetary_unit:
                  type: string
                  description: Currency codes as per ISO 4217
                  example: CAD
                  minLength: 3
                  maxLength: 3
                  pattern: '^[A-Z]+$'
                options:
                  type: object
                  properties:
                    overdraft_amount:
                      type: number
                      multipleOf: 0.01
                      minimum: 0
                      maximum: *********.99
                      example: 100000
                    fund_hold_days:
                      type: string
                      minimum: 0
                      maximum: 99
                      pattern: '^[0-9]+$'
                      example: 5
      responses:
        '201':
          description: Account succesfully created
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                properties:
                  ref_id:
                    type: string
                    format: uuid
                    example: 2a42f783-ca99-4e3d-a87e-d3325013136c
                  name:
                    type: string
                    example: CryptoFin Peer2Peer Settlement Acct
                    minLength: 3
                    maxLength: 50
                    pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
                  description:
                    type: string
                    example: For all peer to peer transfers
                    minLength: 3
                    maxLength: 255
                    pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
                  monetary_unit:
                    type: string
                    description: Currency codes as per ISO 4217
                    example: CAD
                    minLength: 3
                    maxLength: 3
                    pattern: '^[A-Z]+$'
                  options:
                    type: object
                    properties:
                      overdraft_amount:
                        type: number
                        multipleOf: 0.01
                        minimum: 0
                        maximum: *********.99
                        example: 100000
                      fund_hold_days:
                        type: string
                        minimum: 0
                        maximum: 99
                        pattern: '^[0-9]+$'
                        example: 5
                  status:
                    type: string
                    enum:
                      - ACTIVE
                      - INACTIVE
                      - SUSPENDED
                    example: ACTIVE
                  created_date_time:
                    type: string
                    format: date-time
                    example: '2021-10-22T15:59:05.586Z '
        '400':
          description: Bad Request
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '404':
          description: Profile not found
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '500':
          description: Unexpected internal system error
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '503':
          description: System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
    get:
      tags:
        - Ledger Account
      parameters:
        - description: A unique id  (uuid v4) generated for each request
          in: header
          name: x-pg-interaction-id
          schema:
            description: Uniquely identifies the message.
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[a-zA-Z0-9-.]$'
            example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          required: true
          example: 0a0d74f6-49b5-4b0b-b723-48bdfb142117
        - description: A particular point in progression of time defined (GMT)
          in: header
          name: x-pg-interaction-timestamp
          required: true
          schema:
            type: string
            format: date-time
            example: '2020-05-29T17:19:26.951000Z'
        - description: A unique id that identifies the partner
          in: header
          name: x-pg-profile-id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
      operationId: retrieveAccounts
      responses:
        '200':
          description: account retrieved successfully
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    accounts:
                      type: array
                      items:
                        type: object
                        properties:
                          ref_Id:
                            type: string
                            format: uuid
                            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
                          name:
                            type: string
                            example: CryptoFin Peer2Peer Settlement Acct
                            minLength: 3
                            maxLength: 50
                            pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
                          status:
                            type: string
                            enum:
                              - ACTIVE
                              - INACTIVE
                              - SUSPENDED
                            example: ACTIVE
                      example:
                        - ref_Id: d6qzvW7
                          name: CryptoFin Peer2Peer Settlement Acct
                          status: ACTIVE
                        - ref_Id: snCz89k
                          name: CryptoFin Bulk Settlement Acct
                          status: ACTIVE
        '400':
          description: Bad Request
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '404':
          description: Profile not found
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '500':
          description: Unexpected internal system error
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '503':
          description: System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
  '/v1/ledger/account/{account_ref_id}':
    get:
      tags:
        - Ledger Account
      parameters:
        - description: A unique id  (uuid v4) generated for each request
          in: header
          name: x-pg-interaction-id
          schema:
            description: Uniquely identifies the message.
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[a-zA-Z0-9-.]$'
            example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          required: true
          example: 0a0d74f6-49b5-4b0b-b723-48bdfb142117
        - description: A particular point in progression of time defined (GMT)
          in: header
          name: x-pg-interaction-timestamp
          required: true
          schema:
            type: string
            format: date-time
            example: '2020-05-29T17:19:26.951000Z'
        - description: A unique id that identifies the partner
          in: header
          name: x-pg-profile-id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
        - description: A unique id
          in: path
          name: account_ref_id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
      operationId: retrieveAccount
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  ref_Id:
                    type: string
                    format: uuid
                    example: 2a42f783-ca99-4e3d-a87e-d3325013136c
                  name:
                    type: string
                    example: CryptoFin Peer2Peer Settlement Acct
                    minLength: 3
                    maxLength: 50
                    pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
                  description:
                    type: string
                    example: For all peer to peer transfers
                    minLength: 3
                    maxLength: 255
                    pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
                  monetary_unit:
                    type: string
                    description: Currency codes as per ISO 4217
                    example: CAD
                    minLength: 3
                    maxLength: 3
                    pattern: '^[A-Z]+$'
                  options:
                    type: object
                    properties:
                      overdraft_amount:
                        type: number
                        multipleOf: 0.01
                        minimum: 0
                        maximum: *********.99
                        example: 100000
                      fund_hold_days:
                        type: string
                        minimum: 0
                        maximum: 99
                        pattern: '^[0-9]+$'
                        example: 5
                  status:
                    type: string
                    enum:
                      - ACTIVE
                      - INACTIVE
                      - SUSPENDED
                    example: ACTIVE
                  created_date_time:
                    type: string
                    format: date-time
                    example: '2021-10-22T15:59:05.586Z '
                  updated_date_time:
                    type: string
                    format: date-time
                    example: '2021-10-22T15:59:05.586Z'
        '400':
          description: Bad Request
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '404':
          description: account not found
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '500':
          description: Unexpected internal system error
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '503':
          description: System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
    put:
      tags:
        - Ledger Account
      operationId: updateAccount
      parameters:
        - description: A unique id  (uuid v4) generated for each request
          in: header
          name: x-pg-interaction-id
          schema:
            description: Uniquely identifies the message.
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[a-zA-Z0-9-.]$'
            example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          required: true
          example: 0a0d74f6-49b5-4b0b-b723-48bdfb142117
        - description: A particular point in progression of time defined (GMT)
          in: header
          name: x-pg-interaction-timestamp
          required: true
          schema:
            type: string
            format: date-time
            example: '2020-05-29T17:19:26.951000Z'
        - description: A unique id that identifies the partner
          in: header
          name: x-pg-profile-id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
        - description: A unique id
          in: path
          name: account_ref_id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
                - name
                - description
                - monetary_unit
              properties:
                name:
                  type: string
                  example: CryptoFin Peer2Peer Settlement Acct
                  minLength: 3
                  maxLength: 50
                  pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
                description:
                  type: string
                  example: For all peer to peer transfers
                  minLength: 3
                  maxLength: 255
                  pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
                monetary_unit:
                  type: string
                  description: Currency codes as per ISO 4217
                  example: CAD
                  minLength: 3
                  maxLength: 3
                  pattern: '^[A-Z]+$'
                options:
                  type: object
                  properties:
                    overdraft_amount:
                      type: number
                      multipleOf: 0.01
                      minimum: 0
                      maximum: *********.99
                      example: 100000
                    fund_hold_days:
                      type: string
                      minimum: 0
                      maximum: 99
                      pattern: '^[0-9]+$'
                      example: 5
        required: true
      responses:
        '200':
          description: updated successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    ref_id:
                      type: string
                      format: uuid
                      example: 2a42f783-ca99-4e3d-a87e-d3325013136c
                    name:
                      type: string
                      example: CryptoFin Peer2Peer Settlement Acct
                      minLength: 3
                      maxLength: 50
                      pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
                    description:
                      type: string
                      example: For all peer to peer transfers
                      minLength: 3
                      maxLength: 255
                      pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
                    monetary_unit:
                      type: string
                      description: Currency codes as per ISO 4217
                      example: CAD
                      minLength: 3
                      maxLength: 3
                      pattern: '^[A-Z]+$'
                    options:
                      type: object
                      properties:
                        overdraft_amount:
                          type: number
                          multipleOf: 0.01
                          minimum: 0
                          maximum: *********.99
                          example: 100000
                        fund_hold_days:
                          type: string
                          minimum: 0
                          maximum: 99
                          pattern: '^[0-9]+$'
                          example: 5
                    status:
                      type: string
                      enum:
                        - ACTIVE
                        - INACTIVE
                        - SUSPENDED
                      example: ACTIVE
                    created_date_time:
                      type: string
                      format: date-time
                      example: '2021-10-22T15:59:05.586Z '
                    updated_date_time:
                      type: string
                      format: date-time
                      example: '2021-10-22T15:59:05.586Z'
        '400':
          description: Bad Request
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '404':
          description: Account not found
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '500':
          description: Unexpected internal system error
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '503':
          description: System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
  '/v1/ledger/account/{account_ref_id}/status':
    patch:
      tags:
        - Ledger Account
      parameters:
        - description: A unique id  (uuid v4) generated for each request
          in: header
          name: x-pg-interaction-id
          schema:
            description: Uniquely identifies the message.
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[a-zA-Z0-9-.]$'
            example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          required: true
          example: 0a0d74f6-49b5-4b0b-b723-48bdfb142117
        - description: A particular point in progression of time defined (GMT)
          in: header
          name: x-pg-interaction-timestamp
          required: true
          schema:
            type: string
            format: date-time
            example: '2020-05-29T17:19:26.951000Z'
        - description: A unique id that identifies the partner
          in: header
          name: x-pg-profile-id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
        - description: A unique id
          in: path
          name: account_ref_id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
      operationId: updateAccountStatus
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
                - status
                - reason
              properties:
                status:
                  type: string
                  enum:
                    - ACTIVE
                    - INACTIVE
                    - SUSPENDED
                  example: ACTIVE
                reason:
                  type: string
                  minLength: 3
                  maxLength: 240
                  pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
                  example: 'Business relationship was suspended '
      responses:
        '204':
          description: Account status updated
        '400':
          description: Bad Request
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '404':
          description: account not found
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '500':
          description: Unexpected internal system error
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '503':
          description: System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
  '/v1/ledger/account/balance_search/{account_ref_id}':
    get:
      tags:
        - Ledger Account
      parameters:
        - description: A unique id  (uuid v4) generated for each request
          in: header
          name: x-pg-interaction-id
          schema:
            description: Uniquely identifies the message.
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[a-zA-Z0-9-.]$'
            example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          required: true
          example: 0a0d74f6-49b5-4b0b-b723-48bdfb142117
        - description: A particular point in progression of time defined (GMT)
          in: header
          name: x-pg-interaction-timestamp
          required: true
          schema:
            type: string
            format: date-time
            example: '2020-05-29T17:19:26.951000Z'
        - description: A unique id that identifies the partner
          in: header
          name: x-pg-profile-id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
        - description: A unique id
          in: path
          name: account_ref_id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
        - in: query
          name: from_date
          description: Start date/time in UTC of the inquiry. Search for transactions created on or after this date (default is 30 days back)
          type: string
          format: date-time
          example: '2021-10-23T15:59:05.586Z'
        - in: query
          name: to_date
          description: End date/time in UTC of the inquiry. Search for transactions created before this date (default is today)
          type: string
          format: date-time
          example: '2021-10-23T15:59:05.586Z'
        - in: query
          name: offset
          description: offset is starting point of retrieve transaction request filter; if offset is not provided it would be defaulted to zero;
          required: false
          schema:
            type: integer
        - in: query
          name: max_items
          description: Maximum number of response items to be returned.
          required: false
          schema:
            type: integer
            default: 25
            maximum: 250
      operationId: retrieve14DaysAccountBalances
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  total_credit_amount:
                    type: number
                    multipleOf: 0.01
                    minimum: 0
                    maximum: 1000000
                    example: 123
                  total_debit_amount:
                    type: number
                    multipleOf: 0.01
                    minimum: 0
                    maximum: 1000000
                    example: 123
                  total_reserve_amount:
                    type: number
                    multipleOf: 0.01
                    minimum: 0
                    maximum: 1000000
                    example: 123
                  total_amount:
                    type: number
                    multipleOf: 0.01
                    minimum: 0
                    maximum: 1000000
                    example: 123
                  total_pending_amount:
                    type: number
                    multipleOf: 0.01
                    minimum: 0
                    maximum: 1000000
                    example: 123
                  effective_on:
                    type: string
                    format: date-time
                    example: '2021-10-23T15:59:05.586Z'
        '400':
          description: Bad Request
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '404':
          description: account not found
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '500':
          description: Unexpected internal system error
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '503':
          description: System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
  '/v1/ledger/account/{account_ref_id}/balance':
    get:
      tags:
        - Ledger Account
      parameters:
        - description: A unique id  (uuid v4) generated for each request
          in: header
          name: x-pg-interaction-id
          schema:
            description: Uniquely identifies the message.
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[a-zA-Z0-9-.]$'
            example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          required: true
          example: 0a0d74f6-49b5-4b0b-b723-48bdfb142117
        - description: A particular point in progression of time defined (GMT)
          in: header
          name: x-pg-interaction-timestamp
          required: true
          schema:
            type: string
            format: date-time
            example: '2020-05-29T17:19:26.951000Z'
        - description: A unique id that identifies the partner
          in: header
          name: x-pg-profile-id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
        - description: A unique id
          in: path
          name: account_ref_id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
      operationId: retrieveAccountBalance
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  available_balance:
                    type: number
                    multipleOf: 0.01
                    minimum: 0
                    example: **********.3
                  account_balance:
                    type: number
                    multipleOf: 0.01
                    minimum: 0
                    example: **********.08
                  fund_hold_amount:
                    type: number
                    multipleOf: 0.01
                    minimum: 0
                    example: 113692.76
                  prefund_reserve_amount:
                    type: number
                    example: 100000.12
                    multipleOf: 0.01
                    minimum: 0
                    maximum: *********
                  overdraft_amount:
                    type: number
                    multipleOf: 0.01
                    minimum: 0
                    maximum: *********.99
                    example: 100000
                  effective_on:
                    type: string
                    format: date-time
                    example: '2021-10-23T15:59:05.586Z'
        '400':
          description: Bad Request
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '404':
          description: account not found
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '500':
          description: Unexpected internal system error
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '503':
          description: System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
  '/v1/ledger/transaction/{instruction_ref_id}':
    get:
      tags:
        - Ledger Transaction
      operationId: retrieveInstruction
      parameters:
        - description: A unique id  (uuid v4) generated for each request
          in: header
          name: x-pg-interaction-id
          schema:
            description: Uniquely identifies the message.
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[a-zA-Z0-9-.]$'
            example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          required: true
          example: 0a0d74f6-49b5-4b0b-b723-48bdfb142117
        - description: A particular point in progression of time defined (GMT)
          in: header
          name: x-pg-interaction-timestamp
          required: true
          schema:
            type: string
            format: date-time
            example: '2020-05-29T17:19:26.951000Z'
        - description: A unique id that identifies the partner
          in: header
          name: x-pg-profile-id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
        - description: A unique id for ledger account of partner
          in: header
          name: x-pg-account-id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
        - description: A unique id for an instruction
          in: path
          name: instruction_ref_id
          required: true
          schema:
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[0-9a-zA-Z-._]+$'
            example: FILEREF001
      responses:
        '200':
          description: Resource successfully retrieved
          content:
            application/json:
              schema:
                allOf:
                  - type: object
                    required:
                      - instruction_ref_id
                      - payment_rail
                      - status
                      - transactions
                      - created_date_time
                    properties:
                      instruction_ref_id:
                        type: string
                        minLength: 3
                        maxLength: 36
                        pattern: '^[0-9a-zA-Z-._]+$'
                        example: FILEREF001
                      payment_rail:
                        type: string
                        enum:
                          - EFT
                          - ETRANSFER
                          - WIRES
                          - VISA
                          - INTERNAL
                        example: EFT
                      totals:
                        type: object
                        description: 'Credit and debit totals (count, amount)'
                        properties:
                          debit_count:
                            type: integer
                            description: Number of debit transactions
                            format: int32
                            minimum: 0
                            maximum: 25
                            example: 1
                          debit_total:
                            type: number
                            description: Total amount of debit transactions
                            multipleOf: 0.01
                            minimum: 0
                            example: 1500.5
                          credit_count:
                            type: integer
                            description: Number of credit transactions
                            format: int32
                            minimum: 0
                            maximum: 25
                            example: 1
                          credit_total:
                            type: number
                            description: Total amount of credit transactions
                            multipleOf: 0.01
                            minimum: 0
                            example: 2500.5
                      status:
                        type: string
                        description: Status of the instruction
                        enum:
                          - FAILED
                          - PENDING
                          - ROLLBACKED
                          - POSTED
                        example: PENDING
                      transactions:
                        type: array
                        minItems: 1
                        maxItems: 25
                        items:
                          type: object
                          required:
                            - transaction_ref_id
                            - payment_category
                            - transaction_flow
                            - amount
                            - monetary_unit
                            - acceptance_date_time
                            - effective_date_time
                            - status
                          properties:
                            transaction_ref_id:
                              type: string
                              minLength: 3
                              maxLength: 36
                              pattern: '^[0-9a-zA-Z-._]+$'
                              example: f9443bba-0443-4b71-a17d-42a8057534c8
                            payment_category:
                              type: string
                              description: Category of the payment associated with the payment rail
                              enum:
                                - DEBIT_PULL
                                - CREDIT_PUSH
                                - SEND_PAYMENT
                                - COMPLETE_PAYMENT
                                - ALIAS_DEPOSIT_PAYMENT
                                - ACCOUNT_NUMBER_DEPOSIT_PAYMENT
                                - FULFILL_REQUEST_FOR_PAYMENT
                                - REVERSAL
                                - CORRECTION
                                - PREFUND_RESERVE
                              example: DEBIT_PULL
                            transaction_flow:
                              type: string
                              enum:
                                - CREDIT
                                - DEBIT
                              example: CREDIT
                            amount:
                              type: number
                              multipleOf: 0.01
                              minimum: 0
                              maximum: 1000000
                              example: 123
                            monetary_unit:
                              type: string
                              description: Currency codes as per ISO 4217
                              example: CAD
                              minLength: 3
                              maxLength: 3
                              pattern: '^[A-Z]+$'
                            acceptance_date_time:
                              type: string
                              format: date-time
                              example: '2021-10-23T15:59:05.586Z'
                            due_date_time:
                              type: string
                              format: date-time
                              example: '2021-10-25T15:59:05.586Z'
                            effective_date_time:
                              type: string
                              format: date-time
                              example: '2021-10-23T15:59:05.586Z'
                            events:
                              type: array
                              items:
                                type: object
                                required:
                                  - event
                                  - created_date_time
                                properties:
                                  event:
                                    type: string
                                    enum:
                                      - FAILED
                                      - PENDING
                                      - POSTED
                                      - ROLLBACKED
                                      - REVERSED
                                    example: PENDING
                                  created_date_time:
                                    type: string
                                    format: date-time
                                    example: '2021-10-22T15:59:05.586Z '
                            status:
                              type: string
                              description: Status of the transaction
                              enum:
                                - FAILED
                                - PENDING
                                - POSTED
                                - ROLLBACKED
                                - REVERSED
                              example: PENDING
                      created_date_time:
                        type: string
                        format: date-time
                        example: '2021-10-22T15:59:05.586Z '
                      updated_date_time:
                        type: string
                        format: date-time
                        example: '2021-10-22T15:59:05.586Z'
                  - example:
                      instruction_ref_id: 32e655ed-e162-4168-a924-dcbac87fb127
                      payment_rail: EFT
                      totals:
                        debit_count: 1
                        debit_total: 1500.5
                        credit_count: 1
                        credit_total: 2500.5
                      status: POSTED
                      transactions:
                        - transaction_ref_id: f9443bba-0443-4b71-a17d-42a8057534c8
                          payment_category: DEBIT_PULL
                          transaction_flow: CREDIT
                          amount: 2500.5
                          monetary_unit: CAD
                          acceptance_date_time: '2021-10-23T15:59:05.586Z'
                          due_date_time: '2021-10-23T15:59:05.615Z'
                          effective_date_time: '2021-10-23T15:59:05.615Z'
                          status: POSTED
                          events:
                            - event: PENDING
                              created_date_time: '2021-10-23T15:59:11.174Z'
                            - event: POSTED
                              created_date_time: '2021-10-23T16:02:06.412Z'
                        - transaction_ref_id: 5a530373-2f3a-455f-a66d-751a01e3b0f7
                          payment_category: CREDIT_PUSH
                          transaction_flow: DEBIT
                          amount: 1500.5
                          monetary_unit: CAD
                          acceptance_date_time: '2021-10-23T15:59:05.586Z'
                          effective_date_time: '2021-10-23T15:59:05.586Z'
                          status: POSTED
                          events:
                            - event: PENDING
                              created_date_time: '2021-10-23T15:59:11.174Z'
                            - event: POSTED
                              created_date_time: '2021-10-23T16:02:06.412Z'
                      created_date_time: '2021-10-23T16:00:51.615Z'
                      updated_date_time: '2021-10-23T16:02:06.412Z'
        '400':
          description: Bad Request -- Validation failure
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '401':
          description: Unauthorized -- Authorization required to access resource
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '404':
          description: 'Resource Not Found -- Any of service account, austomer, account, etc.'
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '500':
          description: Internal Server Error -- Unexpected error
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '503':
          description: Service Unavailable -- System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
  '/v1/ledger/transaction/{instruction_ref_id}/{transaction_ref_id}':
    get:
      tags:
        - Ledger Transaction
      operationId: retrieveTransaction
      parameters:
        - description: A unique id  (uuid v4) generated for each request
          in: header
          name: x-pg-interaction-id
          schema:
            description: Uniquely identifies the message.
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[a-zA-Z0-9-.]$'
            example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          required: true
          example: 0a0d74f6-49b5-4b0b-b723-48bdfb142117
        - description: A particular point in progression of time defined (GMT)
          in: header
          name: x-pg-interaction-timestamp
          required: true
          schema:
            type: string
            format: date-time
            example: '2020-05-29T17:19:26.951000Z'
        - description: A unique id that identifies the partner
          in: header
          name: x-pg-profile-id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
        - description: A unique id for ledger account of partner
          in: header
          name: x-pg-account-id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
        - description: A unique id for an instruction
          in: path
          name: instruction_ref_id
          required: true
          schema:
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[0-9a-zA-Z-._]+$'
            example: FILEREF001
        - description: A unique id for a transaction
          in: path
          name: transaction_ref_id
          required: true
          schema:
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[0-9a-zA-Z-._]+$'
            example: f9443bba-0443-4b71-a17d-42a8057534c8
      responses:
        '200':
          description: Resource successfully retrieved
          content:
            application/json:
              schema:
                allOf:
                  - type: object
                    required:
                      - transaction_ref_id
                      - payment_category
                      - transaction_flow
                      - amount
                      - monetary_unit
                      - acceptance_date_time
                      - effective_date_time
                      - status
                    properties:
                      transaction_ref_id:
                        type: string
                        minLength: 3
                        maxLength: 36
                        pattern: '^[0-9a-zA-Z-._]+$'
                        example: f9443bba-0443-4b71-a17d-42a8057534c8
                      payment_category:
                        type: string
                        description: Category of the payment associated with the payment rail
                        enum:
                          - DEBIT_PULL
                          - CREDIT_PUSH
                          - SEND_PAYMENT
                          - COMPLETE_PAYMENT
                          - ALIAS_DEPOSIT_PAYMENT
                          - ACCOUNT_NUMBER_DEPOSIT_PAYMENT
                          - FULFILL_REQUEST_FOR_PAYMENT
                          - REVERSAL
                          - CORRECTION
                          - PREFUND_RESERVE
                        example: DEBIT_PULL
                      transaction_flow:
                        type: string
                        enum:
                          - CREDIT
                          - DEBIT
                        example: CREDIT
                      amount:
                        type: number
                        multipleOf: 0.01
                        minimum: 0
                        maximum: 1000000
                        example: 123
                      monetary_unit:
                        type: string
                        description: Currency codes as per ISO 4217
                        example: CAD
                        minLength: 3
                        maxLength: 3
                        pattern: '^[A-Z]+$'
                      acceptance_date_time:
                        type: string
                        format: date-time
                        example: '2021-10-23T15:59:05.586Z'
                      due_date_time:
                        type: string
                        format: date-time
                        example: '2021-10-25T15:59:05.586Z'
                      effective_date_time:
                        type: string
                        format: date-time
                        example: '2021-10-23T15:59:05.586Z'
                      events:
                        type: array
                        items:
                          type: object
                          required:
                            - event
                            - created_date_time
                          properties:
                            event:
                              type: string
                              enum:
                                - FAILED
                                - PENDING
                                - POSTED
                                - ROLLBACKED
                                - REVERSED
                              example: PENDING
                            created_date_time:
                              type: string
                              format: date-time
                              example: '2021-10-22T15:59:05.586Z '
                      status:
                        type: string
                        description: Status of the transaction
                        enum:
                          - FAILED
                          - PENDING
                          - POSTED
                          - ROLLBACKED
                          - REVERSED
                        example: PENDING
                  - example:
                      transaction_ref_id: 5a530373-2f3a-455f-a66d-751a01e3b0f7
                      payment_category: CREDIT_PUSH
                      transaction_flow: DEBIT
                      amount: 1500.5
                      monetary_unit: CAD
                      acceptance_date_time: '2021-10-23T15:59:05.586Z'
                      effective_date_time: '2021-10-23T15:59:05.586Z'
                      status: POSTED
                      events:
                        - event: PENDING
                          created_date_time: '2021-10-23T15:59:11.174Z'
                        - event: POSTED
                          created_date_time: '2021-10-23T16:02:06.412Z'
        '400':
          description: Bad Request -- Validation failure
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '401':
          description: Unauthorized -- Authorization required to access resource
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '404':
          description: 'Resource Not Found -- Any of service account, austomer, account, etc.'
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '500':
          description: Internal Server Error -- Unexpected error
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '503':
          description: Service Unavailable -- System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
  '/v1/ledger/transaction/{instruction_ref_id}/{transaction_ref_id}/metadata':
    put:
      tags:
        - Ledger Transaction
      operationId: updateTransactionMetadata
      parameters:
        - description: A unique id  (uuid v4) generated for each request
          in: header
          name: x-pg-interaction-id
          schema:
            description: Uniquely identifies the message.
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[a-zA-Z0-9-.]$'
            example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          required: true
          example: 0a0d74f6-49b5-4b0b-b723-48bdfb142117
        - description: A particular point in progression of time defined (GMT)
          in: header
          name: x-pg-interaction-timestamp
          required: true
          schema:
            type: string
            format: date-time
            example: '2020-05-29T17:19:26.951000Z'
        - description: A unique id that identifies the partner
          in: header
          name: x-pg-profile-id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
        - description: A unique id for ledger account of partner
          in: header
          name: x-pg-account-id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
        - description: A unique id for an instruction
          in: path
          name: instruction_ref_id
          required: true
          schema:
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[0-9a-zA-Z-._]+$'
            example: FILEREF001
        - description: A unique id for a transaction
          in: path
          name: transaction_ref_id
          required: true
          schema:
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[0-9a-zA-Z-._]+$'
            example: f9443bba-0443-4b71-a17d-42a8057534c8
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                ticket_id:
                  type: string
                  maxLength: 25
                ticket_type:
                  type: string
                  enum:
                    - JIRA
                    - SERVICE_NOW
                notes:
                  type: string
                  minLength: 5
                  maxLength: 256
              anyOf:
                - required:
                    - ticket_id
                    - ticket_type
                - required:
                    - notes
        required: true
      responses:
        '201':
          description: Resource successfully created
          content:
            application/json:
              schema:
                type: object
                properties:
                  ticket_id:
                    type: string
                    maxLength: 25
                  ticket_type:
                    type: string
                    enum:
                      - JIRA
                      - SERVICE_NOW
                  notes:
                    type: string
                    minLength: 5
                    maxLength: 256
                anyOf:
                  - required:
                      - ticket_id
                      - ticket_type
                  - required:
                      - notes
        '204':
          description: Resource successfully updated
        '400':
          description: Bad Request -- Validation failure
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '401':
          description: Unauthorized -- Authorization required to access resource
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '404':
          description: Resource Not Found -- Any of instruction or transaction.
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '500':
          description: Internal Server Error -- Unexpected error
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '503':
          description: Service Unavailable -- System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
    get:
      tags:
        - Ledger Transaction
      operationId: getTransactionMetadata
      parameters:
        - description: A unique id  (uuid v4) generated for each request
          in: header
          name: x-pg-interaction-id
          schema:
            description: Uniquely identifies the message.
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[a-zA-Z0-9-.]$'
            example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          required: true
          example: 0a0d74f6-49b5-4b0b-b723-48bdfb142117
        - description: A particular point in progression of time defined (GMT)
          in: header
          name: x-pg-interaction-timestamp
          required: true
          schema:
            type: string
            format: date-time
            example: '2020-05-29T17:19:26.951000Z'
        - description: A unique id that identifies the partner
          in: header
          name: x-pg-profile-id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
        - description: A unique id for ledger account of partner
          in: header
          name: x-pg-account-id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
        - description: A unique id for an instruction
          in: path
          name: instruction_ref_id
          required: true
          schema:
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[0-9a-zA-Z-._]+$'
            example: FILEREF001
        - description: A unique id for a transaction
          in: path
          name: transaction_ref_id
          required: true
          schema:
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[0-9a-zA-Z-._]+$'
            example: f9443bba-0443-4b71-a17d-42a8057534c8
      responses:
        '200':
          description: Resource successfully updated
          content:
            application/json:
              schema:
                allOf:
                  - type: object
                    properties:
                      ticket_id:
                        type: string
                        maxLength: 25
                      ticket_type:
                        type: string
                        enum:
                          - JIRA
                          - SERVICE_NOW
                      notes:
                        type: string
                        minLength: 5
                        maxLength: 256
                    anyOf:
                      - required:
                          - ticket_id
                          - ticket_type
                      - required:
                          - notes
                  - type: object
                    properties:
                      created_date_time:
                        type: string
                        format: date-time
                        example: '2021-10-22T15:59:05.586Z '
                      updated_date_time:
                        type: string
                        format: date-time
                        example: '2021-10-22T15:59:05.586Z'
        '400':
          description: Bad Request -- Validation failure
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '401':
          description: Unauthorized -- Authorization required to access resource
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '404':
          description: Resource Not Found -- Any of instruction or transaction.
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '500':
          description: Internal Server Error -- Unexpected error
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '503':
          description: Service Unavailable -- System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
  /v1/ledger/transaction/internal:
    post:
      tags:
        - Ledger Transaction
      operationId: internalTransaction
      parameters:
        - description: A unique id  (uuid v4) generated for each request
          in: header
          name: x-pg-interaction-id
          schema:
            description: Uniquely identifies the message.
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[a-zA-Z0-9-.]$'
            example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          required: true
          example: 0a0d74f6-49b5-4b0b-b723-48bdfb142117
        - description: A particular point in progression of time defined (GMT)
          in: header
          name: x-pg-interaction-timestamp
          required: true
          schema:
            type: string
            format: date-time
            example: '2020-05-29T17:19:26.951000Z'
        - description: A unique id that identifies the partner
          in: header
          name: x-pg-profile-id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
        - description: A unique id for ledger account of partner
          in: header
          name: x-pg-account-id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
                - instruction_ref_id
                - payment_rail
                - transactions
              properties:
                instruction_ref_id:
                  type: string
                  minLength: 3
                  maxLength: 36
                  pattern: '^[0-9a-zA-Z-._]+$'
                  example: FILEREF001
                payment_rail:
                  type: string
                  enum:
                    - INTERNAL
                  example: INTERNAL
                transactions:
                  type: array
                  minItems: 1
                  maxItems: 25
                  items:
                    type: object
                    required:
                      - transaction_ref_id
                      - payment_category
                      - transaction_flow
                      - amount
                      - monetary_unit
                    properties:
                      transaction_ref_id:
                        type: string
                        minLength: 3
                        maxLength: 36
                        pattern: '^[0-9a-zA-Z-._]+$'
                        example: f9443bba-0443-4b71-a17d-42a8057534c8
                      payment_category:
                        type: string
                        description: Category of the payment associated with the payment rail
                        enum:
                          - PREFUND_RESERVE
                          - CORRECTION
                        example: CORRECTION
                      transaction_flow:
                        type: string
                        enum:
                          - CREDIT
                          - DEBIT
                        example: CREDIT
                      amount:
                        type: number
                        example: 100000.12
                        multipleOf: 0.01
                        minimum: 0
                        maximum: *********
                      monetary_unit:
                        type: string
                        description: Currency codes as per ISO 4217
                        example: CAD
                        minLength: 3
                        maxLength: 3
                        pattern: '^[A-Z]+$'
              example:
                instruction_ref_id: 32e655ed-e162-4168-a924-dcbac87fb127
                payment_rail: INTERNAL
                transactions:
                  - transaction_ref_id: f9443bba-0443-4b71-a17d-42a8057534c8
                    payment_category: CORRECTION
                    amount: 2500.5
                    monetary_unit: CAD
                    acceptance_date_time: '2021-10-23T15:59:05.586Z'
                  - transaction_ref_id: 5a530373-2f3a-455f-a66d-751a01e3b0f7
                    payment_category: PREFUND_RESERVE
                    amount: 1500.5
                    monetary_unit: CAD
                    acceptance_date_time: '2021-10-23T15:59:05.586Z'
        required: true
      responses:
        '201':
          description: Resource successfully created
          content:
            application/json:
              schema:
                type: object
                required:
                  - instruction_ref_id
                  - payment_rail
                  - totals
                  - status
                  - transactions
                  - created_date_time
                properties:
                  instruction_ref_id:
                    type: string
                    minLength: 3
                    maxLength: 36
                    pattern: '^[0-9a-zA-Z-._]+$'
                    example: FILEREF001
                  payment_rail:
                    type: string
                    enum:
                      - INTERNAL
                    example: INTERNAL
                  totals:
                    type: object
                    description: 'Credit and debit totals (count, amount)'
                    properties:
                      debit_count:
                        type: integer
                        description: Number of debit transactions
                        format: int32
                        minimum: 0
                        maximum: 25
                        example: 1
                      debit_total:
                        type: number
                        description: Total amount of debit transactions
                        multipleOf: 0.01
                        minimum: 0
                        example: 1500.5
                      credit_count:
                        type: integer
                        description: Number of credit transactions
                        format: int32
                        minimum: 0
                        maximum: 25
                        example: 1
                      credit_total:
                        type: number
                        description: Total amount of credit transactions
                        multipleOf: 0.01
                        minimum: 0
                        example: 2500.5
                  status:
                    type: string
                    description: Status of the instruction
                    enum:
                      - FAILED
                      - PENDING
                      - ROLLBACKED
                      - POSTED
                    example: PENDING
                  transactions:
                    type: array
                    minItems: 1
                    maxItems: 25
                    items:
                      type: object
                      required:
                        - transaction_ref_id
                        - payment_category
                        - transaction_flow
                        - amount
                        - monetary_unit
                        - acceptance_date_time
                        - effective_date_time
                        - events
                        - status
                      properties:
                        transaction_ref_id:
                          type: string
                          minLength: 3
                          maxLength: 36
                          pattern: '^[0-9a-zA-Z-._]+$'
                          example: f9443bba-0443-4b71-a17d-42a8057534c8
                        payment_category:
                          type: string
                          description: Category of the payment associated with the payment rail
                          enum:
                            - PREFUND_RESERVE
                            - CORRECTION
                          example: CORRECTION
                        transaction_flow:
                          type: string
                          enum:
                            - CREDIT
                            - DEBIT
                          example: CREDIT
                        amount:
                          type: number
                          example: 100000.12
                          multipleOf: 0.01
                          minimum: 0
                          maximum: *********
                        monetary_unit:
                          type: string
                          description: Currency codes as per ISO 4217
                          example: CAD
                          minLength: 3
                          maxLength: 3
                          pattern: '^[A-Z]+$'
                        acceptance_date_time:
                          type: string
                          format: date-time
                          example: '2021-10-23T15:59:05.586Z'
                        effective_date_time:
                          type: string
                          format: date-time
                          example: '2021-10-23T15:59:05.586Z'
                        events:
                          type: array
                          items:
                            type: object
                            required:
                              - event
                              - created_date_time
                            properties:
                              event:
                                type: string
                                enum:
                                  - FAILED
                                  - PENDING
                                  - POSTED
                                  - ROLLBACKED
                                  - REVERSED
                                example: PENDING
                              created_date_time:
                                type: string
                                format: date-time
                                example: '2021-10-22T15:59:05.586Z '
                        status:
                          type: string
                          description: Status of the transaction
                          enum:
                            - FAILED
                            - PENDING
                            - POSTED
                            - ROLLBACKED
                            - REVERSED
                          example: PENDING
                  created_date_time:
                    type: string
                    format: date-time
                    example: '2021-10-22T15:59:05.586Z '
        '400':
          description: Bad Request -- Validation failure
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '401':
          description: Unauthorized -- Authorization required to access resource
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '404':
          description: 'Resource Not Found -- Any of service account, austomer, account, etc.'
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '500':
          description: Internal Server Error -- Unexpected error
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '503':
          description: Service Unavailable -- System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
  /v1/ledger/transaction:
    get:
      tags:
        - Ledger Transaction
      operationId: retrieveTransactionList
      parameters:
        - description: A unique id  (uuid v4) generated for each request
          in: header
          name: x-pg-interaction-id
          schema:
            description: Uniquely identifies the message.
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[a-zA-Z0-9-.]$'
            example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          required: true
          example: 0a0d74f6-49b5-4b0b-b723-48bdfb142117
        - description: A particular point in progression of time defined (GMT)
          in: header
          name: x-pg-interaction-timestamp
          required: true
          schema:
            type: string
            format: date-time
            example: '2020-05-29T17:19:26.951000Z'
        - description: A unique id that identifies the partner
          in: header
          name: x-pg-profile-id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
        - description: A unique id for ledger account of partner
          in: header
          name: x-pg-account-id
          required: true
          schema:
            type: string
            format: uuid
            example: 2a42f783-ca99-4e3d-a87e-d3325013136c
      responses:
        '200':
          description: Resource successfully retrieved
          content:
            application/json:
              schema:
                allOf:
                  - type: object
                    required:
                      - instruction_ref_id
                      - payment_rail
                      - status
                      - transactions
                      - created_date_time
                    properties:
                      instruction_ref_id:
                        type: string
                        minLength: 3
                        maxLength: 36
                        pattern: '^[0-9a-zA-Z-._]+$'
                        example: FILEREF001
                      payment_rail:
                        type: string
                        enum:
                          - EFT
                          - ETRANSFER
                          - WIRES
                          - VISA
                          - INTERNAL
                        example: EFT
                      totals:
                        type: object
                        description: 'Credit and debit totals (count, amount)'
                        properties:
                          debit_count:
                            type: integer
                            description: Number of debit transactions
                            format: int32
                            minimum: 0
                            maximum: 25
                            example: 1
                          debit_total:
                            type: number
                            description: Total amount of debit transactions
                            multipleOf: 0.01
                            minimum: 0
                            example: 1500.5
                          credit_count:
                            type: integer
                            description: Number of credit transactions
                            format: int32
                            minimum: 0
                            maximum: 25
                            example: 1
                          credit_total:
                            type: number
                            description: Total amount of credit transactions
                            multipleOf: 0.01
                            minimum: 0
                            example: 2500.5
                      status:
                        type: string
                        description: Status of the instruction
                        enum:
                          - FAILED
                          - PENDING
                          - ROLLBACKED
                          - POSTED
                        example: PENDING
                      transactions:
                        type: array
                        minItems: 1
                        maxItems: 25
                        items:
                          type: object
                          required:
                            - transaction_ref_id
                            - payment_category
                            - transaction_flow
                            - amount
                            - monetary_unit
                            - acceptance_date_time
                            - effective_date_time
                            - status
                          properties:
                            transaction_ref_id:
                              type: string
                              minLength: 3
                              maxLength: 36
                              pattern: '^[0-9a-zA-Z-._]+$'
                              example: f9443bba-0443-4b71-a17d-42a8057534c8
                            payment_category:
                              type: string
                              description: Category of the payment associated with the payment rail
                              enum:
                                - DEBIT_PULL
                                - CREDIT_PUSH
                                - SEND_PAYMENT
                                - COMPLETE_PAYMENT
                                - ALIAS_DEPOSIT_PAYMENT
                                - ACCOUNT_NUMBER_DEPOSIT_PAYMENT
                                - FULFILL_REQUEST_FOR_PAYMENT
                                - REVERSAL
                                - CORRECTION
                                - PREFUND_RESERVE
                              example: DEBIT_PULL
                            transaction_flow:
                              type: string
                              enum:
                                - CREDIT
                                - DEBIT
                              example: CREDIT
                            amount:
                              type: number
                              multipleOf: 0.01
                              minimum: 0
                              maximum: 1000000
                              example: 123
                            monetary_unit:
                              type: string
                              description: Currency codes as per ISO 4217
                              example: CAD
                              minLength: 3
                              maxLength: 3
                              pattern: '^[A-Z]+$'
                            acceptance_date_time:
                              type: string
                              format: date-time
                              example: '2021-10-23T15:59:05.586Z'
                            due_date_time:
                              type: string
                              format: date-time
                              example: '2021-10-25T15:59:05.586Z'
                            effective_date_time:
                              type: string
                              format: date-time
                              example: '2021-10-23T15:59:05.586Z'
                            events:
                              type: array
                              items:
                                type: object
                                required:
                                  - event
                                  - created_date_time
                                properties:
                                  event:
                                    type: string
                                    enum:
                                      - FAILED
                                      - PENDING
                                      - POSTED
                                      - ROLLBACKED
                                      - REVERSED
                                    example: PENDING
                                  created_date_time:
                                    type: string
                                    format: date-time
                                    example: '2021-10-22T15:59:05.586Z '
                            status:
                              type: string
                              description: Status of the transaction
                              enum:
                                - FAILED
                                - PENDING
                                - POSTED
                                - ROLLBACKED
                                - REVERSED
                              example: PENDING
                      created_date_time:
                        type: string
                        format: date-time
                        example: '2021-10-22T15:59:05.586Z '
                      updated_date_time:
                        type: string
                        format: date-time
                        example: '2021-10-22T15:59:05.586Z'
                  - example:
                      instruction_ref_id: 32e655ed-e162-4168-a924-dcbac87fb127
                      payment_rail: EFT
                      totals:
                        debit_count: 1
                        debit_total: 1500.5
                        credit_count: 1
                        credit_total: 2500.5
                      status: POSTED
                      transactions:
                        - transaction_ref_id: f9443bba-0443-4b71-a17d-42a8057534c8
                          payment_category: DEBIT_PULL
                          transaction_flow: CREDIT
                          amount: 2500.5
                          monetary_unit: CAD
                          acceptance_date_time: '2021-10-23T15:59:05.586Z'
                          due_date_time: '2021-10-23T15:59:05.615Z'
                          effective_date_time: '2021-10-23T15:59:05.615Z'
                          status: POSTED
                          events:
                            - event: PENDING
                              created_date_time: '2021-10-23T15:59:11.174Z'
                            - event: POSTED
                              created_date_time: '2021-10-23T16:02:06.412Z'
                        - transaction_ref_id: 5a530373-2f3a-455f-a66d-751a01e3b0f7
                          payment_category: CREDIT_PUSH
                          transaction_flow: DEBIT
                          amount: 1500.5
                          monetary_unit: CAD
                          acceptance_date_time: '2021-10-23T15:59:05.586Z'
                          effective_date_time: '2021-10-23T15:59:05.586Z'
                          status: POSTED
                          events:
                            - event: PENDING
                              created_date_time: '2021-10-23T15:59:11.174Z'
                            - event: POSTED
                              created_date_time: '2021-10-23T16:02:06.412Z'
                      created_date_time: '2021-10-23T16:00:51.615Z'
                      updated_date_time: '2021-10-23T16:02:06.412Z'
        '400':
          description: Bad Request -- Validation failure
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '401':
          description: Unauthorized -- Authorization required to access resource
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '404':
          description: 'Resource Not Found -- Any of service account, austomer, account, etc.'
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '500':
          description: Internal Server Error -- Unexpected error
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '503':
          description: Service Unavailable -- System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
  '/v1/ledger/transaction/internal/{transaction_ref_id}':
    get:
      tags:
        - Ledger Transaction
      parameters:
        - description: A unique id  (uuid v4) generated for each request
          in: header
          name: x-pg-interaction-id
          schema:
            description: Uniquely identifies the message.
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[a-zA-Z0-9-.]$'
            example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          required: true
          example: 0a0d74f6-49b5-4b0b-b723-48bdfb142117
        - description: A particular point in progression of time defined (GMT)
          in: header
          name: x-pg-interaction-timestamp
          required: true
          schema:
            type: string
            format: date-time
            example: '2020-05-29T17:19:26.951000Z'
        - description: A unique id for a transaction
          in: path
          name: transaction_ref_id
          required: true
          schema:
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[0-9a-zA-Z-._]+$'
            example: f9443bba-0443-4b71-a17d-42a8057534c8
      operationId: getTransaction
      responses:
        '200':
          description: Resource successfully retrieved
          content:
            application/json:
              schema:
                allOf:
                  - type: object
                    required:
                      - transaction_ref_id
                      - payment_category
                      - transaction_flow
                      - amount
                      - monetary_unit
                      - acceptance_date_time
                      - effective_date_time
                      - status
                    properties:
                      transaction_ref_id:
                        type: string
                        minLength: 3
                        maxLength: 36
                        pattern: '^[0-9a-zA-Z-._]+$'
                        example: f9443bba-0443-4b71-a17d-42a8057534c8
                      payment_category:
                        type: string
                        description: Category of the payment associated with the payment rail
                        enum:
                          - DEBIT_PULL
                          - CREDIT_PUSH
                          - SEND_PAYMENT
                          - COMPLETE_PAYMENT
                          - ALIAS_DEPOSIT_PAYMENT
                          - ACCOUNT_NUMBER_DEPOSIT_PAYMENT
                          - FULFILL_REQUEST_FOR_PAYMENT
                          - REVERSAL
                          - CORRECTION
                          - PREFUND_RESERVE
                        example: DEBIT_PULL
                      transaction_flow:
                        type: string
                        enum:
                          - CREDIT
                          - DEBIT
                        example: CREDIT
                      amount:
                        type: number
                        multipleOf: 0.01
                        minimum: 0
                        maximum: 1000000
                        example: 123
                      monetary_unit:
                        type: string
                        description: Currency codes as per ISO 4217
                        example: CAD
                        minLength: 3
                        maxLength: 3
                        pattern: '^[A-Z]+$'
                      acceptance_date_time:
                        type: string
                        format: date-time
                        example: '2021-10-23T15:59:05.586Z'
                      due_date_time:
                        type: string
                        format: date-time
                        example: '2021-10-25T15:59:05.586Z'
                      effective_date_time:
                        type: string
                        format: date-time
                        example: '2021-10-23T15:59:05.586Z'
                      events:
                        type: array
                        items:
                          type: object
                          required:
                            - event
                            - created_date_time
                          properties:
                            event:
                              type: string
                              enum:
                                - FAILED
                                - PENDING
                                - POSTED
                                - ROLLBACKED
                                - REVERSED
                              example: PENDING
                            created_date_time:
                              type: string
                              format: date-time
                              example: '2021-10-22T15:59:05.586Z '
                      status:
                        type: string
                        description: Status of the transaction
                        enum:
                          - FAILED
                          - PENDING
                          - POSTED
                          - ROLLBACKED
                          - REVERSED
                        example: PENDING
                  - example:
                      transaction_ref_id: 5a530373-2f3a-455f-a66d-751a01e3b0f7
                      payment_category: CREDIT_PUSH
                      transaction_flow: DEBIT
                      amount: 1500.5
                      monetary_unit: CAD
                      acceptance_date_time: '2021-10-23T15:59:05.586Z'
                      effective_date_time: '2021-10-23T15:59:05.586Z'
                      status: POSTED
                      events:
                        - event: PENDING
                          created_date_time: '2021-10-23T15:59:11.174Z'
                        - event: POSTED
                          created_date_time: '2021-10-23T16:02:06.412Z'
        '400':
          description: Bad Request -- Validation failure
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '401':
          description: Unauthorized -- Authorization required to access resource
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '404':
          description: 'Resource Not Found -- Any of service account, customer, account, etc.'
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '500':
          description: Internal Server Error -- Unexpected error
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '503':
          description: Service Unavailable -- System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
  /v1/ledger/transaction/search:
    post:
      tags:
        - Ledger Transaction
      operationId: getAllTransactions
      parameters:
        - description: A unique id  (uuid v4) generated for each request
          in: header
          name: x-pg-interaction-id
          schema:
            description: Uniquely identifies the message.
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[a-zA-Z0-9-.]$'
            example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          required: true
          example: 0a0d74f6-49b5-4b0b-b723-48bdfb142117
        - description: A particular point in progression of time defined (GMT)
          in: header
          name: x-pg-interaction-timestamp
          required: true
          schema:
            type: string
            format: date-time
            example: '2020-05-29T17:19:26.951000Z'
        - in: query
          name: profile_id
          schema:
            description: A unique profile id
            in: path
            name: profile_id
            required: true
            schema:
              type: string
              format: uuid
              example: 2a42f783-ca99-4e3d-a87e-d3325013136c
        - in: query
          name: instruction_id
          schema:
            description: A unique id for an instruction
            in: path
            name: instruction_id
            required: true
            schema:
              type: string
              minLength: 3
              maxLength: 36
              pattern: '^[0-9a-zA-Z-._]+$'
              example: FILEREF001
        - in: query
          name: payment_rail
          schema:
            type: string
            enum:
              - EFT
              - ETRANSFER
              - WIRES
              - VISA
              - INTERNAL
            example: EFT
        - in: query
          name: from_date
          description: Start date/time in UTC of the inquiry. Search for transactions created on or after this date (default is 30 days back)
          type: string
          format: date-time
          example: '2021-10-23T15:59:05.586Z'
        - in: query
          name: to_date
          description: End date/time in UTC of the inquiry. Search for transactions created before this date (default is today)
          type: string
          format: date-time
          example: '2021-10-23T15:59:05.586Z'
        - in: query
          name: offset
          description: offset is starting point of retrieve transaction request filter; if offset is not provided it would be defaulted to zero;
          schema:
            type: integer
            minimum: 0
        - in: query
          name: max_items
          description: Maximum number of response items to be returned.
          schema:
            type: integer
            minimum: 1
            maximum: 25
      responses:
        '200':
          description: Resource successfully created
          content:
            application/json:
              schema:
                type: object
                required:
                  - transactions
                properties:
                  transactions:
                    type: array
                    minItems: 1
                    maxItems: 25
                    items:
                      type: object
                      required:
                        - transaction_ref_id
                        - instruction_ref_id
                        - account_ref_id
                        - amount
                        - acceptance_date_time
                      properties:
                        transaction_ref_id:
                          type: string
                          minLength: 3
                          maxLength: 36
                          pattern: '^[0-9a-zA-Z-._]+$'
                          example: f9443bba-0443-4b71-a17d-42a8057534c8
                        instruction_ref_id:
                          type: string
                          minLength: 3
                          maxLength: 36
                          pattern: '^[0-9a-zA-Z-._]+$'
                          example: FILEREF001
                        account_ref_id:
                          type: string
                          format: uuid
                          example: 2a42f783-ca99-4e3d-a87e-d3325013136c
                        amount:
                          type: number
                          multipleOf: 0.01
                          minimum: 0
                          maximum: 1000000
                          example: 123
                        acceptance_date_time:
                          type: string
                          format: date-time
                          example: '2021-10-23T15:59:05.586Z'
                  next_offset:
                    description: 'offset for next page, present only if next page available'
                    type: integer
                    minimum: 1
                    example: 30
                  more_records:
                    type: boolean
        '400':
          description: Bad Request -- Validation failure
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '401':
          description: Unauthorized -- Authorization required to access resource
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '404':
          description: 'Resource Not Found -- Any of service account, customer, account, etc.'
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '500':
          description: Internal Server Error -- Unexpected error
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '503':
          description: Service Unavailable -- System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
  /v1/ledger/transaction/search/instruction:
    post:
      tags:
        - Ledger Transaction
      operationId: getAllInstructions
      parameters:
        - description: A unique id  (uuid v4) generated for each request
          in: header
          name: x-pg-interaction-id
          schema:
            description: Uniquely identifies the message.
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[a-zA-Z0-9-.]$'
            example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          required: true
          example: 0a0d74f6-49b5-4b0b-b723-48bdfb142117
        - description: A particular point in progression of time defined (GMT)
          in: header
          name: x-pg-interaction-timestamp
          required: true
          schema:
            type: string
            format: date-time
            example: '2020-05-29T17:19:26.951000Z'
        - in: query
          name: profile_id
          schema:
            description: A unique profile id
            in: path
            name: profile_id
            required: true
            schema:
              type: string
              format: uuid
              example: 2a42f783-ca99-4e3d-a87e-d3325013136c
          required: true
        - in: query
          name: account_id
          schema:
            description: A unique account id
            in: path
            name: account_id
            required: true
            schema:
              type: string
              format: uuid
              example: 2a42f783-ca99-4e3d-a87e-d3325013136c
          required: true
        - in: query
          name: instruction_id
          schema:
            description: A unique id for an instruction
            in: path
            name: instruction_id
            required: true
            schema:
              type: string
              minLength: 3
              maxLength: 36
              pattern: '^[0-9a-zA-Z-._]+$'
              example: FILEREF001
        - in: query
          name: payment_rail
          schema:
            type: string
            enum:
              - EFT
              - ETRANSFER
              - WIRES
              - VISA
              - INTERNAL
            example: EFT
        - in: query
          name: from_date
          description: Start date/time in UTC of the inquiry. Search for transactions created on or after this date (default is 30 days back)
          type: string
          format: date-time
          example: '2021-10-23T15:59:05.586Z'
        - in: query
          name: to_date
          description: End date/time in UTC of the inquiry. Search for transactions created before this date (default is today)
          type: string
          format: date-time
          example: '2021-10-23T15:59:05.586Z'
        - in: query
          name: offset
          description: offset is starting point of retrieve transaction request filter; if offset is not provided it would be defaulted to zero;
          schema:
            type: integer
            minimum: 0
        - in: query
          name: max_items
          description: Maximum number of response items to be returned.
          schema:
            type: integer
            minimum: 1
            maximum: 25
      responses:
        '200':
          description: Resource successfully created
          content:
            application/json:
              schema:
                type: object
                required:
                  - transactions
                properties:
                  instructions:
                    type: array
                    minItems: 0
                    maxItems: 250
                    items:
                      type: object
                      required:
                        - instruction_ref_id
                        - payment_rail
                        - created_date_time
                        - status
                      properties:
                        instruction_ref_id:
                          type: string
                          minLength: 3
                          maxLength: 36
                          pattern: '^[0-9a-zA-Z-._]+$'
                          example: FILEREF001
                        payment_rail:
                          type: string
                          enum:
                            - EFT
                            - ETRANSFER
                            - WIRES
                            - VISA
                            - INTERNAL
                          example: EFT
                        status:
                          type: string
                          description: Status of the instruction
                          enum:
                            - FAILED
                            - PENDING
                            - ROLLBACKED
                            - POSTED
                          example: PENDING
                        created_date_time:
                          type: string
                          format: date-time
                          example: '2021-10-22T15:59:05.586Z '
                        updated_date_time:
                          type: string
                          format: date-time
                          example: '2021-10-22T15:59:05.586Z'
                  more_records:
                    type: boolean
        '400':
          description: Bad Request -- Validation failure
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '401':
          description: Unauthorized -- Authorization required to access resource
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '404':
          description: 'Resource Not Found -- Any of service account, customer, account, etc.'
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '500':
          description: Internal Server Error -- Unexpected error
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '503':
          description: Service Unavailable -- System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
            x-pg-correspondent-id:
              description: Unique message identifier for the response.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
  /v1/ledger/health/balance/snapshot:
    get:
      tags:
        - Ledger Health
      parameters:
        - in: query
          name: start_time
          description: Start date/time in UTC of the inquiry. Search for transactions created on or after this date (default is 30 days back)
          type: string
          format: date-time
          example: '2021-10-23T15:59:05.586Z'
        - in: query
          name: end_time
          description: End date/time in UTC of the inquiry. Search for transactions created before this date (default is today)
          type: string
          format: date-time
          example: '2021-10-23T15:59:05.586Z'
      operationId: cloudwatchReponse
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                type: object
                required:
                  - status
                properties:
                  status:
                    type: string
                    description: Status of the balance snapshot
                    enum:
                      - SUCCESS
                      - FAILED
                    example: SUCCESS
        '400':
          description: Bad Request
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '404':
          description: account balance not found
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '500':
          description: Unexpected internal system error
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '503':
          description: System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
  /v1/ledger/health/cloudwatch/request:
    get:
      tags:
        - Ledger Health
      parameters:
        - in: query
          name: start_time
          description: Start date/time in UTC of the inquiry. Search for transactions created on or after this date (default is 30 days back)
          type: string
          format: date-time
          example: '2021-10-23T15:59:05.586Z'
        - in: query
          name: end_time
          description: End date/time in UTC of the inquiry. Search for transactions created before this date (default is today)
          type: string
          format: date-time
          example: '2021-10-23T15:59:05.586Z'
      operationId: cloudwatchRequest
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  additionalProperties: true
        '400':
          description: Bad Request
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '404':
          description: account balance not found
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '500':
          description: Unexpected internal system error
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '503':
          description: System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
  /v1/ledger/health/cloudwatch/response:
    get:
      tags:
        - Ledger Health
      parameters:
        - description: A unique id  (uuid v4) generated for each request
          in: header
          name: x-pg-interaction-id
          schema:
            description: Uniquely identifies the message.
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[a-zA-Z0-9-.]$'
            example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          required: true
          example: 0a0d74f6-49b5-4b0b-b723-48bdfb142117
        - description: A particular point in progression of time defined (GMT)
          in: header
          name: x-pg-interaction-timestamp
          required: true
          schema:
            type: string
            format: date-time
            example: '2020-05-29T17:19:26.951000Z'
      operationId: cloudwatchResponse
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  additionalProperties: true
        '400':
          description: Bad Request
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '404':
          description: account balance not found
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '500':
          description: Unexpected internal system error
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '503':
          description: System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
  /v1/ledger/health/cloudwatch/timeout_error:
    get:
      tags:
        - Ledger Health
      parameters:
        - description: A unique id  (uuid v4) generated for each request
          in: header
          name: x-pg-interaction-id
          schema:
            description: Uniquely identifies the message.
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[a-zA-Z0-9-.]$'
            example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          required: true
          example: 0a0d74f6-49b5-4b0b-b723-48bdfb142117
        - description: A particular point in progression of time defined (GMT)
          in: header
          name: x-pg-interaction-timestamp
          required: true
          schema:
            type: string
            format: date-time
            example: '2020-05-29T17:19:26.951000Z'
      operationId: cloudwatchTimeoutError
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  additionalProperties: true
        '400':
          description: Bad Request
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '404':
          description: account balance not found
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '500':
          description: Unexpected internal system error
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '503':
          description: System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
  /v1/ledger/health/cloudwatch/internal_error:
    get:
      tags:
        - Ledger Health
      parameters:
        - description: A unique id  (uuid v4) generated for each request
          in: header
          name: x-pg-interaction-id
          schema:
            description: Uniquely identifies the message.
            type: string
            minLength: 3
            maxLength: 36
            pattern: '^[a-zA-Z0-9-.]$'
            example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          required: true
          example: 0a0d74f6-49b5-4b0b-b723-48bdfb142117
        - description: A particular point in progression of time defined (GMT)
          in: header
          name: x-pg-interaction-timestamp
          required: true
          schema:
            type: string
            format: date-time
            example: '2020-05-29T17:19:26.951000Z'
      operationId: cloudwatchInternalError
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  additionalProperties: true
        '400':
          description: Bad Request
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '404':
          description: account balance not found
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '500':
          description: Unexpected internal system error
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
        '503':
          description: System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              description: A unique Id generated for each request for message tracking purpose.
              schema:
                description: Uniquely identifies the message.
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[a-zA-Z0-9-.]$'
                example: d2c57dd2-27e1-4f84-9f3d-60bb2f701cc4
          content:
            application/json:
              schema:
                type: object
                required:
                  - error
                properties:
                  error:
                    type: array
                    items:
                      type: object
                      required:
                        - code
                      properties:
                        code:
                          type: string
                        additional_information:
                          type: string
components:
  schemas:
    CreateLedgerProfileRequest:
      type: object
      required:
        - crm_id
        - display_name
        - legal_name
      properties:
        crm_id:
          type: string
          example: j41klvu
          minLength: 3
          maxLength: 50
          pattern: '^[0-9a-zA-Z]+$'
        display_name:
          type: string
          pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
          minLength: 3
          maxLength: 50
          example: Crypto Fintech
        legal_name:
          type: string
          example: Crypto Fintech Inc.
          minLength: 3
          maxLength: 50
          pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
        cognito_client_id:
          type: string
          maxLength: 50
    CreateLedgerProfileResponse:
      type: object
      properties:
        ref_id:
          type: string
          format: uuid
          example: 2a42f783-ca99-4e3d-a87e-d3325013136c
        crm_id:
          type: string
          example: j41klvu
          minLength: 3
          maxLength: 50
          pattern: '^[0-9a-zA-Z]+$'
        cognito_client_id:
          type: string
          maxLength: 50
        display_name:
          type: string
          pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
          minLength: 3
          maxLength: 50
          example: Crypto Fintech
        legal_name:
          type: string
          example: Crypto Fintech Inc.
          minLength: 3
          maxLength: 50
          pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
        status:
          type: string
          enum:
            - ENABLED
            - DISABLED
          example: ENABLED
        created_date_time:
          type: string
          format: date-time
          example: '2021-10-22T15:59:05.586Z '
    UpdateLedgerProfileRequest:
      type: object
      required:
        - display_name
        - crm_id
        - legal_name
      properties:
        crm_id:
          type: string
          example: j41klvu
          minLength: 3
          maxLength: 50
          pattern: '^[0-9a-zA-Z]+$'
        cognito_client_id:
          type: string
          maxLength: 50
        display_name:
          type: string
          pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
          minLength: 3
          maxLength: 50
          example: Crypto Fintech
        legal_name:
          type: string
          example: Crypto Fintech Inc.
          minLength: 3
          maxLength: 50
          pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
    UpdateLedgerProfileResponse:
      type: object
      properties:
        ref_id:
          type: string
          format: uuid
          example: 2a42f783-ca99-4e3d-a87e-d3325013136c
        crm_id:
          type: string
          example: j41klvu
          minLength: 3
          maxLength: 50
          pattern: '^[0-9a-zA-Z]+$'
        cognito_client_id:
          type: string
          maxLength: 50
        display_name:
          type: string
          pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
          minLength: 3
          maxLength: 50
          example: Crypto Fintech
        legal_name:
          type: string
          example: Crypto Fintech Inc.
          minLength: 3
          maxLength: 50
          pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
        status:
          type: string
          enum:
            - ENABLED
            - DISABLED
          example: ENABLED
        created_date_time:
          type: string
          format: date-time
          example: '2021-10-22T15:59:05.586Z '
        updated_date_time:
          type: string
          format: date-time
          example: '2021-10-22T15:59:05.586Z'
    RetrieveLedgerProfilesResponse:
      type: object
      properties:
        profiles:
          type: array
          items:
            type: object
            properties:
              ref_id:
                type: string
                format: uuid
                example: 2a42f783-ca99-4e3d-a87e-d3325013136c
              crm_id:
                type: string
                example: j41klvu
                minLength: 3
                maxLength: 50
                pattern: '^[0-9a-zA-Z]+$'
              display_name:
                type: string
                pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
                minLength: 3
                maxLength: 50
                example: Crypto Fintech
              legal_name:
                type: string
                example: Crypto Fintech Inc.
                minLength: 3
                maxLength: 50
                pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
              status:
                type: string
                enum:
                  - ENABLED
                  - DISABLED
                example: ENABLED
              created_date_time:
                type: string
                format: date-time
                example: '2021-10-22T15:59:05.586Z '
              updated_date_time:
                type: string
                format: date-time
                example: '2021-10-22T15:59:05.586Z'
              cognito_client_id:
                type: string
                maxLength: 50
              reason:
                type: string
                minLength: 3
                maxLength: 240
                pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
                example: 'Business relationship was suspended '
    RetrieveLedgerProfileResponse:
      type: object
      properties:
        ref_id:
          type: string
          format: uuid
          example: 2a42f783-ca99-4e3d-a87e-d3325013136c
        crm_id:
          type: string
          example: j41klvu
          minLength: 3
          maxLength: 50
          pattern: '^[0-9a-zA-Z]+$'
        display_name:
          type: string
          pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
          minLength: 3
          maxLength: 50
          example: Crypto Fintech
        legal_name:
          type: string
          example: Crypto Fintech Inc.
          minLength: 3
          maxLength: 50
          pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
        status:
          type: string
          enum:
            - ENABLED
            - DISABLED
          example: ENABLED
        created_date_time:
          type: string
          format: date-time
          example: '2021-10-22T15:59:05.586Z '
        updated_date_time:
          type: string
          format: date-time
          example: '2021-10-22T15:59:05.586Z'
        cognito_client_id:
          type: string
          maxLength: 50
        reason:
          type: string
          minLength: 3
          maxLength: 240
          pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
          example: 'Business relationship was suspended '
    UpdateLedgerProfileStatusRequest:
      type: object
      required:
        - status
        - reason
      properties:
        status:
          type: string
          enum:
            - ENABLED
            - DISABLED
          example: ENABLED
        reason:
          type: string
          minLength: 3
          maxLength: 240
          pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
          example: 'Business relationship was suspended '
    CreateLedgerAccountRequest:
      type: object
      required:
        - name
        - description
        - monetary_unit
      properties:
        name:
          type: string
          example: CryptoFin Peer2Peer Settlement Acct
          minLength: 3
          maxLength: 50
          pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
        description:
          type: string
          example: For all peer to peer transfers
          minLength: 3
          maxLength: 255
          pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
        monetary_unit:
          type: string
          description: Currency codes as per ISO 4217
          example: CAD
          minLength: 3
          maxLength: 3
          pattern: '^[A-Z]+$'
        options:
          type: object
          properties:
            overdraft_amount:
              type: number
              multipleOf: 0.01
              minimum: 0
              maximum: *********.99
              example: 100000
            fund_hold_days:
              type: string
              minimum: 0
              maximum: 99
              pattern: '^[0-9]+$'
              example: 5
    CreateLedgerAccountResponse:
      type: object
      properties:
        ref_id:
          type: string
          format: uuid
          example: 2a42f783-ca99-4e3d-a87e-d3325013136c
        name:
          type: string
          example: CryptoFin Peer2Peer Settlement Acct
          minLength: 3
          maxLength: 50
          pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
        description:
          type: string
          example: For all peer to peer transfers
          minLength: 3
          maxLength: 255
          pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
        monetary_unit:
          type: string
          description: Currency codes as per ISO 4217
          example: CAD
          minLength: 3
          maxLength: 3
          pattern: '^[A-Z]+$'
        options:
          type: object
          properties:
            overdraft_amount:
              type: number
              multipleOf: 0.01
              minimum: 0
              maximum: *********.99
              example: 100000
            fund_hold_days:
              type: string
              minimum: 0
              maximum: 99
              pattern: '^[0-9]+$'
              example: 5
        status:
          type: string
          enum:
            - ACTIVE
            - INACTIVE
            - SUSPENDED
          example: ACTIVE
        created_date_time:
          type: string
          format: date-time
          example: '2021-10-22T15:59:05.586Z '
    RetrieveLedgerAccountsResponse:
      type: object
      properties:
        accounts:
          type: array
          items:
            type: object
            properties:
              ref_Id:
                type: string
                format: uuid
                example: 2a42f783-ca99-4e3d-a87e-d3325013136c
              name:
                type: string
                example: CryptoFin Peer2Peer Settlement Acct
                minLength: 3
                maxLength: 50
                pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
              status:
                type: string
                enum:
                  - ACTIVE
                  - INACTIVE
                  - SUSPENDED
                example: ACTIVE
          example:
            - ref_Id: d6qzvW7
              name: CryptoFin Peer2Peer Settlement Acct
              status: ACTIVE
            - ref_Id: snCz89k
              name: CryptoFin Bulk Settlement Acct
              status: ACTIVE
    RetrieveLedgerAccountResponse:
      type: object
      properties:
        ref_Id:
          type: string
          format: uuid
          example: 2a42f783-ca99-4e3d-a87e-d3325013136c
        name:
          type: string
          example: CryptoFin Peer2Peer Settlement Acct
          minLength: 3
          maxLength: 50
          pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
        description:
          type: string
          example: For all peer to peer transfers
          minLength: 3
          maxLength: 255
          pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
        monetary_unit:
          type: string
          description: Currency codes as per ISO 4217
          example: CAD
          minLength: 3
          maxLength: 3
          pattern: '^[A-Z]+$'
        options:
          type: object
          properties:
            overdraft_amount:
              type: number
              multipleOf: 0.01
              minimum: 0
              maximum: *********.99
              example: 100000
            fund_hold_days:
              type: string
              minimum: 0
              maximum: 99
              pattern: '^[0-9]+$'
              example: 5
        status:
          type: string
          enum:
            - ACTIVE
            - INACTIVE
            - SUSPENDED
          example: ACTIVE
        created_date_time:
          type: string
          format: date-time
          example: '2021-10-22T15:59:05.586Z '
        updated_date_time:
          type: string
          format: date-time
          example: '2021-10-22T15:59:05.586Z'
    UpdateLedgerAccountRequest:
      type: object
      required:
        - name
        - description
        - monetary_unit
      properties:
        name:
          type: string
          example: CryptoFin Peer2Peer Settlement Acct
          minLength: 3
          maxLength: 50
          pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
        description:
          type: string
          example: For all peer to peer transfers
          minLength: 3
          maxLength: 255
          pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
        monetary_unit:
          type: string
          description: Currency codes as per ISO 4217
          example: CAD
          minLength: 3
          maxLength: 3
          pattern: '^[A-Z]+$'
        options:
          type: object
          properties:
            overdraft_amount:
              type: number
              multipleOf: 0.01
              minimum: 0
              maximum: *********.99
              example: 100000
            fund_hold_days:
              type: string
              minimum: 0
              maximum: 99
              pattern: '^[0-9]+$'
              example: 5
    UpdateLedgerAccountResponse:
      type: object
      properties:
        ref_id:
          type: string
          format: uuid
          example: 2a42f783-ca99-4e3d-a87e-d3325013136c
        name:
          type: string
          example: CryptoFin Peer2Peer Settlement Acct
          minLength: 3
          maxLength: 50
          pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
        description:
          type: string
          example: For all peer to peer transfers
          minLength: 3
          maxLength: 255
          pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
        monetary_unit:
          type: string
          description: Currency codes as per ISO 4217
          example: CAD
          minLength: 3
          maxLength: 3
          pattern: '^[A-Z]+$'
        options:
          type: object
          properties:
            overdraft_amount:
              type: number
              multipleOf: 0.01
              minimum: 0
              maximum: *********.99
              example: 100000
            fund_hold_days:
              type: string
              minimum: 0
              maximum: 99
              pattern: '^[0-9]+$'
              example: 5
        status:
          type: string
          enum:
            - ACTIVE
            - INACTIVE
            - SUSPENDED
          example: ACTIVE
        created_date_time:
          type: string
          format: date-time
          example: '2021-10-22T15:59:05.586Z '
        updated_date_time:
          type: string
          format: date-time
          example: '2021-10-22T15:59:05.586Z'
    UpdateLedgerAccountStatusRequest:
      type: object
      required:
        - status
        - reason
      properties:
        status:
          type: string
          enum:
            - ACTIVE
            - INACTIVE
            - SUSPENDED
          example: ACTIVE
        reason:
          type: string
          minLength: 3
          maxLength: 240
          pattern: '^[a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+( [a-zA-ZÀ-Ÿà-ÿ0-9-_,.#''?:]+)*$'
          example: 'Business relationship was suspended '
    RetrieveLedgerAccountBalanceHistoryResponse:
      type: object
      properties:
        total_credit_amount:
          type: number
          multipleOf: 0.01
          minimum: 0
          maximum: 1000000
          example: 123
        total_debit_amount:
          type: number
          multipleOf: 0.01
          minimum: 0
          maximum: 1000000
          example: 123
        total_reserve_amount:
          type: number
          multipleOf: 0.01
          minimum: 0
          maximum: 1000000
          example: 123
        total_amount:
          type: number
          multipleOf: 0.01
          minimum: 0
          maximum: 1000000
          example: 123
        total_pending_amount:
          type: number
          multipleOf: 0.01
          minimum: 0
          maximum: 1000000
          example: 123
        effective_on:
          type: string
          format: date-time
          example: '2021-10-23T15:59:05.586Z'
    RetrieveLedgerAccountBalanceResponse:
      type: object
      properties:
        available_balance:
          type: number
          multipleOf: 0.01
          minimum: 0
          example: **********.3
        account_balance:
          type: number
          multipleOf: 0.01
          minimum: 0
          example: **********.08
        fund_hold_amount:
          type: number
          multipleOf: 0.01
          minimum: 0
          example: 113692.76
        prefund_reserve_amount:
          type: number
          example: 100000.12
          multipleOf: 0.01
          minimum: 0
          maximum: *********
        overdraft_amount:
          type: number
          multipleOf: 0.01
          minimum: 0
          maximum: *********.99
          example: 100000
        effective_on:
          type: string
          format: date-time
          example: '2021-10-23T15:59:05.586Z'
    InitiateLedgerPrefundReserveRequest:
      type: object
      required:
        - instruction_ref_id
        - payment_rail
        - transactions
      properties:
        instruction_ref_id:
          type: string
          minLength: 3
          maxLength: 36
          pattern: '^[0-9a-zA-Z-._]+$'
          example: FILEREF001
        payment_rail:
          type: string
          enum:
            - INTERNAL
          example: INTERNAL
        transactions:
          type: array
          minItems: 1
          maxItems: 25
          items:
            type: object
            required:
              - transaction_ref_id
              - payment_category
              - transaction_flow
              - amount
              - monetary_unit
            properties:
              transaction_ref_id:
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[0-9a-zA-Z-._]+$'
                example: f9443bba-0443-4b71-a17d-42a8057534c8
              payment_category:
                type: string
                description: Category of the payment associated with the payment rail
                enum:
                  - PREFUND_RESERVE
                  - CORRECTION
                example: CORRECTION
              transaction_flow:
                type: string
                enum:
                  - CREDIT
                  - DEBIT
                example: CREDIT
              amount:
                type: number
                example: 100000.12
                multipleOf: 0.01
                minimum: 0
                maximum: *********
              monetary_unit:
                type: string
                description: Currency codes as per ISO 4217
                example: CAD
                minLength: 3
                maxLength: 3
                pattern: '^[A-Z]+$'
      example:
        instruction_ref_id: 32e655ed-e162-4168-a924-dcbac87fb127
        payment_rail: INTERNAL
        transactions:
          - transaction_ref_id: f9443bba-0443-4b71-a17d-42a8057534c8
            payment_category: CORRECTION
            amount: 2500.5
            monetary_unit: CAD
            acceptance_date_time: '2021-10-23T15:59:05.586Z'
          - transaction_ref_id: 5a530373-2f3a-455f-a66d-751a01e3b0f7
            payment_category: PREFUND_RESERVE
            amount: 1500.5
            monetary_unit: CAD
            acceptance_date_time: '2021-10-23T15:59:05.586Z'
    InitiateLedgerPrefundReserveResponse:
      type: object
      required:
        - instruction_ref_id
        - payment_rail
        - totals
        - status
        - transactions
        - created_date_time
      properties:
        instruction_ref_id:
          type: string
          minLength: 3
          maxLength: 36
          pattern: '^[0-9a-zA-Z-._]+$'
          example: FILEREF001
        payment_rail:
          type: string
          enum:
            - INTERNAL
          example: INTERNAL
        totals:
          type: object
          description: 'Credit and debit totals (count, amount)'
          properties:
            debit_count:
              type: integer
              description: Number of debit transactions
              format: int32
              minimum: 0
              maximum: 25
              example: 1
            debit_total:
              type: number
              description: Total amount of debit transactions
              multipleOf: 0.01
              minimum: 0
              example: 1500.5
            credit_count:
              type: integer
              description: Number of credit transactions
              format: int32
              minimum: 0
              maximum: 25
              example: 1
            credit_total:
              type: number
              description: Total amount of credit transactions
              multipleOf: 0.01
              minimum: 0
              example: 2500.5
        status:
          type: string
          description: Status of the instruction
          enum:
            - FAILED
            - PENDING
            - ROLLBACKED
            - POSTED
          example: PENDING
        transactions:
          type: array
          minItems: 1
          maxItems: 25
          items:
            type: object
            required:
              - transaction_ref_id
              - payment_category
              - transaction_flow
              - amount
              - monetary_unit
              - acceptance_date_time
              - effective_date_time
              - events
              - status
            properties:
              transaction_ref_id:
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[0-9a-zA-Z-._]+$'
                example: f9443bba-0443-4b71-a17d-42a8057534c8
              payment_category:
                type: string
                description: Category of the payment associated with the payment rail
                enum:
                  - PREFUND_RESERVE
                  - CORRECTION
                example: CORRECTION
              transaction_flow:
                type: string
                enum:
                  - CREDIT
                  - DEBIT
                example: CREDIT
              amount:
                type: number
                example: 100000.12
                multipleOf: 0.01
                minimum: 0
                maximum: *********
              monetary_unit:
                type: string
                description: Currency codes as per ISO 4217
                example: CAD
                minLength: 3
                maxLength: 3
                pattern: '^[A-Z]+$'
              acceptance_date_time:
                type: string
                format: date-time
                example: '2021-10-23T15:59:05.586Z'
              effective_date_time:
                type: string
                format: date-time
                example: '2021-10-23T15:59:05.586Z'
              events:
                type: array
                items:
                  type: object
                  required:
                    - event
                    - created_date_time
                  properties:
                    event:
                      type: string
                      enum:
                        - FAILED
                        - PENDING
                        - POSTED
                        - ROLLBACKED
                        - REVERSED
                      example: PENDING
                    created_date_time:
                      type: string
                      format: date-time
                      example: '2021-10-22T15:59:05.586Z '
              status:
                type: string
                description: Status of the transaction
                enum:
                  - FAILED
                  - PENDING
                  - POSTED
                  - ROLLBACKED
                  - REVERSED
                example: PENDING
        created_date_time:
          type: string
          format: date-time
          example: '2021-10-22T15:59:05.586Z '
    LedgerRetrieveSearchTransaction:
      type: object
      required:
        - transaction_ref_id
        - instruction_ref_id
        - account_ref_id
        - amount
        - acceptance_date_time
      properties:
        transaction_ref_id:
          type: string
          minLength: 3
          maxLength: 36
          pattern: '^[0-9a-zA-Z-._]+$'
          example: f9443bba-0443-4b71-a17d-42a8057534c8
        instruction_ref_id:
          type: string
          minLength: 3
          maxLength: 36
          pattern: '^[0-9a-zA-Z-._]+$'
          example: FILEREF001
        account_ref_id:
          type: string
          format: uuid
          example: 2a42f783-ca99-4e3d-a87e-d3325013136c
        amount:
          type: number
          multipleOf: 0.01
          minimum: 0
          maximum: 1000000
          example: 123
        acceptance_date_time:
          type: string
          format: date-time
          example: '2021-10-23T15:59:05.586Z'
    RetrieveTransactionsResponse:
      type: object
      required:
        - transactions
      properties:
        transactions:
          type: array
          minItems: 1
          maxItems: 25
          items:
            type: object
            required:
              - transaction_ref_id
              - instruction_ref_id
              - account_ref_id
              - amount
              - acceptance_date_time
            properties:
              transaction_ref_id:
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[0-9a-zA-Z-._]+$'
                example: f9443bba-0443-4b71-a17d-42a8057534c8
              instruction_ref_id:
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[0-9a-zA-Z-._]+$'
                example: FILEREF001
              account_ref_id:
                type: string
                format: uuid
                example: 2a42f783-ca99-4e3d-a87e-d3325013136c
              amount:
                type: number
                multipleOf: 0.01
                minimum: 0
                maximum: 1000000
                example: 123
              acceptance_date_time:
                type: string
                format: date-time
                example: '2021-10-23T15:59:05.586Z'
        next_offset:
          description: 'offset for next page, present only if next page available'
          type: integer
          minimum: 1
          example: 30
        more_records:
          type: boolean
    RetrieveInstructionsResponse:
      type: object
      required:
        - transactions
      properties:
        instructions:
          type: array
          minItems: 0
          maxItems: 250
          items:
            type: object
            required:
              - instruction_ref_id
              - payment_rail
              - created_date_time
              - status
            properties:
              instruction_ref_id:
                type: string
                minLength: 3
                maxLength: 36
                pattern: '^[0-9a-zA-Z-._]+$'
                example: FILEREF001
              payment_rail:
                type: string
                enum:
                  - EFT
                  - ETRANSFER
                  - WIRES
                  - VISA
                  - INTERNAL
                example: EFT
              status:
                type: string
                description: Status of the instruction
                enum:
                  - FAILED
                  - PENDING
                  - ROLLBACKED
                  - POSTED
                example: PENDING
              created_date_time:
                type: string
                format: date-time
                example: '2021-10-22T15:59:05.586Z '
              updated_date_time:
                type: string
                format: date-time
                example: '2021-10-22T15:59:05.586Z'
        more_records:
          type: boolean
    LedgerRetrieveSearchInstruction:
      type: object
      required:
        - instruction_ref_id
        - payment_rail
        - created_date_time
        - status
      properties:
        instruction_ref_id:
          type: string
          minLength: 3
          maxLength: 36
          pattern: '^[0-9a-zA-Z-._]+$'
          example: FILEREF001
        payment_rail:
          type: string
          enum:
            - EFT
            - ETRANSFER
            - WIRES
            - VISA
            - INTERNAL
          example: EFT
        status:
          type: string
          description: Status of the instruction
          enum:
            - FAILED
            - PENDING
            - ROLLBACKED
            - POSTED
          example: PENDING
        created_date_time:
          type: string
          format: date-time
          example: '2021-10-22T15:59:05.586Z '
        updated_date_time:
          type: string
          format: date-time
          example: '2021-10-22T15:59:05.586Z'
    Metadata:
      type: object
      properties:
        ticket_id:
          type: string
          maxLength: 25
        ticket_type:
          type: string
          enum:
            - JIRA
            - SERVICE_NOW
        notes:
          type: string
          minLength: 5
          maxLength: 256
      anyOf:
        - required:
            - ticket_id
            - ticket_type
        - required:
            - notes
    RetrieveMetadata:
      allOf:
        - type: object
          properties:
            ticket_id:
              type: string
              maxLength: 25
            ticket_type:
              type: string
              enum:
                - JIRA
                - SERVICE_NOW
            notes:
              type: string
              minLength: 5
              maxLength: 256
          anyOf:
            - required:
                - ticket_id
                - ticket_type
            - required:
                - notes
        - type: object
          properties:
            created_date_time:
              type: string
              format: date-time
              example: '2021-10-22T15:59:05.586Z '
            updated_date_time:
              type: string
              format: date-time
              example: '2021-10-22T15:59:05.586Z'
    BalanceSnapshotStatus:
      type: string
      description: Status of the balance snapshot
      enum:
        - SUCCESS
        - FAILED
      example: SUCCESS
    HealthResponse:
      type: object
      required:
        - status
      properties:
        status:
          type: string
          description: Status of the balance snapshot
          enum:
            - SUCCESS
            - FAILED
          example: SUCCESS
    CloudwatchResponse:
      type: array
      items:
        type: object
        additionalProperties: true
