#!/bin/bash

LEDGER_VERSION=1.4.0
export API_SPEC="general_limits_${LEDGER_VERSION}"

# cleanup
rm generated.swagger.yaml generated-merged-not-flattened.swagger.yaml

echo "# ######## #"
echo "# Admin UI #"
echo "# ######## #"
export API_NAME="adminui"

export API_SPEC_NAME="${API_SPEC}_${API_NAME}"
export API_OPENAPI_FILE="openapi-${API_NAME}-merge.json"

# cleanup
rm ${API_SPEC_NAME}.yaml ${API_SPEC_NAME}.html

# Generate flattened Swagger API Specification
npx openapi-merge-cli@1.1.29 --config ${API_OPENAPI_FILE}
npx swagger-cli@4.0.4 bundle --outfile generated.swagger.yaml --type yaml --dereference generated-merged-not-flattened.swagger.yaml

# Move over the API Specification
mv generated.swagger.yaml ${API_SPEC_NAME}.yaml

# Generate static HTML pages
npx --verbose redoc-cli bundle ${API_SPEC_NAME}.yaml --options.theme.colors.primary.main=#789c4a
mv redoc-static.html ${API_SPEC_NAME}.html


echo; echo; echo; echo;
echo "# ######## #"
echo "# Internal #"
echo "# ######## #"
export API_NAME="internal"

export API_SPEC_NAME="${API_SPEC}_${API_NAME}"
export API_OPENAPI_FILE="openapi-${API_NAME}-merge.json"

# cleanup
rm ${API_SPEC_NAME}.yaml ${API_SPEC_NAME}.html

# Generate flattened Swagger API Specification
npx openapi-merge-cli@1.1.29 --config ${API_OPENAPI_FILE}
npx swagger-cli@4.0.4 bundle --outfile generated.swagger.yaml --type yaml --dereference generated-merged-not-flattened.swagger.yaml

# Move over the API Specification
mv generated.swagger.yaml ${API_SPEC_NAME}.yaml

# Generate static HTML pages
npx --verbose redoc-cli bundle ${API_SPEC_NAME}.yaml --options.theme.colors.primary.main=#789c4a
mv redoc-static.html ${API_SPEC_NAME}.html


echo; echo; echo; echo;
echo "# ###### #"
echo "# System #"
echo "# ###### #"
export API_NAME="system"

export API_SPEC_NAME="${API_SPEC}_${API_NAME}"
export API_OPENAPI_FILE="openapi-${API_NAME}-merge.json"

# cleanup
rm ${API_SPEC_NAME}.yaml ${API_SPEC_NAME}.html

# Generate flattened Swagger API Specification
npx openapi-merge-cli@1.1.29 --config ${API_OPENAPI_FILE}
npx swagger-cli@4.0.4 bundle --outfile generated.swagger.yaml --type yaml --dereference generated-merged-not-flattened.swagger.yaml

# Move over the API Specification
mv generated.swagger.yaml ${API_SPEC_NAME}.yaml

# Generate static HTML pages
npx --verbose redoc-cli bundle ${API_SPEC_NAME}.yaml --options.theme.colors.primary.main=#789c4a
mv redoc-static.html ${API_SPEC_NAME}.html


echo; echo; echo; echo; echo;
echo "# ######## #"
echo "# External #"
echo "# ######## #"
export API_NAME="external"

export API_SPEC_NAME="${API_SPEC}_${API_NAME}"
export API_OPENAPI_FILE="openapi-${API_NAME}-merge.json"

# cleanup
rm ${API_SPEC_NAME}.yaml ${API_SPEC_NAME}.html

# Generate flattened Swagger API Specification
npx openapi-merge-cli@1.1.29 --config ${API_OPENAPI_FILE}
npx swagger-cli@4.0.4 bundle --outfile generated.swagger.yaml --type yaml --dereference generated-merged-not-flattened.swagger.yaml

# Move over the API Specification
mv generated.swagger.yaml ${API_SPEC_NAME}.yaml

# Generate static HTML pages
npx --verbose redoc-cli bundle ${API_SPEC_NAME}.yaml --options.theme.colors.primary.main=#789c4a
mv redoc-static.html ${API_SPEC_NAME}.html

