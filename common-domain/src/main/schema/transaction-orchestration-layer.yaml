openapi: 3.1.0
info:
  title: Transaction Orchestration Layer API
  description: |
    API specification for a transaction orchestration layer that handles multiple domains 
    (e-transfer, EFT, bill payment, card services) and manages intraday balances.
    
    ## Transaction Types
    
    ### Single-Phase Transactions
    Single-phase transactions are posted immediately upon creation. The funds are either debited or credited
    and become available right away. These transactions go directly to the POSTED status.
    
    ### Dual-Phase Transactions
    Dual-phase transactions involve two steps:
    1. **Initiation**: The transaction is created and funds are reserved but not yet available
    2. **Commitment**: The transaction is committed, making the funds available
    
    ## Transaction Lifecycle Operations
    
    ### Commit
    Applies to dual-phase transactions only. Commits a previously initiated transaction, making the funds
    available. Changes status from INITIATED to POSTED.
    
    ### Rollback
    Applies to dual-phase transactions in INITIATED status only. Cancels a pending transaction before it is
    committed, returning any reserved funds to their original state. Changes status to ROLLED_BACK.
    

  version: 1.0.0
  contact:
    name: TOL Technical Team
servers:
  - url: 'https://tol-stg-api.peoplescloud.io'
    description: Transaction Orchestration Layer Staging Environment
  - url: 'https://tol-api.peoplesgroup.com'
    description: Transaction Orchestration Layer Production Environment

paths:
  /api/v1/transactions:
    get:
      summary: Search transactions
      description: |
        Search for transactions based on query parameters.
        ## Sorting
        By default, transactions are sorted by `initiatedTimestamp` in ascending order (oldest to newest).
      operationId: searchTransactions
      tags:
        - Transactions
      parameters:
        - $ref: '#/components/parameters/InteractionId'
        - $ref: '#/components/parameters/Page'
        - $ref: '#/components/parameters/Size'
        - $ref: '#/components/parameters/Filter'
      responses:
        '200':
          description: Transactions retrieved successfully
          headers:
            X-PG-Interaction-ID:
              $ref: '#/components/headers/InteractionId'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedTransactionResponse'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '500':
          $ref: '#/components/responses/InternalServerError'
        '503':
          $ref: '#/components/responses/ServiceUnavailable'
        '504':
          $ref: '#/components/responses/GatewayTimeout'

    post:
      summary: Initiate a transaction
      description: |
        Initiate a single-phase or dual-phase transaction based on the type specified in the request body.
      operationId: createTransaction
      tags:
        - Transactions
      parameters:
        - $ref: '#/components/parameters/InteractionId'
        - $ref: '#/components/parameters/IdempotencyKey'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Transaction'
      responses:
        '201':
          description: Transaction successfully created
          headers:
            X-PG-Interaction-ID:
              $ref: '#/components/headers/InteractionId'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Transaction'
          links:
            GetTransactionById:
              $ref: '#/components/links/GetTransactionById'
            CommitTransaction:
              $ref: '#/components/links/CommitTransaction'
            RollbackTransaction:
              $ref: '#/components/links/RollbackTransaction'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '500':
          $ref: '#/components/responses/InternalServerError'
        '503':
          $ref: '#/components/responses/ServiceUnavailable'
        '504':
          $ref: '#/components/responses/GatewayTimeout'

  /api/v1/transactions/{transactionId}/commit:
    post:
      summary: Commit a transaction
      description: |
        Commits a dual-phase transaction. Transaction status will be updated from INITIATED to POSTED.
      operationId: commitTransaction
      tags:
        - Transactions
      parameters:
        - $ref: '#/components/parameters/TransactionId'
        - $ref: '#/components/parameters/InteractionId'
        - $ref: '#/components/parameters/IdempotencyKey'
      requestBody:
        required: false
        content:
          application/json:
            schema:
              type: object
      responses:
        '200':
          description: Dual phase transaction has been successfully committed. Transaction status updated from INITIATED to POSTED.
          headers:
            X-PG-Interaction-ID:
              $ref: '#/components/headers/InteractionId'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Transaction'
          links:
            GetTransactionById:
              $ref: '#/components/links/GetTransactionById'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '409':
          $ref: '#/components/responses/ConflictError'
          description: 'Error committing a single phase transaction or a transaction that is already in ROLLED_BACK status.'
        '500':
          $ref: '#/components/responses/InternalServerError'
        '503':
          $ref: '#/components/responses/ServiceUnavailable'
        '504':
          $ref: '#/components/responses/GatewayTimeout'

  /api/v1/transactions/{transactionId}:
    get:
      summary: Get a transaction by ID
      description: Retrieve the details and current status of a specific transaction.
      operationId: getTransaction
      tags:
        - Transactions
      parameters:
        - $ref: '#/components/parameters/TransactionId'
        - $ref: '#/components/parameters/InteractionId'
      responses:
        '200':
          description: Transaction details retrieved successfully
          headers:
            X-PG-Interaction-ID:
              $ref: '#/components/headers/InteractionId'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Transaction'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '500':
          $ref: '#/components/responses/InternalServerError'
        '503':
          $ref: '#/components/responses/ServiceUnavailable'
        '504':
          $ref: '#/components/responses/GatewayTimeout'

  /api/v1/transactions/{transactionId}/rollback:
    post:
      summary: Rollback a transaction
      description: |
        Rollback a dual-phase transaction that has been initiated but not yet committed.
        
        ## Effects on Balances
        When a transaction is rolled back:
        - For DEBIT transactions: Any funds that were reserved (held) are released back to the available balance
        - For CREDIT transactions: Any provisional credits that were applied are removed
        
      operationId: rollbackTransaction
      tags:
        - Transactions
      parameters:
        - $ref: '#/components/parameters/TransactionId'
        - $ref: '#/components/parameters/InteractionId'
        - $ref: '#/components/parameters/IdempotencyKey'
      requestBody:
        required: false
        content:
          application/json:
            schema:
              type: object
      responses:
        '204':
          description: Transaction rolled back successfully.
          headers:
            X-PG-Interaction-ID:
              $ref: '#/components/headers/InteractionId'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '409':
          $ref: '#/components/responses/ConflictError'
          description: 'Error rolling back a transaction that is already in ROLLED_BACK status.'
        '500':
          $ref: '#/components/responses/InternalServerError'
        '503':
          $ref: '#/components/responses/ServiceUnavailable'
        '504':
          $ref: '#/components/responses/GatewayTimeout'

  /api/v1/balances:
    get:
      summary: Get balances of an account
      description: |
        Get balances of an account.
      operationId: getBalances
      tags:
        - Balance
      parameters:
        - $ref: '#/components/parameters/InteractionId'
        - $ref: '#/components/parameters/AccountId'
      responses:
        '200':
          description: Balances retrieved successfully
          headers:
            X-PG-Interaction-ID:
              $ref: '#/components/headers/InteractionId'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Balance'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '500':
          $ref: '#/components/responses/InternalServerError'
        '503':
          $ref: '#/components/responses/ServiceUnavailable'
        '504':
          $ref: '#/components/responses/GatewayTimeout'

components:
  headers:
    InteractionId:
      description: A unique id (uuid v4) generated for each request
      required: true
      schema:
        type: string
        format: uuid
        example: '197aa8d3-0850-4ad8-8b76-07e89ae17304'

    InteractionTimestamp:
      description: A particular point in progression of time defined (UTC)
      required: true
      schema:
        type: string
        format: date-time #YYYY-MM-DDThh:mm:ss.sssZ
        example: '2025-01-11T17:19:26.951000Z'

  parameters:
    InteractionId:
      description: A unique id (uuid v4) generated for each request
      in: header
      name: X-PG-Interaction-ID
      required: true
      schema:
        type: string
        format: uuid
      example: '197aa8d3-0850-4ad8-8b76-07e89ae17304'

    InteractionTimestamp:
      description: A particular point in progression of time defined (GMT)
      in: header
      name: X-PG-Interaction-Timestamp
      required: true
      schema:
        type: string
        format: date-time #YYYY-MM-DDThh:mm:ss.sssZ
        example: '2025-01-11T17:19:26.951000Z'

    IdempotencyKey:
      description: |
        Unique token to ensure that only one resource is created regardless of how many times a request is sent.
        Mandatory for all modifying methods (PUT, POST, PATCH, DELETE).
      in: header
      name: X-PG-Idempotency-Key
      required: true
      schema:
        type: string
        format: uuid
      example: "197aa8d3-0850-4ad8-8b76-07e89ae17304"

    TransactionId:
      name: transactionId
      in: path
      required: true
      schema:
        type: string
        format: uuid
      description: ID of the transaction.

    AccountId:
      name: accountId
      in: query
      required: true
      schema:
        type: string
      description: ID of the account.
      example: "ACC123456789"

    Page:
      name: page
      in: query
      required: true
      description: "Page number for pagination."
      schema:
        type: integer
        minimum: 0
        default: 0
    Size:
      name: size
      in: query
      required: true
      description: "Number of items per page."
      schema:
        type: integer
        minimum: 1
        maximum: 100
        default: 20
    Fields:
      name: fields
      in: query
      required: false
      description: "Comma-separated list of fields to include in the response."
      schema:
        type: string
        example: "accountId,transactionAmount,status"
    Filter:
      name: filter
      in: query
      description: |
        Expression-based filter for transactions. 
        Only supports the below 3:
        - Account ID search : `accountId = '12345'`
        - Date range filtering : `initiatedTimestamp >= '2025-03-01T00:00:00.000000Z' AND initiatedTimestamp <= '2025-03-17T23:59:59.999999Z'`
        - Account ID and date range : `accountId='12345' and initiatedTimestamp>='2025-03-01T00:00:00.000000Z' and initiatedTimestamp<='2025-03-17T23:59:59.999999Z'`
      required: false
      schema:
        type: string
      example: accountId='12345' and initiatedTimestamp>='2025-03-01T00:00:00.000000Z' and initiatedTimestamp<='2025-03-17T23:59:59.999999Z'

  schemas:
    Transaction:
      type: object
      properties:
        id:
          $ref: '#/components/schemas/TransactionId'
          readOnly: true
        sourceDomain:
          $ref: '#/components/schemas/SourceDomain'
        accountId:
          $ref: '#/components/schemas/AccountId'
        transactionAmount:
          $ref: '#/components/schemas/TransactionAmount'
        creditDebitIndicator:
          $ref: '#/components/schemas/CreditDebitIndicator'
        referenceId:
          $ref: '#/components/schemas/ReferenceId'
        status:
          $ref: '#/components/schemas/TransactionStatus'
          readOnly: true
        initiatedTimestamp:
          $ref: '#/components/schemas/InitiatedTimestamp'
          readOnly: true
        postedTimestamp:
          $ref: '#/components/schemas/PostedTimestamp'
          readOnly: true
        endToEndId:
          $ref: '#/components/schemas/EndToEndId'
        type:
          type: string
          enum: [ SINGLE_PHASE, DUAL_PHASE ]
          description: Type of transaction to initiate.
      required:
        # List all required fields for creating a transaction
        - type
        - sourceDomain
        - accountId
        - transactionAmount
        - creditDebitIndicator

    TransactionId:
      type: string
      format: uuid
      description: Unique transaction identifier
      example: "123e4567-e89b-12d3-a456-************"

    TransactionStatus:
      type: string
      description: |
        Status of the transaction in the orchestration layer.

        - INITIATED: Transaction has been successfully initiated in a dual-phase flow. Funds are reserved but not yet available.
        - POSTED: Transaction has been fully processed and committed. For single-phase transactions, this occurs immediately after creation.
                   For dual-phase transactions, this happens after an explicit commit operation.
        - ROLLED_BACK: Dual-phase transaction was canceled before being committed. Any reserved funds were released.
      enum:
        - INITIATED
        - POSTED
        - ROLLED_BACK
      example: "INITIATED"

    PaginatedTransactionResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/Transaction'
        pagination:
          type: object
          properties:
            totalRecords:
              type: integer
              minimum: 0
              example: 1
            totalPages:
              type: integer
              minimum: 0
              example: 1
            currentPage:
              type: integer
              minimum: 1
              example: 1
            pageSize:
              type: integer
              minimum: 0
              example: 1
            nextPage:
              type: string
            prevPage:
              type: string

    InitiatedTimestamp:
      type: string
      format: date-time
      description: Date and Timestamp of when the transaction was initiated
      example: "2025-03-10T23:59:59.999999Z"

    PostedTimestamp:
      type: string
      format: date-time
      description: Date and Timestamp of when the transaction was POSTED or reached the final status i.e ROLLED_BACK
      example: "2025-03-10T23:59:59.999999Z"

    SourceDomain:
      type: string
      description: Payment rail source
      enum:
        - ETRANSFER
        - EFT
        - WIRES
        - CARDS
      example: "EFT"

    CreditDebitIndicator:
      type: string
      enum:
        - CREDIT
        - DEBIT
      description: "Credit debit indicator"

    TransactionAmount:
      type: object
      description: Represents a monetary value with an amount and currency.
      required:
        - amount
        - currency
      properties:
        amount:
          type: string
          description: |
            The transaction amount as a string with two decimal places. 
            The associated credit/debit indicator determines the transaction type.
          pattern: '^\d+\.\d{2}$' # Allows only positive values with exactly two decimal places
          example: "250.75"
        currency:
          type: string
          description: The currency code as per ISO 4217.
          enum:
            - CAD
          example: "CAD"

    MonetaryValue:
          type: object
          description: Represents a monetary value with an amount and currency.
          required:
            - amount
            - currency
          properties:
            amount:
              type: string
              description: |
                The monetary amount as a string with two decimal places. 
                Supports both positive and negative values. 
              pattern: '^-?\d+\.\d{2}$' # Allows both positive and negative values with exactly two decimal places
              example: "-250.75"
            currency:
              type: string
              description: The currency code as per ISO 4217.
              enum:
                - CAD
              example: "CAD"

    EndToEndId:
      type: string
      format: uuid
      description: Unique ID to identify the entire end-to-end call chain involving multiple systems.
      example: "5e7baca2-4981-4228-99a2-33a273de4ff9"

    ReferenceId:
      type: string
      description: External reference ID from the payment rail
      maxLength: 256
      example: "PAY987654321"

    AccountId:
      type: string
      description: Account identifier
      example: "ACC123456789"

    Balance:
      type: object
      description: |
        Represents the different balance types for an account.

        - accountBalance (also known as posted balance): Represents the settled balance including all posted transactions
        - availableBalance: Represents the balance available for immediate use, which may differ from accountBalance
                           due to pending transactions, holds, or authorized amounts

        Note: Both balance types can be negative in overdraft scenarios, which is why MonetaryValue supports negative amounts.
      properties:
        accountId:
          $ref: '#/components/schemas/AccountId'
        committedBalance:
          $ref: '#/components/schemas/MonetaryValue'
          description: The settled balance including all posted transactions. May be negative in overdraft scenarios.
        availableBalance:
          $ref: '#/components/schemas/MonetaryValue'
          description: The balance available for immediate use. May differ from committedBalance due to pending transactions or holds.
      required:
        - accountId

    ErrorResponse:
      type: object
      required:
        - error
      properties:
        error:
          type: object
          required:
            - status
            - code
          properties:
            status:
              type: integer
              example: 400
            code:
              type: string
              example: "ERR-4001"
              description: Application-specific error code.
            details:
              type: array
              items:
                type: object
                properties:
                  target:
                    type: string
                    example: "transactionAmount"
                    description: The specific field that caused the error.
                  message:
                    type: string
                    example: "Value must be greater than zero."
                    description: Detailed error message for the specific field.

  responses:
    BadRequestError:
      description: Bad request - invalid input parameters
      headers:
        X-PG-Interaction-ID:
          $ref: '#/components/headers/InteractionId'
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            status: 400
            code: "ERR-4001"
            details:
              target: "transactionAmount"
              message: "Value must be greater than zero."

    UnauthorizedError:
      description: Unauthorized - authentication required
      headers:
        X-PG-Interaction-ID:
          $ref: '#/components/headers/InteractionId'
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            status: 401
            code: "ERR-4010"
            details:
              message: "Authentication credentials are missing or invalid"

    ForbiddenError:
      description: Forbidden - insufficient permissions
      headers:
        X-PG-Interaction-ID:
          $ref: '#/components/headers/InteractionId'
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            status: 403
            code: "ERR-4030"
            details:
              message: "You do not have permission to access this resource"

    NotFoundError:
      description: Resource not found
      headers:
        X-PG-Interaction-ID:
          $ref: '#/components/headers/InteractionId'
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            status: 404
            code: "ERR-4040"
            details:
              message: "The specified resource could not be found"

    ConflictError:
      description: Resource conflict
      headers:
        X-PG-Interaction-ID:
          $ref: '#/components/headers/InteractionId'
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            status: 409
            code: "ERR-4090"
            details:
              message: "Commit operation not allowed on single phase transaction."

    InternalServerError:
      description: Internal server error
      headers:
        X-PG-Interaction-ID:
          $ref: '#/components/headers/InteractionId'
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            status: 500
            code: "ERR-5000"
            details:
              message: "An unexpected error occurred. Please try again later."

    ServiceUnavailable:
      description: Service Unavailable - System is temporarily unavailable
      headers:
        X-PG-Interaction-ID:
          $ref: '#/components/headers/InteractionId'
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            status: 503
            code: "ERR-5030"
            details:
              message: "The system is undergoing maintenance."

    GatewayTimeout:
      description: Gateway Timeout - The server did not receive a timely response from an upstream service.
      headers:
        X-PG-Interaction-ID:
          $ref: '#/components/headers/InteractionId'
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            status: 504
            code: "ERR-5040"
            details:
              message: "The core banking system did not respond within the expected time."

  links:
    GetTransactionById:
      description: |
        Retrieves the transaction details using the `transactionId`.
      operationRef: '#/'
      parameters:
        transactionId: '$response.body#/id'

    CommitTransaction:
      description: |
        Commits a dual-phase transaction using the `transactionId`.
      operationRef: '#/paths/~1api~1v1~1transactions~1{transactionId}/patch'
      parameters:
        transactionId: '$response.body#/id'

    RollbackTransaction:
      description: |
        Rolls back a dual-phase transaction that has not been committed using the `transactionId`.
      operationRef: '#/paths/~1api~1v1~1transactions~1{transactionId}~1rollback/post'
      parameters:
        transactionId: '$response.body#/id'


security:
  - OAuth2: []
  - ApiKey: []