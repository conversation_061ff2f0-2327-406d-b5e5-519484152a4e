<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>ledger-parent-domain</artifactId>
        <groupId>com.peoplestrust</groupId>
        <version>1.0-SNAPSHOT</version>
        <relativePath>../ledger-parent-domain/pom.xml</relativePath>
    </parent>
    <artifactId>ledger-health-domain</artifactId>
    <name>Ledger::Health::Domain</name>
    <build>
        <plugins>
            <!-- A Maven plugin to support the OpenAPI generator project -->
            <plugin>
                <groupId>org.openapitools</groupId>
                <artifactId>openapi-generator-maven-plugin</artifactId>
                <version>${openapi.generator.version}</version>
                <executions>
                    <execution>
                        <id>profile-adminui</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>${project.basedir}/../common-domain/src/main/schema/health-adminui.yaml</inputSpec>
                            <output>${project.build.directory}</output>
                            <generatorName>java</generatorName>
                            <library>resttemplate</library>
                            <generateApis>false</generateApis>
                            <generateApiTests>false</generateApiTests>
                            <generateApiDocumentation>true</generateApiDocumentation>
                            <generateModels>true</generateModels>
                            <generateModelTests>false</generateModelTests>
                            <generateModelDocumentation>false</generateModelDocumentation>
                            <generateSupportingFiles>false</generateSupportingFiles>
                            <modelPackage>com.peoplestrust.health.domain.model</modelPackage>
                            <addCompileSourceRoot>true</addCompileSourceRoot>
                            <skipValidateSpec>true</skipValidateSpec>
                            <configOptions>
                                <sourceFolder>generated-sources</sourceFolder>
                                <performBeanValidation>true</performBeanValidation>
                                <useBeanValidation>true</useBeanValidation>
                                <dateLibrary>java8</dateLibrary>
				<useJakartaEe>true</useJakartaEe>
                                <interfaceOnly>true</interfaceOnly>
                                <additionalModelTypeAnnotations>@lombok.Builder @lombok.AllArgsConstructor</additionalModelTypeAnnotations>
                            </configOptions>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>