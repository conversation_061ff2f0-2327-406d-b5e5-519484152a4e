package com.peoplestrust.qa.transaction.util.api.v1.service;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.peoplestrust.qa.transaction.util.api.v1.QAUtilApplication;
import com.peoplestrust.transaction.persistence.entity.InstructionEntity;
import com.peoplestrust.transaction.persistence.entity.InstructionStatus;
import com.peoplestrust.transaction.persistence.entity.MonetaryUnit;
import com.peoplestrust.transaction.persistence.entity.PaymentCategoryType;
import com.peoplestrust.transaction.persistence.entity.PaymentRailType;
import com.peoplestrust.transaction.persistence.entity.TransactionEntity;
import com.peoplestrust.transaction.persistence.entity.TransactionFlowType;
import com.peoplestrust.transaction.persistence.entity.TransactionStatus;
import com.peoplestrust.transaction.persistence.repository.write.InstructionRepository;
import com.peoplestrust.transaction.persistence.repository.write.TransactionRepository;
import com.peoplestrust.util.api.common.exception.ResourceNotFoundException;
import com.peoplestrust.util.api.common.util.DateUtils;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import net.bytebuddy.utility.RandomString;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@SpringBootTest
@ActiveProfiles("test")
@ContextConfiguration(classes = QAUtilApplication.class)
@ExtendWith(SpringExtension.class)
@Slf4j
public class RetrieveInstructionAndTransactionServiceTestIT {

  @Autowired
  private TransactionRepository transactionRepository;

  @Autowired
  private InstructionRepository instructionRepository;

  @Autowired
  private QAUtilService qaUtilService;

  private String accountId = UUID.randomUUID().toString();

  private String profileId = UUID.randomUUID().toString();

  @Test
  public void getInstructionTest() throws ResourceNotFoundException {
    InstructionEntity instructionEntity = createInstructionEntity();
    instructionRepository.save(instructionEntity);

    InstructionEntity retrievedInstructionEntity = qaUtilService.getInstruction(instructionEntity.getInstructionRefId());
    assertEquals(retrievedInstructionEntity.getInstructionRefId(), instructionEntity.getInstructionRefId());

    List<TransactionEntity> transactionEntities = createTransactionEntities();

    for (TransactionEntity transactionEntity : transactionEntities) {
      transactionEntity.setInstruction(retrievedInstructionEntity);
      transactionRepository.save(transactionEntity);
    }

    for (TransactionEntity transactionEntity : transactionEntities) {
      TransactionEntity retrievedTransactionEntity = qaUtilService.getTransaction(transactionEntity.getTransactionRefId());
      assertEquals(transactionEntity.getTransactionRefId(), retrievedTransactionEntity.getTransactionRefId());
    }
  }

  private InstructionEntity createInstructionEntity() {
    String instructionId = "TEST-INST" + RandomString.make(7);

    return InstructionEntity.builder()
        .instructionRefId(instructionId)
        .accountRefId(UUID.fromString(accountId))
        .profileRefId(UUID.fromString(profileId))
        .paymentRail(PaymentRailType.EFT)
        .transactions(null)
        .status(InstructionStatus.PENDING)
        .build();
  }

  private List<TransactionEntity> createTransactionEntities() {
    List<TransactionEntity> list = new ArrayList<>();

    TransactionEntity t1 = TransactionEntity.builder()
        .profileRefId(UUID.fromString(profileId))
        .accountRefId(UUID.fromString(accountId))
        .transactionRefId(UUID.randomUUID().toString())
        .transactionFlow(TransactionFlowType.CREDIT)
        .paymentCategory(PaymentCategoryType.DEBIT_PULL)
        .amount(new BigDecimal(100)).monetaryUnit(MonetaryUnit.CAD)
        .status(TransactionStatus.PENDING)
        .acceptanceDateTime(DateUtils.offsetDateTime().toLocalDateTime())
        .effectiveDateTime(DateUtils.offsetDateTime().toLocalDateTime())
        .dueDateTime(DateUtils.offsetDateTime().plusDays(2).toLocalDateTime())
        .build();

    TransactionEntity t2 = TransactionEntity.builder()
        .profileRefId(UUID.fromString(profileId))
        .accountRefId(UUID.fromString(accountId))
        .transactionRefId(UUID.randomUUID().toString())
        .transactionFlow(TransactionFlowType.DEBIT)
        .paymentCategory(PaymentCategoryType.CREDIT_PUSH)
        .amount(new BigDecimal(100))
        .monetaryUnit(MonetaryUnit.CAD)
        .status(TransactionStatus.PENDING)
        .acceptanceDateTime(LocalDateTime.now())
        .effectiveDateTime(LocalDateTime.now())
        .build();

    TransactionEntity t3 = TransactionEntity.builder()
        .profileRefId(UUID.fromString(profileId))
        .accountRefId(UUID.fromString(accountId))
        .transactionRefId(UUID.randomUUID().toString())
        .transactionFlow(TransactionFlowType.CREDIT)
        .paymentCategory(PaymentCategoryType.DEBIT_PULL)
        .amount(new BigDecimal(100))
        .monetaryUnit(MonetaryUnit.CAD)
        .status(TransactionStatus.PENDING)
        .acceptanceDateTime(LocalDateTime.now())
        .effectiveDateTime(LocalDateTime.now())
        .build();

    list.add(t1);
    list.add(t2);
    list.add(t3);
    return list;
  }

  @AfterEach
  public void doCleanUpAfterTest() {
    log.trace("cleanup - start");
    instructionRepository.findByProfileRefIdAndAccountRefId(UUID.fromString(profileId), UUID.fromString(accountId)).stream().
        forEach(e -> instructionRepository.delete(e));
    log.trace("clean up - end");
  }
}
