package com.peoplestrust.qa.transaction.util.api.v1.controller;

import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import com.peoplestrust.qa.transaction.util.api.v1.service.KafkaCleanupService;
import com.peoplestrust.util.api.common.exception.ResourceNotFoundException;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;

@SpringBootTest
@ActiveProfiles("test")
@Slf4j
@AutoConfigureMockMvc
public class KafkaCleanupControllerTest {

  @Autowired
  MockMvc mockMvc;

  @MockBean
  KafkaCleanupService kafkaCleanupService;

  @Test
  void cleanupKafkaQueue_success() throws Exception {
    doNothing().when(kafkaCleanupService).cleanupKafkaQueue("testConsumerGroup");

    mockMvc.perform(post("/v1/util/kafka/cleanup/testConsumerGroup")
            .contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk());
    Mockito.verify(kafkaCleanupService, Mockito.times(1)).cleanupKafkaQueue("testConsumerGroup");
  }

  @Test
  void cleanupKafkaQueue_failure() throws Exception {
    doThrow(new RuntimeException("Internal Server Error")).when(kafkaCleanupService)
        .cleanupKafkaQueue("testConsumerGroup");

    mockMvc.perform(post("/v1/util/kafka/cleanup/testConsumerGroup")
            .contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isInternalServerError());
    Mockito.verify(kafkaCleanupService, Mockito.times(1)).cleanupKafkaQueue("testConsumerGroup");
  }

  @Test
  void cleanupKafkaQueue_consumerGroupNotFound() throws Exception {
    doThrow(new ResourceNotFoundException("Consumer group not found")).when(kafkaCleanupService)
        .cleanupKafkaQueue("testConsumerGroup");
    mockMvc.perform(post("/v1/util/kafka/cleanup/testConsumerGroup")
            .contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isNotFound());
    Mockito.verify(kafkaCleanupService, Mockito.times(1)).cleanupKafkaQueue("testConsumerGroup");
  }
}
