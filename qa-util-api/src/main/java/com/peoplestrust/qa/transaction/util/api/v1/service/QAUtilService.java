package com.peoplestrust.qa.transaction.util.api.v1.service;

import com.peoples.banking.util.logger.annotation.PerfLogger;
import com.peoplestrust.qa.domain.model.PatchLedgerTransactionRequest;
import com.peoplestrust.scheduler.persistence.entity.BalanceEntity;
import com.peoplestrust.scheduler.persistence.repository.write.BalanceRepository;
import com.peoplestrust.transaction.persistence.entity.InstructionEntity;
import com.peoplestrust.transaction.persistence.entity.TransactionEntity;
import com.peoplestrust.transaction.persistence.entity.TransactionMetadataEntity;
import com.peoplestrust.transaction.persistence.repository.write.InstructionRepository;
import com.peoplestrust.transaction.persistence.repository.write.TransactionMetadataRepository;
import com.peoplestrust.transaction.persistence.repository.write.TransactionRepository;
import com.peoplestrust.util.api.common.exception.ExceptionUtil;
import com.peoplestrust.util.api.common.exception.InvalidFieldException;
import com.peoplestrust.util.api.common.exception.ResourceNotFoundException;
import com.peoplestrust.util.api.common.util.DateUtils;
import com.peoplestrust.util.api.common.util.Messages;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import jakarta.validation.ConstraintViolationException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;

/**
 * QA transaction util service
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class QAUtilService {

  private final TransactionRepository transactionRepository;
  private final TransactionMetadataRepository transactionMetadataRepository;
  private final InstructionRepository instructionRepository;
  private final BalanceRepository balanceRepository;
  private final SaveInstructions saveInstructions;

  /**
   * service to retrieve a specific transaction
   *
   * @param transactionRefId transaction reference ID (unique) that identifies the transaction
   * @return TransactionEntity
   */
  @PerfLogger
  public TransactionEntity getTransaction(String transactionRefId) throws ResourceNotFoundException {
    TransactionEntity transactionEntity = transactionRepository.findByTransactionRefId(transactionRefId);
    if (transactionEntity == null) {
      throw new ResourceNotFoundException(Messages.TRANSACTION_NOT_FOUND);
    }
    transactionEntity.setInstruction(null);
    return transactionEntity;
  }

  /**
   * service to retrieve a specific transaction
   *
   * @param transactionRefId transaction reference ID (unique) that identifies the transaction
   * @return TransactionEntity
   */
  @PerfLogger
  public TransactionEntity patchTransaction(
      String transactionRefId,
      PatchLedgerTransactionRequest patchLedgerTransactionRequest
  ) throws ResourceNotFoundException {

    // Validate transaction exists
    TransactionEntity transactionEntity = transactionRepository.findByTransactionRefId(transactionRefId);
    if (transactionEntity == null) {
      throw new ResourceNotFoundException(Messages.TRANSACTION_NOT_FOUND);
    }

    // Validate request
    if (patchLedgerTransactionRequest == null) {
      throw new IllegalArgumentException("Patch request cannot be null.");
    }

    Object effectiveDateTimeObj = patchLedgerTransactionRequest.getEffectiveDateTime();
    Object createdDateTimeObj = patchLedgerTransactionRequest.getCreatedDateTime();

    if (!(effectiveDateTimeObj instanceof String) || !(createdDateTimeObj instanceof String)) {
      throw new IllegalArgumentException("effectiveDateTime and createdDateTime must be valid ISO datetime strings.");
    }

    String effectiveDateTimeStr = ((String) effectiveDateTimeObj).trim();
    String createdDateTimeStr = ((String) createdDateTimeObj).trim();

    if (effectiveDateTimeStr.isEmpty() || createdDateTimeStr.isEmpty()) {
      throw new IllegalArgumentException("Datetime strings cannot be empty.");
    }

    try {
      LocalDateTime effectiveDateTime = parseToLocalDateTime(effectiveDateTimeStr);
      LocalDateTime createdDateTime = parseToLocalDateTime(createdDateTimeStr);

      transactionEntity.setEffectiveDateTime(effectiveDateTime);
      transactionEntity.setCreatedDateTime(createdDateTime);

      transactionRepository.save(transactionEntity);

      log.info("Patched transactionRefId={} with effectiveDateTime={} and createdDateTime={}",
          transactionRefId, effectiveDateTime, createdDateTime);

      return transactionEntity;

    } catch (DateTimeParseException e) {
      throw new IllegalArgumentException("Invalid datetime format. Expected ISO format like '2025-07-31T10:21:10.123Z'", e);
    }
  }


  /**
   * Parses a date-time string into LocalDateTime with flexible ISO handling
   */
  private LocalDateTime parseToLocalDateTime(String dateTimeStr) {
    try {
      // Try OffsetDateTime (handles timezone/Z)
      return OffsetDateTime.parse(dateTimeStr).toLocalDateTime();
    } catch (DateTimeParseException ignore) {}

    try {
      return LocalDateTime.parse(dateTimeStr, DateTimeFormatter.ISO_LOCAL_DATE_TIME);
    } catch (DateTimeParseException ignore) {}

    try {
      return LocalDateTime.parse(dateTimeStr, DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS"));
    } catch (DateTimeParseException ignore) {}

    try {
      return LocalDateTime.parse(dateTimeStr, DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSSSS"));
    } catch (DateTimeParseException ignore) {}

    throw new DateTimeParseException("Unable to parse datetime: " + dateTimeStr, dateTimeStr, 0);
  }


  /**
   * service to retrieve a specific instruction
   *
   * @param instructionRefId instruction reference ID (unique) that identifies the instruction
   * @return
   */
  @PerfLogger
  public InstructionEntity getInstruction(String instructionRefId) throws ResourceNotFoundException {
    InstructionEntity instructionEntity = instructionRepository.findByInstructionRefId(instructionRefId);
    if (instructionEntity == null) {
      throw new ResourceNotFoundException(Messages.INSTRUCTION_NOT_FOUND);
    }
    if (instructionEntity.getTransactions() != null) {
      instructionEntity.getTransactions().forEach(t -> t.setInstruction(null));
    }
    return instructionEntity;
  }

  /**
   * Service to return the latest balance snapshot.
   *
   * @param profileRefId
   * @param accountRefId
   * @return list of BalanceEntity
   * @throws ResourceNotFoundException
   */
  @PerfLogger
  public List<BalanceEntity> getBalance(String profileRefId, String accountRefId) throws ResourceNotFoundException {
    log.debug("retrieving balance snapshots for profile_ref_id={}, account_ref_id={}", profileRefId, accountRefId);

    UUID profileUuid = UUID.fromString(profileRefId);
    UUID accountUuid = UUID.fromString(accountRefId);
    List<BalanceEntity> balanceEntity = balanceRepository.findTop28ByAccountRefIdAndProfileRefIdOrderByCreatedDateTimeDesc(accountUuid, profileUuid);

    log.debug("located {}} (maximum of 10) snapshots", balanceEntity.size());

    if (balanceEntity == null || balanceEntity.isEmpty()) {
      throw new ResourceNotFoundException(Messages.ACCOUNT_NOT_FOUND);
    }

    return balanceEntity;
  }

  /**
   * service to clear cache from redis for a specific key
   *
   * @param profileRefId unique profile reference ID
   * @param accountRefId unique account reference ID
   * @param balanceId    combination of profile reference ID and account reference ID
   * @throws ResourceNotFoundException
   */
  @CacheEvict(value = "RedisBalance", key = "#balanceId")
  public void clearCache(String profileRefId, String accountRefId,
      String balanceId) throws ResourceNotFoundException {

    // profile validation check
    if (null == profileRefId) {
      throw new ResourceNotFoundException(Messages.PROFILE_NOT_FOUND);
    }

    // account validation check
    if (null == accountRefId) {
      throw new ResourceNotFoundException(Messages.ACCOUNT_NOT_FOUND);
    }
  }

  /**
   * service to insert bulk instructions to DataBase
   *
   * @param noOfDays         No of days range to set effective date time
   * @param pastStartDate    From which date you want to set effective date time to go back past
   * @param noOfInstructions no of instructions to create
   * @param noOfTransactions no of transactions to create per instruction
   * @param profileRefId     unique id that identifies profile
   * @param accountRefId     unique id that identifies account
   * @return total of insert Transactions
   * @throws Exception
   */
  @PerfLogger
  public Integer insertBulkTransactions(Integer noOfDays, String pastStartDate, Integer noOfInstructions, Integer noOfTransactions,
      String profileRefId, String accountRefId
  ) throws Exception {
    long totalInstructions = 0;
    OffsetDateTime pastDate = DateUtils.toOffsetDateTime(pastStartDate);
    QADataUtil qaDataUtil = new QADataUtil(noOfDays, pastDate, noOfTransactions, noOfInstructions,
        profileRefId, accountRefId);
    List<InstructionEntity> instructionEntities = null;

    try {
      while (true) {
        // Generate instructions
        instructionEntities = qaDataUtil.getInstructions();

        if (instructionEntities.size() == 0) {
          break;
        }
        saveInstructions.saveTransactions(instructionEntities);
        totalInstructions = totalInstructions + instructionEntities.size();
      }

    } catch (ConstraintViolationException ex) {

      log.warn("constraint violation exception on saving transaction", ExceptionUtil.message(ex));

      throw new InvalidFieldException(ExceptionUtil.message(ex));
    } catch (DataIntegrityViolationException ex) {
      throw new InvalidFieldException(ExceptionUtil.message(ex));
    } catch (Exception ex) {

      log.error("unexpected exception on saving transaction", ex.getMessage());

      throw new Exception(ex.getMessage());
    }
    log.debug("Totally inserted {} instruction(s)", totalInstructions);
    return Math.toIntExact(totalInstructions);
  }

  public TransactionMetadataEntity getTransactionMetadata(String transactionRefId) throws ResourceNotFoundException {
    TransactionEntity transactionEntity = transactionRepository.findByTransactionRefId(transactionRefId);
    if (transactionEntity == null) {
      throw new ResourceNotFoundException(Messages.TRANSACTION_NOT_FOUND);
    }
    Optional<TransactionMetadataEntity> transactionMetadataEntityOpt = this.transactionMetadataRepository.findByTransactionEntity(transactionEntity);
    return transactionMetadataEntityOpt.orElseThrow(() -> new ResourceNotFoundException(Messages.TRANSACTION_METADATA_NOT_FOUND));
  }
}

