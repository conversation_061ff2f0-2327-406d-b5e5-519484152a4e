package com.peoplestrust.qa.transaction.util.api.v1.config;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
@Getter
@Setter
@NoArgsConstructor
public class QAUtilProperty {
  /**
   * property to define kafka server address
   */
  @Value("${kafka.bootstrap-servers}")
  private String kafkaBootstrapAddress;

  /**
   * property to define kafka topic name
   */
  @Value("${kafka.topic}")
  private String kafkaTopic;

  /**
   * property to enable or disable SSL
   */
  @Value("${kafka.ssl.enabled}")
  private boolean sslEnabled;

  /**
   * property to define sasl password
   */
  @Value("${kafka.sasl.jaas.config.password}")
  private String saslJaasConfigPassword;

  /**
   * property to define sasl username
   */
  @Value("${kafka.sasl.jaas.config.username}")
  private String saslJaasConfigUsername;

  /**
   * property to define kafka wait time
   */
  @Value("${kafka.wait_time:10}")
  private long kafkaWaitTime;

  @Value("${kafka-listener.start-kafka-listener.url}")
  private String startKafkaListenerUrl;

  @Value("${kafka-listener.stop-kafka-listener.url}")
  private String stopKafkaListenerUrl;

}
